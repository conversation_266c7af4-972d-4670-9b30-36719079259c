{"name": "digibox-web", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.local next dev", "build:dev": "env-cmd -f .env.dev next build", "build:staging": "env-cmd -f .env.staging next build", "build": "next build", "start:dev": "env-cmd -f .env.dev next start", "start:staging": "env-cmd -f .env.staging next start", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^6.4.12", "@mui/lab": "^6.0.0-beta.10", "@mui/material": "^6.4.12", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-three/csg": "^3.2.0", "@react-three/drei": "^9.114.0", "@react-three/fiber": "^8.17.8", "@react-three/postprocessing": "^2.16.2", "@reduxjs/toolkit": "1.9.5", "@types/three": "^0.168.0", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.5.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "cookies-next": "^4.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "env-cmd": "^10.1.0", "focus-formik-error": "^1.1.0", "formik": "^2.4.5", "formik-error-focus": "^2.0.0", "gsap": "^3.12.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.536.0", "motion": "^12.18.1", "next": "14.1.1", "next-redux-wrapper": "^8.1.0", "next-themes": "^0.3.0", "next-video": "^1.1.0", "nprogress": "^0.2.0", "react": "^18", "react-date-picker": "^11.0.0", "react-datetime-picker": "^6.0.1", "react-day-picker": "^8.9.1", "react-dom": "^18", "react-fast-marquee": "^1.6.4", "react-hook-form": "^7.51.1", "react-redux": "^8.1.3", "react-time-picker": "^7.0.0", "recoil": "^0.7.7", "sonner": "^1.4.41", "styled-components": "^6.1.8", "swiper": "^11.1.0", "swr": "^2.2.4", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "three": "^0.168.0", "usehooks-ts": "^3.1.0", "yup": "^1.4.0", "zod": "^3.22.4", "zustand": "^4.4.3"}, "devDependencies": {"@next/eslint-plugin-next": "^13.5.3", "@types/axios": "^0.14.0", "@types/lodash": "^4.14.199", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^18", "@types/react-dom": "^18", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.7.3", "@typescript-eslint/parser": "^6.7.3", "autoprefixer": "10.4.16", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.1.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "postcss": "^8", "prettier": "^3.0.3", "tailwindcss": "^3.3.0", "typescript": "^5"}}