name: Digibox Web Develop CI

on:
  push:
    branches:
      - develop

env:
  IMAGE_NAME: omadar/digibox-web-dev
  DOCKER_FILE_DIR: ./Dockerfile

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Dashboard to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=schedule
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: ./
          file: ${{ env.DOCKER_FILE_DIR }}
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

      - name: Setup yq
        uses: dcarbone/install-yq-action@v1.1.1
        with:
          version: v4.35.1
          download-compressed: true
          force: false

      - name: Check yq
        run: |
          which yq
          yq --version

      - name: ArgoCD Deployment
        run: |
          echo ${{ steps.meta.outputs.tags }}
          echo ${{ env.IMAGE_NAME }}
          echo ${{ steps.meta.outputs.version }}
          git clone ${{ secrets.GIT_OPS_URL }}
          cd lucablock-ops-kustomize
          yq e -i '.images[0].newName = "${{ env.IMAGE_NAME }}"' digibox/services/web/environments/dev/kustomization.yaml
          yq e -i '.images[0].newTag = "${{ steps.meta.outputs.version }}"' digibox/services/web/environments/dev/kustomization.yaml
          git config --global user.email ${{ secrets.GIT_OPS_ACCESS_EMAIL }}
          git config --global user.name ${{ secrets.GIT_OPS_ACCESS_USERNAME }}
          git add .
          git commit -a -m "update deployment ${{ steps.meta.outputs.tags }}"
          git push

  deployment:
    name: Jira deployment tracking
    runs-on: ubuntu-latest
    needs: build
    environment: develop
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: webhook trigger jira automation
        run: |
          curl -X POST https://automation.atlassian.com/pro/hooks/3e539f7a9067b8a44a7871b1e22e9c499408a0a7 \
          -H "Content-Type: application/json" \
          -d '{"project":"Digibox","service":"web","status":"ON FEATURE"}'

