{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": "./",
    "paths": {
      "@/fonts": ["public/fonts"],
      "@/api/*": ["src/api/*"],
      "@/components/*": ["src/components/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/lib/*": ["src/lib/*"],
      "@/libs/*": ["src/libs/*"],
      "@/layouts/*": ["src/layouts/*"],
      "@/services/*": ["src/services/*"],
      "@/store/*": ["src/store/*"],
      "@/styles/*": ["src/styles/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"],
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "video.d.ts",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
  ],
  "exclude": [
    "node_modules"
  ]
}
