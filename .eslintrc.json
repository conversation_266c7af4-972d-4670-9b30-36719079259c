{
  // Configuration for JavaScript files
  "extends": ["airbnb-base", "plugin:prettier/recommended","next/core-web-vitals"],
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "singleQuote": true
      }
    ]
  },
  "overrides": [
    // Configuration for TypeScript files
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "plugins": ["@typescript-eslint", "unused-imports"],
      "extends": [
        "airbnb-typescript",
        "next/core-web-vitals",
        "plugin:prettier/recommended"
      ],
      "parserOptions": {
        "project": "./tsconfig.json"
      },
      "rules": {
        "@typescript-eslint/naming-convention": "off",
        "@typescript-eslint/no-use-before-define": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-shadow": "off",
        "@typescript-eslint/semi": ["error"],
        "@next/next/no-img-element": "off",
        "array-callback-return": "off",
        "camelcase": "off",
        "class-methods-use-this": "off",
        "consistent-return": "off",
        "global-require": 0,
        "jsx-a11y/anchor-is-valid": "off",
        "func-names": "off",
        "import/no-extraneous-dependencies": "off",
        "import/order": "off",
        "import/no-cycle": "off",
        "import/prefer-default-export": "off",
        "import/no-unresolved": "off",
        "react-hooks/rules-of-hooks": "off",
        "object-shorthand": "off",
        "no-await-in-loop": "off",
        "no-underscore-dangle": "off",
        "no-param-reassign": "off",
        "no-plusplus": "off",
        "no-nested-ternary": "off",
        "no-multi-assign": "off",
        "no-restricted-syntax": "off",
        "no-return-await": "off",
        "no-use-before-define": "off",
        "no-undef": "off",
        "no-unused-expressions": "off",
        "no-console": "off",
        "no-prototype-builtins": "off",
        "no-return-assign": "off",
        "max-classes-per-file": "off",
        "radix": "off",
        "react/display-name": "off",
        "react/prop-types": "off",
        "react/no-unknown-property": "off",
        "react/destructuring-assignment": "off",
        "react/require-default-props": "off",
        "react/jsx-props-no-spreading": "off",
        "react-hooks/exhaustive-deps": "off",
        "react/react-in-jsx-scope": "off",
        "react/no-unescaped-entities": "off",
        "prettier/prettier": [
          "error",
          {
            "singleQuote": true
          }
        ],
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
        "semi": "off"
      }
    }
  ]
}
