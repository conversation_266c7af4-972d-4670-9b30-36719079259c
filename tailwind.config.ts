import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    screens: {
      tablet: '431px',
      desktop: '1013px',
    },
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontFamily: {
        LineSeedSand: ['var(--line-seed-sans-font)'],
      },
      backgroundImage: {
        bg: "url('/background/bg.png')",
        'bg-contact': "url('/background/bg-contact.png')",
        'bg-landing': "url('/background/bg-landing.png')",
        'bg-welcome': "url('/background/bg-welcome.jpg')",
      },
      gridTemplateColumns: {
        repeat: 'repeat(auto-fill, minmax(140px, 1fr))',
        'repeat-1': 'repeat(1, minmax(140px, 1fr))',
        'repeat-2': 'repeat(2, minmax(140px, 1fr))',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      colors: {
        'primary-dark': '#E64300',
        'primary-main': '#FF4F00',
        'primary-light': '#FF6B33',
        'primary-bg': '#FCE8E5',
        'secondary-dark': '#3E32DA',
        'secondary-main': '#605DEC',
        'secondary-light': '#7C7CF0',
        'secondary-bg': '#FAFAFF',
        'success-dark': '#00BA31',
        'success-main': '#00E145',
        'success-light': '#54E763',
        'success-bg': '#EBFEEB',
        'background-paper': '#F5F7F8',
        'gray-800': '#424242',
        'gray-700': '#616161',
        'gray-600': '#757575',
        'gray-500': '#9E9E9E',
        'gray-300': '#E0E0E0',
        'gray-200': '#EEEEEE',
        'gray-100': '#F5F5F5',
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
