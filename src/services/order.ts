import Http from '@/api/Http';

const apiOrder = {
  createOrder: async (data: any) => {
    try {
      const res = await Http.post(`/order`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createOrderPayment: async (data: any) => {
    try {
      const res = await Http.post(`/order/payment`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getAllOrders: async () => {
    try {
      const res = await Http.get(`/order`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getOrderById: async (orderId: string | string[]) => {
    try {
      const res = await Http.get(`/order/${orderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getShippingDetailByOrderId: async (orderItemId: string | string[]) => {
    try {
      const res = await Http.get(`/order/item/shipping/${orderItemId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  cancelOrderById: async (orderId: string, data: any) => {
    try {
      const res = await Http.put(`/order/cancel/${orderId}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteOrderById: async (orderId: string) => {
    try {
      const res = await Http.delete(`/order/${orderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  reQuotationById: async (orderId: string) => {
    try {
      const res = await Http.put(`/order/restore/${orderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  customerConfirmReceived: async (orderItemId: number) => {
    try {
      const res = await Http.put(
        `/order/item/customer-confirm-received/${orderItemId}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiOrder;
