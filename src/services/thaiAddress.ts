import Http from '@/api/Http';

const apiThaiAddress = {
  getZipcode: async (zipcode: number) => {
    try {
      const res = await Http.get(`/thai-address/zipcode-by-zipcode/${zipcode}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getDistrict: async (provinceId: number) => {
    try {
      const res = await Http.get(`/thai-address/district/${provinceId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },

  getSubDistrict: async (districtId: number) => {
    try {
      const res = await Http.get(`/thai-address/sub-district/${districtId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiThaiAddress;
