import Http from '@/api/Http';

const apiDiscount = {
  getUserDiscountList: async (params: any) => {
    try {
      const res = await Http.get(`/discount/list/user-discount`, {
        params: params,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getDiscountCategoryList: async () => {
    try {
      const res = await Http.get(`/discount-category`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getDiscountPage: async (params: any) => {
    try {
      const res = await Http.get(`/discount`, {
        params: params,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  retrieveDiscount: async (data: any) => {
    try {
      const res = await Http.post(
        `/discount/retrieve-discount?discountId=${data.discountId}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getDiscountById: async (id: number) => {
    try {
      const res = await Http.get(`/discount/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiDiscount;
