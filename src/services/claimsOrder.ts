import Http from '@/api/Http';

const apiClaimsOrder = {
  getClaimsOrder: async () => {
    try {
      const res = await Http.get(`/order-claim/orders`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getClaimsOrderShipping: async (id: string | string[]) => {
    try {
      const res = await Http.get(`/order-claim/shipping/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getClaimsOrderByCustomer: async (
    page: number,
    size: number,
    ascending: boolean,
  ) => {
    try {
      const res = await Http.get(
        // `/order-claim/customer?ascending=${ascending}`,
        `/order-claim/customer?page=${page}&size=${size}&ascending=${ascending}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createClaimsOrder: async (data: any) => {
    try {
      const res = await Http.post(`/order-claim`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  uploadFile: async (data: any) => {
    try {
      const res = await Http.post(`/order-claim/file-upload`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  customerConfirmReceived: async (orderItemId: number) => {
    try {
      const res = await Http.put(
        `/order-claim/customer-confirm-received/${orderItemId}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiClaimsOrder;
