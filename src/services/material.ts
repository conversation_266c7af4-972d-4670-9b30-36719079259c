import Http from '@/api/Http';

const apiMaterial = {
  getMaterial: async (category?: number) => {
    try {
      const res = await Http.get(`/material?category=${category}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getMaterialPage: async () => {
    try {
      const url = `/material/page?page=${0}&size=${100}&ascending=${true}`;
      const res = await Http.get(url);
      return res.data;
    } catch (error: any) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiMaterial;
