import Http from '@/api/Http';

const apiSpecialTechnic = {
  getSpecialTechnic: async (category?: number) => {
    try {
      const res = await Http.get(`/special-technic?category=${category}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getDataSpecialTechnic: async () => {
    try {
      const url = `/web/special-technic/page?page=${0}&size=${100}&ascending=${true}`;
      const res = await Http.get(url);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
};

export default apiSpecialTechnic;
