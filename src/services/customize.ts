import Http from '@/api/Http';
import { DataSizeType } from '@/types/customize';

const apiCustomize = {
  findModelSizeId: async (params: DataSizeType) => {
    try {
      const res = await Http.get(`/web/customize/find-model-size`, { params });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  findModelSizeConfigMaterialsByModelSizeId: async (modelSizeId: number) => {
    try {
      const res = await Http.get(
        `/web/printing/model-size-config/material/${modelSizeId}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getModelSizeByModelId: async (modelId: number) => {
    try {
      const res = await Http.get(`/model-size/model/${modelId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiCustomize;
