import Http from '@/api/Http';

const apiProducts = {
  getProducts: async () => {
    try {
      const res = await Http.get(`/product`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getProductList: async (params: any) => {
    try {
      const res = await Http.get(`/product/page`, { params });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getProductCategory: async () => {
    try {
      const res = await Http.get(
        `/product-category/page?page=0&size=100&ascending=true`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiProducts;
