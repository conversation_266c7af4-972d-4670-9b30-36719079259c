import Http from '@/api/Http';

const apiRevision = {
  getRevisionById: async (id: string) => {
    try {
      const res = await Http.get(`/revision/items/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getRevisionItemById: async (id: number | string[]) => {
    try {
      const res = await Http.get(`/revision/item/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createRevision: async (orderId: string) => {
    try {
      const res = await Http.post(`/revision/create/${orderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  confirmRevision: async (id: string | string[], data: any) => {
    try {
      const res = await Http.post(`/revision/confirm/${id}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  editRevision: async (id: string | string[] | undefined, data: any) => {
    try {
      const res = await Http.put(`/revision/item/${id}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  cancelRevision: async (id: string) => {
    try {
      const res = await Http.put(`/revision/cancel/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteRevision: async (id: number) => {
    try {
      const res = await Http.delete(`/revision/item/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiRevision;
