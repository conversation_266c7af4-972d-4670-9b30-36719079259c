import Http from '@/api/Http';

const apiModel = {
  getModel: async () => {
    try {
      const res = await Http.get(`/model`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getModelByProductId: async (productId: string) => {
    try {
      const res = await Http.get(`/model/product?productId=${productId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getModelSizeByModelId: async (modelId: number) => {
    try {
      const res = await Http.get(`/web/model-size/model/${modelId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getPrintingByModelSizeConfigId: async (modelSizeConfigId: number) => {
    try {
      const res = await Http.get(`/printing-config/${modelSizeConfigId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getPrintingConfigById: async (id: number) => {
    try {
      const res = await Http.get(`/web/printing-config/id/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getUnfoldedSizePage: async () => {
    try {
      const res = await Http.get(
        `/unfolded-size/page?page=0&size=100&ascending=true`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiModel;
