import Http from '@/api/Http';
import { CustomizeFormType } from '@/types/customize';

const apiCart = {
  getCart: async () => {
    try {
      const res = await Http.get(`/cart`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getCartItem: async (id: number) => {
    try {
      const res = await Http.get(`/cart/item/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  addCart: async (data: CustomizeFormType) => {
    try {
      const res = await Http.post(`/cart`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  editCart: async (id: number, data: CustomizeFormType) => {
    try {
      const res = await Http.put(`/cart/item/${id}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteCart: async (id: number) => {
    try {
      const res = await Http.delete(`/cart/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  addCustomCart: async (params: { uuid?: string; customizeId: number }) => {
    try {
      const res = await Http.post(
        `/web/cart/add-custom-cart?customizeId=${params.customizeId}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getMyCart: async (uuid?: string) => {
    try {
      const res = await Http.get(
        `/web/cart/my-cart${uuid ? `?uuid=${uuid}` : ''}`,
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteCartById: async (cartId: number) => {
    try {
      const res = await Http.delete(`/web/cart/delete-my-cart/${cartId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getCartById: async (cartId: number) => {
    try {
      const res = await Http.get(`/web/cart/my-cart/${cartId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiCart;
