import Http from '@/api/Http';

import { LoginWithPasswordType } from '@/types/auth';

const apiUser = {
  getProfile: async () => {
    try {
      const res = await Http.get(`/user/me`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getAddress: async (isTax: boolean) => {
    try {
      const res = await Http.get(`/user/address/${isTax}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createAddress: async (data: any) => {
    try {
      const res = await Http.post(`/user/address`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  editAddress: async (id: number, data: any) => {
    try {
      const res = await Http.put(`/user/address/${id}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  login: async (data: LoginWithPasswordType) => {
    try {
      const res = await Http.post(`/user/login`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  register: async (data: LoginWithPasswordType) => {
    try {
      const res = await Http.post(`/user/register`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateProfile: async (data: any) => {
    try {
      const res = await Http.put(`/user/updateProfile`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  logout: async () => {
    try {
      const res = await Http.post(`/user/logout`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  sendEmailForgotPassword: async (email: string) => {
    try {
      const res = await Http.post(`/user/sendEmailForgotPassword`, email);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  resetPassword: async (forgotPayload: any) => {
    try {
      const res = await Http.put(`/user/forgotPassword`, forgotPayload);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updatePassword: async (updatePasswordPayload: any) => {
    try {
      const res = await Http.put(`/user/password`, updatePasswordPayload);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteAddressById: async (addressId: number) => {
    try {
      const res = await Http.delete(`/user/address/${addressId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteAccount: async () => {
    try {
      const res = await Http.delete(`/user/deleteAccount`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiUser;
