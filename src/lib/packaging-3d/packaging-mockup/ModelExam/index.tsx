import React, { Fragment, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { ContactShadows, OrbitControls } from '@react-three/drei';

function Box(props: any) {
  const ref: any = useRef();
  const [hovered, hover] = useState(false);
  const [clicked, click] = useState(false);
  useFrame((_state: any, delta: any) => (ref.current.rotation.x += delta));
  return (
    <Fragment>
      <mesh
        {...props}
        ref={ref}
        scale={clicked ? 1.5 : 1}
        onClick={() => click(!clicked)}
        onPointerOver={(event) => {
          event.stopPropagation();
          hover(true);
        }}
        onPointerOut={() => hover(false)}
      >
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color={hovered ? 'hotpink' : 'orange'} />
      </mesh>
      <ContactShadows
        frames={1}
        blur={1}
        opacity={0.5}
        position={[0, -1.5, 0]}
      />
      <ContactShadows
        frames={1}
        blur={3}
        opacity={0.5}
        color="orange"
        position={[0, -1.5, 0]}
      />
    </Fragment>
  );
}

const ModelExam: React.FC = () => {
  return (
    <Canvas>
      <ambientLight intensity={5} />
      <directionalLight position={[10, 10, 5]} intensity={7.5} />
      <Box position={[-1.2, 0, 0]} />
      <Box position={[1.2, 0, 0]} />
      <OrbitControls />
    </Canvas>
  );
};

export default ModelExam;
