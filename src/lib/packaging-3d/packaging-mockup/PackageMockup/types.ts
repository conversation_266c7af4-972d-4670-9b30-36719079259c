export type ModelInfoType =
  | {
      id: string | null;
      name: string;
      metadata: MetaDataType;
      cropPositions?: {
        in: any[];
        out: any[];
      };
      textures?: {
        in: any[];
        out: any[];
      };
    }
  | any;

export type MetaDataType = {
  width: number;
  height: number;
  length: number;
  thickness: number;
  unit: string;
  initW: number;
  initH: number;
  initL: number;
  initT: number;
};

export const initialModelInfo = {
  id: '',
  name: 'test',
  metadata: {
    width: 20,
    height: 20,
    length: 20,
    thickness: 0.1,
    unit: 'mm',
    initW: 20,
    initH: 20,
    initL: 20,
    initT: 0.1,
  },
  cropPositions: {
    in: [],
    out: [],
  },
  textures: {
    in: [],
    out: [],
  },
};

export type ModelControlsType = {
  is3D: boolean;
  isFreeControl: boolean;
  actionState: number;
  side: number;
  mode: number;
  isHelper: boolean;
  isPlay: boolean;
  isMapControl: boolean;
  zoom: number;
  isDimension: boolean;
  rotation: { x: any; y: any; z: any };
};

export const initialModelControls = {
  is3D: false,
  isFreeControl: false,
  actionState: 0,
  side: 0,
  mode: 0,
  isHelper: false,
  isPlay: false,
  isMapControl: false,
  zoom: 100,
  isDimension: false,
  rotation: { x: 0, y: 0, z: 0 },
};

export type CanvasSizeType = {
  x: number;
  y: number;
};

export const initialCanvasSize = {
  x: 1,
  y: 1,
};

export type FileFormatType = {
  svgBlob: any;
  fileName: string;
};

export const initialFileFormat = {
  svgBlob: '',
  fileName: '',
};
