import React, { Fragment, useEffect, useState } from 'react';
import { Shape } from 'three';
import { Decal, Extrude, useTexture } from '@react-three/drei';

import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import _ from 'lodash';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  color?: string;
  thickness: number;
  pivot?: any;
  curve?: any;
  isLine?: any;
  a?: any;
  x?: any;
  degree?: any;
  textures?: any;
  textureId?: any;
};

const A_Shape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, pivot, curve, isLine, a, x, degree, textures, textureId } =
    props;
  const [offset, setOffset] = useState<any>([0, 0, 0]);
  const [rotate, setRotate] = useState<any>([0, 0, 0]);
  const [outOffset, setOutOffset] = useState<any>([0, 0, 0]);
  const [outRotate, setOutRotate] = useState<any>([0, 0, 0]);
  const [inOffset, setInOffset] = useState<any>([0, 0, 0]);
  const [inRotate, setInRotate] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
      setRotate([0, 0, 0]);
      setOutOffset([0, 0, 0]);
      setOutRotate([0, 0, 0]);
      setInOffset([0, 0, 0]);
      setInRotate([0, 0, 0]);
    } else if (pivot === 'bottom') {
      if (curve === 'right') {
        setOffset([0, a, 0]);
        setRotate([0, 0, 0]);
        setOutOffset([0, -a / 2, -thickness * 0.3]);
        setOutRotate([0, Math.PI, 0]);
        setInOffset([0, -a / 2, thickness * 1.3]);
        setInRotate([0, 0, 0]);
      } else if (curve === 'left') {
        setOffset([0, a, isLine ? 0 : thickness]);
        setRotate([0, Math.PI, 0]);
        setOutOffset([0, -a / 2, thickness + thickness * 0.3]);
        setOutRotate([0, 0, 0]);
        setInOffset([0, -a / 2, 0]);
        setInRotate([0, Math.PI, 0]);
      }
    } else if (pivot === 'top') {
      if (curve === 'right') {
        setOffset([0, -a, isLine ? 0 : thickness]);
        setRotate([Math.PI, 0, 0]);
        setOutOffset([0, -a / 2, thickness + thickness * 0.3]);
        setOutRotate([Math.PI, Math.PI, 0]);
        setInOffset([0, -a / 2, 0]);
        setInRotate([Math.PI, 0, 0]);
      } else if (curve === 'left') {
        setOffset([0, -a, 0]);
        setRotate([Math.PI, Math.PI, 0]);
        setOutOffset([0, -a / 2, -thickness * 0.3]);
        setOutRotate([Math.PI, 0, 0]);
        setInOffset([0, -a / 2, thickness * 1.3]);
        setInRotate([Math.PI, Math.PI, 0]);
      }
    }
  }, [pivot, thickness, a, curve, isLine]);
  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const shape: any = new Shape();
  const gapX: any = x * 0.03;
  const gapY: any = x * 0.03;
  const w: number = degreeToWidth(degree, a - gapY);
  const Lx: number = -x / 2 + gapX;
  const Ly: number = -a + gapY;
  shape.moveTo(-x / 2, -a);
  shape.lineTo(Lx, Ly);
  shape.lineTo(Lx + w, 0);
  shape.lineTo(x / 2 - x / 2, 0);
  shape.bezierCurveTo(
    x / 2 - x * 0.1,
    0,
    x / 2 - x * 0.1,
    0,
    x / 2 - a * 0.025,
    -a + a * 0.075,
  );
  shape.lineTo(x / 2 - a * 0.025, -a + a * 0.075);
  shape.lineTo(x / 2, -a + a * 0.05);
  shape.lineTo(x / 2, -a);

  return (
    <group>
      {!isLine ? (
        <Fragment>
          <Extrude
            castShadow
            position={offset}
            rotation={rotate}
            args={[shape, extrudeSettings({ isLine, thickness })]}
          >
            <Decal
              // debug
              position={outOffset}
              rotation={outRotate}
              scale={[x, a, thickness]}
              map={getTextureOut(textureId) ? texture_out : null}
            >
              <OutSideMaterial />
            </Decal>
            {/* inside */}
            <Decal
              // debug
              position={inOffset}
              rotation={inRotate}
              scale={[x, a, thickness]}
              map={getTextureIn(textureId) ? texture_in : null}
            >
              <InSideMaterial isTexture={getTextureIn(textureId)} />
            </Decal>
            <BaseMaterial />
          </Extrude>
        </Fragment>
      ) : (
        <mesh castShadow position={offset} rotation={rotate}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default A_Shape;
