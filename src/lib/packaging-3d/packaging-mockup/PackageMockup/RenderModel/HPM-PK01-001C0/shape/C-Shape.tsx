import { Decal, Extrude, useTexture } from '@react-three/drei';
import React, { Fragment, useEffect, useState } from 'react';
import { Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import _ from 'lodash';

type PropsType = {
  color?: string;
  thickness: number;
  x: number;
  c: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const C_Shape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, c, pivot, isLine, textures, textureId } = props;
  const gapX: any = x * 0.025;
  const gapY: any = x * 0.005;
  const spacing: any = c * 0.05;
  const [offset, setOffset] = useState<any>([0, 0, 0]);
  const [rotate, setRotate] = useState<any>([0, 0, 0]);
  const [outOffset, setOutOffset] = useState<any>([0, 0, 0]);
  const [outRotate, setOutRotate] = useState<any>([0, 0, 0]);
  const [inOffset, setInOffset] = useState<any>([0, 0, 0]);
  const [inRotate, setInRotate] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
      setOutOffset([0, 0, 0]);
      setOutRotate([0, 0, 0]);
      setRotate([0, 0, 0]);
      setInOffset([0, 0, 0]);
      setInRotate([0, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, 0, 0]);
      setRotate([0, 0, 0]);
      setOutOffset([0, c / 2, -thickness * 0.3]);
      setOutRotate([0, Math.PI, 0]);
      setInOffset([0, c / 2, thickness * 1.3]);
      setInRotate([0, 0, 0]);
    } else if (pivot === 'top') {
      setOffset([0, 0, isLine ? 0 : thickness]);
      setRotate([Math.PI, 0, 0]);
      setOutOffset([0, c / 2, thickness + thickness * 0.3]);
      setOutRotate([Math.PI, Math.PI, 0]);
      setInOffset([0, c / 2, 0]);
      setInRotate([Math.PI, 0, 0]);
    }
  }, [pivot, thickness, isLine, c, x]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const radius: number = 0.15;
  const shape: any = new Shape();
  shape.moveTo(-x / 2 + gapX, -gapY);
  shape.lineTo(-x / 2 + gapX, spacing);
  shape.lineTo(-x / 2, spacing);
  shape.quadraticCurveTo(-x / 2, c, -x / 2 + x * radius, c);
  shape.lineTo(x / 2 - x * radius, c);
  shape.quadraticCurveTo(x / 2, c, x / 2, spacing);
  shape.lineTo(x / 2 - gapX, spacing);
  shape.lineTo(x / 2 - gapX, -gapY);

  return (
    <group>
      {!isLine ? (
        <Fragment>
          <Extrude
            castShadow
            position={offset}
            rotation={rotate}
            args={[shape, extrudeSettings({ isLine, thickness })]}
          >
            <Decal
              // debug
              position={outOffset}
              rotation={outRotate}
              scale={[x, c, thickness]}
              map={getTextureOut(textureId) ? texture_out : null}
            >
              <OutSideMaterial />
            </Decal>
            {/* inside */}
            <Decal
              // debug
              position={inOffset}
              rotation={inRotate}
              scale={[x, c, thickness]}
              map={getTextureIn(textureId) ? texture_in : null}
            >
              <InSideMaterial isTexture={getTextureIn(textureId)} />
            </Decal>
            <BaseMaterial />
          </Extrude>
        </Fragment>
      ) : (
        <mesh castShadow position={offset} rotation={rotate}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};
export default C_Shape;
