import { useRecoilState } from 'recoil';
import {
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import { ModelControlsType, ModelInfoType } from '../../types';
import { ContactShadows } from '@react-three/drei';
import React from 'react';
import Main3DTuckEndBoxesDoubleTrayC from './Model-3d';
import MainDieLineTuckEndBoxesDoubleTrayC from './Model-dieline';
import DimensionLabel from '../DimensionLabel';
import * as THREE from 'three';

type propsType = {
  type: '3d' | '2d';
};

const HPMPK01001C0: React.FC<propsType> = ({ type }: propsType) => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height, length } = modelInfo.metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, isDimension, isPlay } = modelControls;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { HPM_PK01_001C0 } = modelSpacialData;

  if (type === '2d') {
    return <MainDieLineTuckEndBoxesDoubleTrayC />;
  }
  if (type === '3d') {
    return (
      <group>
        {isDimension && actionState === 100 && !isPlay && (
          <group>
            <DimensionLabel
              origin={new THREE.Vector3(width / 2, -height / 2.01, -length / 2)}
              yConfig={{ label: 'H', color: '#06BA31', length: height + 20 }}
              xConfig={{ label: 'W', color: '#FF590F', length: width + 20 }}
              zConfig={{ label: 'L', color: '#605DEC', length: length + 20 }}
            />
          </group>
        )}
        <group position={[-(HPM_PK01_001C0.g / 2 - width / 2), 0, -length / 2]}>
          <Main3DTuckEndBoxesDoubleTrayC />
        </group>
        {actionState === 100 && (
          <ContactShadows
            opacity={0}
            color="black"
            position={[0, -height / 1.5, 0]}
            scale={2 * width + 2 * length + HPM_PK01_001C0.g}
            blur={4}
            far={100000}
          />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK01001C0;
