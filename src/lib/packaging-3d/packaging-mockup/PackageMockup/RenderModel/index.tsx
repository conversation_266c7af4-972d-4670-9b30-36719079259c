import { CanvasContainer } from '../styles';
import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { PropsType } from '../index';
import { Canvas } from '@react-three/fiber';
import {
  CameraControls,
  GizmoHelper,
  GizmoViewport,
  OrthographicCamera,
} from '@react-three/drei';
import { atom, useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  initialCanvasSize,
  initialFileFormat,
  initialModelControls,
  initialModelInfo,
  ModelControlsType,
  ModelInfoType,
} from '../types';
import { initialModelSpecialInfo, ModelSpecialDataType } from '../spacialTypes';
import MainDieLine from './MainDieLine';
import Main3D from './Main3D';
import { setSpacialData } from '../../../utils/spacialdata';
import { getRatioSize } from '../../../utils/helper';
import Env from './Environment';
import styled from 'styled-components';
import FixEnv from './FixEnvironment';
import * as THREE from 'three';

export const modelInfoState = atom({
  key: 'modelInfoState',
  default: initialModelInfo,
});

export const modelSpacialDataState = atom({
  key: 'modelSpacialDataState',
  default: initialModelSpecialInfo,
});

export const modelControlsState = atom({
  key: 'modelControlsState',
  default: initialModelControls,
});

export const canvasSizeState = atom({
  key: 'canvasSizeState',
  default: initialCanvasSize,
});

export const fileFormatState = atom({
  key: 'fileFormatState',
  default: initialFileFormat,
});
const RenderModel: React.FC<{ data: PropsType }> = ({ data }) => {
  const ref2D: any = useRef();
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { cropPositions } = modelInfo;
  const [modelSpacialData, setModelSpecialData] =
    useRecoilState<ModelSpecialDataType>(modelSpacialDataState);
  const [modelControls, setModelControls] =
    useRecoilState<ModelControlsType>(modelControlsState);
  const { is3D, isHelper } = modelControls;
  const [canvasSize] = useRecoilState<CanvasSizeType>(canvasSizeState);
  const { x, y } = canvasSize;

  useEffect(() => {
    if (cropPositions) {
      data.onCropPositions(cropPositions);
    }
  }, [cropPositions]);

  useMemo(() => {
    setModelControls({
      ...modelControls,
      is3D: data.is3D,
      isFreeControl: data.isFreeControl,
      actionState: data.actionState,
      side: data.side,
      mode: data.mode,
      isHelper: data.isHelper,
      isPlay: data.isPlay,
      isMapControl: data.isMapControl,
      zoom: data.zoom,
      isDimension: data.isDimension,
      rotation: data.rotation,
    });
    setModelInfo({
      ...modelInfo,
      id: data.modelId,
      metadata: {
        ...modelInfo.metadata,
        width: getRatioSize({
          x: data.width,
          y: data.height,
          z: data.length,
        }).x,
        height: getRatioSize({
          x: data.width,
          y: data.height,
          z: data.length,
        }).y,
        length: getRatioSize({
          x: data.width,
          y: data.height,
          z: data.length,
        }).z,
        thickness: 0.01,
        unit: data.unit,
        initW: data.width,
        initH: data.height,
        initL: data.length,
        initT: 0.01,
      },
      textures: data.textures,
    });
    if (data) {
      setSpacialData({
        initial: modelSpacialData,
        data: data,
        onSpacial: (sd: any) => {
          setModelSpecialData(sd.spacial);
        },
      });
    }
  }, [data]);

  // const Export = () => {
  //   if (metaData?.fileFormat?.svgBlob && metaData?.fileFormat?.fileName) {
  //     saveAs(metaData.fileFormat.svgBlob, metaData.fileFormat.fileName);
  //   }
  // };
  // const [base64, setBase64] = useState<any>('');

  const updateCanvasSize = async () => {
    const canvas = await {
      width: ref2D?.current?.width,
      height: ref2D?.current?.height,
    };
    await data.onCanvasSize(canvas);
  };

  useEffect(() => {
    if (ref2D?.current) {
      setTimeout(updateCanvasSize, 1000);
    }
  }, [ref2D?.current?.width, ref2D?.current?.height]);
  // const UpdateCanvas = () => {
  //   useFrame((state) => {
  //     // console.log(state?.gl.domElement.toDataURL('image/png'));
  //     console.log(state);
  //   });
  //   return null;
  // };

  // const UpdateCanvas = () => {
  // const gl = useThree((state) => state.gl);
  // const data = gl.domElement
  //   .toDataURL('image/png')
  //   .replace('image/png', 'image/octet-stream');
  // useFrame((_state: any) => {
  // getSvgBlob(_state);
  // const data = _state.gl.domElement
  //   .toDataURL('image/png')
  //   .replace('image/png', 'image/octet-stream');
  // console.log(data);
  //   });
  //   return null;
  // };

  // const CameraZoom = () => {
  //   useFrame((_state: any) => {
  //     _state.camera.zoom = (data.zoom * 1) / 100;
  //     _state.camera.lookAt(0, 0, 0);
  //     _state.camera.updateProjectionMatrix();
  //   });
  //   return null;
  // };

  // const getSvgBlob = (canvas: any) => {
  //   const { scene, camera, size } = canvas;
  //   const { width, height } = size;
  //   const rendererSVG: SVGRenderer = new SVGRenderer();
  //   rendererSVG.setSize(width, height);
  //   rendererSVG.render(scene, camera);
  //   const xmlS: XMLSerializer = new XMLSerializer();
  //   const svgFile: string = xmlS.serializeToString(rendererSVG.domElement);
  //   const preface = '<?xml version="1.0" standalone="no"?>\r\n';
  //   const svgBlob = new Blob([preface, svgFile], {
  //     type: 'image/svg+xml;charset=utf-8',
  //   });
  //   setFileFormat({
  //     svgBlob: svgBlob,
  //     fileName: 'HPM-PK01-001A0.svg',
  //   });
  // };

  const setCanvasSize = () => {
    if (!is3D) {
      const width = data.baseSize
        ? `${data.baseSize * x}px`
        : `calc(100vh * ${x})`;
      const heigth = data.baseSize
        ? `${data.baseSize * y}px`
        : `calc(100vh * ${y})`;
      return { width: width, height: heigth };
    }
    const width = data.baseSize ? `${data.baseSize}px` : `calc(100vw)`;
    const heigth = data.baseSize ? `${data.baseSize}px` : `calc(100vh)`;
    return { width: width, height: heigth };
  };

  const getEnvironment = () => {
    if (
      data.modelId === 'HPM-PK04-001A0' ||
      data.modelId === 'HPM-PK04-002A0'
    ) {
      return <FixEnv />;
    }
    return <Env />;
  };
  const { DEG2RAD } = THREE.MathUtils;
  const cameraControlsRef: any = useRef();

  useEffect(() => {
    if (cameraControlsRef) {
      const factor = data.rotation.y >= 0 ? 10 : -10;
      cameraControlsRef.current?.rotate(factor * DEG2RAD, 0, true);
    }
  }, [data.rotation.y]);

  const [zoomState, setZoomState] = useState<number>(100);

  useEffect(() => {
    if (cameraControlsRef) {
      if (data.zoom > zoomState) {
        setZoomState(data.zoom);
        cameraControlsRef.current?.dolly(20, true);
      } else {
        setZoomState(data.zoom);
        cameraControlsRef.current?.dolly(-20, true);
      }
    }
  }, [data.zoom]);

  useEffect(() => {
    if (cameraControlsRef) {
      cameraControlsRef.current?.reset(true);
    }
  }, [data?.modelId]);

  return (
    <CanvasContainer
      // width={`${400 * x}px`}
      // height={`${400 * y}px`}
      // width={is3D ? `${400 * x}px` : `calc(100vh * ${x})`}
      // height={is3D ? `${400 * y}px` : `calc(100vh * ${y})`}
      // width={
      //   is3D
      //     ? `${data.baseSize ? `${data.baseSize}px` : '100vw'}`
      //     : `calc(100vh * ${x})`
      // }
      // height={
      //   is3D
      //     ? `${data.baseSize ? `${data.baseSize}px` : 'calc(100vh - 40px)'}`
      //     : `calc(100vh * ${y})`
      // }
      is3D={is3D}
      width={setCanvasSize().width}
      height={setCanvasSize().height}
    >
      <ThreeCanvas
        style={{ display: is3D ? '' : 'none' }}
        frameloop={'always'}
        shadows
        // flat
        // linear
        // gl={{ preserveDrawingBuffer: true, antialias: false }}
        // gl={(canvas: any) => {
        //   const gl = new SVGRenderer();
        //   const parent = canvas.parentNode;
        //   parent.removeChild(canvas);
        //   parent.appendChild(gl.domElement);
        //   return gl;
        // }}
        camera={{
          position: [0, 0, 10],
          near: 0.001,
          far: 2500,
        }}
      >
        {/* <Perf position="top-right" /> */}
        <CameraControls ref={cameraControlsRef} />
        {/* {!data.isMapControl && <CameraZoom />} */}
        {/* <Env /> */}
        {getEnvironment()}
        <Main3D modelId={data.modelId} />
        {isHelper && (
          <Fragment>
            <GizmoHelper
              alignment="bottom-right" // widget alignment within scene
              margin={[80, 80]} // widget margins (X, Y)
            >
              <GizmoViewport
                axisColors={['red', 'green', 'blue']}
                labelColor="black"
              />
            </GizmoHelper>
            <axesHelper scale={[1000, 1000, 1000]} />
            <axesHelper scale={[1000, 1000, 1000]} rotation={[0, Math.PI, 0]} />
          </Fragment>
        )}
      </ThreeCanvas>
      <Canvas
        ref={ref2D}
        style={{ display: !is3D ? '' : 'none' }}
        frameloop={'always'}
        // shadows
        // flat
        gl={{ preserveDrawingBuffer: true }}
        // linear
        // gl={{ preserveDrawingBuffer: true, antialias: false }}
        // gl={(canvas: any) => {
        // const gl = new SVGRenderer();
        // const parent = canvas.parentNode;
        // parent.removeChild(canvas);
        // parent.appendChild(gl.domElement);
        // console.log(gl);
        // return canvas;
        // }}
        camera={{
          position: [0, 0, 10],
          near: 0.001,
          far: 100000000,
        }}
      >
        {isHelper && (
          <Fragment>
            <GizmoHelper
              alignment="bottom-right" // widget alignment within scene
              margin={[80, 80]} // widget margins (X, Y)
            >
              <GizmoViewport
                axisColors={['red', 'green', 'blue']}
                labelColor="black"
              />
            </GizmoHelper>
            <axesHelper scale={[1000, 1000, 1000]} />
            <axesHelper scale={[1000, 1000, 1000]} rotation={[0, Math.PI, 0]} />
          </Fragment>
        )}
        {/* <CameraZoom /> */}
        {/* <Env /> */}
        <OrthographicCamera makeDefault />
        <MainDieLine modelId={data.modelId} />
        {/* <UpdateCanvas /> */}
      </Canvas>
    </CanvasContainer>
  );
};

export default RenderModel;

const ThreeCanvas = styled(Canvas)`
  height: 100% !important;
  width: 100% !important;
`;
