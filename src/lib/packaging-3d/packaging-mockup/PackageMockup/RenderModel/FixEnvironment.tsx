import React, { Fragment, useState } from 'react';
import {
  MapControls,
  OrbitControls,
  PerspectiveCamera,
} from '@react-three/drei';
import { useRecoilState } from 'recoil';
import { ModelControlsType } from '../types';
import { modelControlsState } from './index';

const FixEnv: React.FC = () => {
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { isFreeControl, isMapControl } = modelControls;
  const [intensity] = useState<number>(2);

  return (
    <Fragment>
      <directionalLight position={[0, 1, 0]} intensity={intensity * 1.5} />
      <directionalLight position={[-1, 0, 0]} intensity={intensity * 0.9} />
      <directionalLight position={[1, 0, 0]} intensity={intensity} />
      <directionalLight position={[0, 0, 1]} intensity={intensity * 0.6} />
      <directionalLight position={[0, 0, -1]} intensity={intensity * 0.5} />
      <directionalLight position={[0, -1, 0]} intensity={intensity * 0.75} />
      <PerspectiveCamera
        position={[0, 200, 800]}
        fov={14}
        far={100000}
        makeDefault
      />
      <OrbitControls makeDefault={isFreeControl} enabled={isFreeControl} />
      {isMapControl && (
        <MapControls
          enableDamping={true}
          dampingFactor={0.08}
          minDistance={100}
          maxDistance={500}
          maxPolarAngle={Math.PI / 2}
        />
      )}
    </Fragment>
  );
};

export default FixEnv;
