import styled from 'styled-components';
import { Billboard, Html, Line } from '@react-three/drei';
import React from 'react';
import { Vector3 } from 'three';

type propsType = {
  origin: Vector3;
  yConfig: { label: string; color: string; length: number };
  xConfig: { label: string; color: string; length: number };
  zConfig: { label: string; color: string; length: number };
};

const DimensionLabel: React.FC<propsType> = (props: propsType) => {
  const { origin, yConfig, xConfig, zConfig } = props;
  return (
    <group position={origin}>
      <Billboard
        position={[-xConfig.length, 0, 0]}
        scale={2}
        follow={true}
        lockX={false}
        lockY={false}
        lockZ={false}
      >
        <Annotation>
          <TextLabel color={xConfig.color}>{xConfig.label}</TextLabel>
        </Annotation>
      </Billboard>
      <Line
        points={[
          [1, 0, -1],
          [-xConfig.length + 5 + 1, 0, -1],
        ]}
        color={xConfig.color}
        lineWidth={2}
        segments
        dashed={false}
        // vertexColors={[[0, 0, 0], ...]}
      />
      <Billboard
        position={[0, yConfig.length, 0]}
        scale={2}
        follow={true}
        lockX={false}
        lockY={false}
        lockZ={false}
      >
        <Annotation>
          <TextLabel color={yConfig.color}>{yConfig.label}</TextLabel>
        </Annotation>
      </Billboard>
      <Line
        points={[
          [1, 0, -1],
          [1, yConfig.length - 5, -1],
        ]}
        color={yConfig.color}
        lineWidth={2}
        segments
        dashed={false}
        // vertexColors={[[0, 0, 0], ...]}
      />
      <Billboard
        position={[0, 0, zConfig.length]}
        scale={2}
        follow={true}
        lockX={false}
        lockY={false}
        lockZ={false}
      >
        <Annotation>
          <TextLabel color={zConfig.color}>{zConfig.label}</TextLabel>
        </Annotation>
      </Billboard>
      <Line
        points={[
          [1, 0, -1],
          [1, 0, zConfig.length - 5 - 1],
        ]}
        color={zConfig.color}
        lineWidth={2}
        segments
        dashed={false}
        // vertexColors={[[0, 0, 0], ...]}
      />
    </group>
  );
};

export default DimensionLabel;

const TextLabel = styled.div<{ color: string }>`
  font-size: 80px;
  font-weight: 800;
  border: ${(props: any) => `20px solid ${props.color}`};
  color: ${(props: any) => props.color};
  background: ${(props: any) => `${props.color}40`};
  padding: 50px;
  border-radius: 100%;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

function Annotation({ children, ...props }: any) {
  return (
    <Html
      {...props}
      as="div"
      zIndexRange={[1, 0]}
      transform
      occlude
      // geometry={<planeGeometry args={[10, 10, 0.1]} />}
    >
      {children}
    </Html>
  );
}
