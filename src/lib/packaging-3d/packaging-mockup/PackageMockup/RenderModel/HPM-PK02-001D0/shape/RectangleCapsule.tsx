import React, { useLayoutEffect, useMemo, useState } from 'react';
import { Path, Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import _ from 'lodash';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  aX: number;
  aY: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};
const RectangleCapsule: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, aX, aY, pivot, isLine, textures, textureId } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useLayoutEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
    }
  }, [pivot, x, y, isLine]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );

  const shapes3D: any = useMemo(() => {
    const w = x * 0.15;
    const h = y * 0.1;
    const r = h / 2;
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    const hole: any = new Path();
    hole.moveTo(aX - w / 2, aY);
    hole.lineTo(aX + w / 2, aY);
    hole.absarc(
      aX + w / 2 + r,
      aY - r,
      r,
      Math.PI / 2,
      (3 * Math.PI) / 2,
      true,
    );
    hole.lineTo(aX + w / 2, aY - h);
    hole.lineTo(aX - w / 2, aY - h);
    hole.absarc(
      aX - w / 2 - r,
      aY - r,
      r,
      (3 * Math.PI) / 2,
      Math.PI / 2,
      true,
    );
    hole.lineTo(aX - w / 2, aY);
    shape.holes = [hole];
    return shape;
  }, [x, y]);
  const shapes: any = useMemo(() => {
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    return new Shape(shape.getPoints(10));
  }, [x, y]);
  const hole: any = useMemo(() => {
    const w = x * 0.15;
    const h = y * 0.1;
    const r = h / 2;
    const holeA: any = new Shape();
    holeA.moveTo(aX - w / 2, aY);
    holeA.lineTo(aX + w / 2, aY);
    holeA.absarc(
      aX + w / 2 + r,
      aY - r,
      r,
      Math.PI / 2,
      (3 * Math.PI) / 2,
      true,
    );
    holeA.lineTo(aX + w / 2, aY - h);
    holeA.lineTo(aX - w / 2, aY - h);
    holeA.absarc(
      aX - w / 2 - r,
      aY - r,
      r,
      (3 * Math.PI) / 2,
      Math.PI / 2,
      true,
    );
    holeA.lineTo(aX - w / 2, aY);
    return new Shape(holeA.getPoints(10));
  }, [x, y, isLine]);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shapes3D, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, thickness * 0.3]}
            rotation={[0, -Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            scale={[x, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shapes} />
          <LineByShape shape={hole} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleCapsule;
