import React, { useEffect, useState } from 'react';
import { Extrude, useTexture, Decal } from '@react-three/drei';
import { Shape } from 'three';
import _ from 'lodash';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  degree: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
  textureLabel?: string;
};

const TrapzoidShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, degree, pivot, isLine, textures, textureId } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'top') {
      setOffset([0, -y / 2, 0]);
    }
  }, [pivot, x, y]);
  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const w: number = degreeToWidth(degree, y);
  const shape: any = new Shape();
  const bt_gap: number = w * 0.2;
  shape.moveTo(-x / 2, y / 2);
  shape.lineTo(x / 2, y / 2);
  shape.lineTo(x / 2 - w + bt_gap, -y / 2 + bt_gap);
  shape.lineTo(x / 2 - w + bt_gap, -y / 2);
  shape.lineTo(-x / 2 + w - bt_gap, -y / 2);
  shape.lineTo(-x / 2 + w - bt_gap, -y / 2 + bt_gap);
  shape.lineTo(-x / 2, y / 2);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, thickness * 0.3]}
            rotation={[0, Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            scale={[x, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default TrapzoidShape;
