import React, { useEffect, useRef, useState } from 'react';
import RectangleShape from './shape/RectangleShape';
import { useRecoilState } from 'recoil';
import { CanvasSizeType, ModelControlsType, ModelInfoType } from '../../types';
import {
  canvasSizeState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import TrapeziumShape from './shape/TrapeziumShape';
import TrapzoidShape from './shape/TrapzoidShape';
import TriangleShape from './shape/TriangleShap';
import { degreeToWidth } from '../../../../utils/helper';
import gsap from 'gsap';
import { PAPER_SIDE } from '../../../../utils/constants';

const Main3DShoppingBagB0: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, height, length, thickness } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { g, gDegree, topHeight, bottomHeight } =
    modelSpacialData.HPM_PK02_001B0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side, is3D, isPlay, actionState } = modelControls;

  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);

  useEffect(() => {
    if (is3D) {
      getCanvasSize();
    }
  }, [is3D]);

  const getCanvasSize = () => {
    setCanvasSize({
      ...canvasSize,
      x: 1,
      y: 1,
    });
  };

  // Ref for animation
  const RefModel = useRef<any>();
  const Ref1_S1_1 = useRef<any>();
  const Ref1_S2_1 = useRef<any>();
  const Ref1_S3_1 = useRef<any>();
  const Ref1_S4_1 = useRef<any>();
  const Ref1_S5_1 = useRef<any>();
  const Ref2_G1_G2 = useRef<any>();
  const Ref2_G1 = useRef<any>();
  const Ref3_G4_G5 = useRef<any>();
  const Ref3_G5 = useRef<any>();
  const Ref4_S1_3 = useRef<any>();
  const Ref4_S2_4 = useRef<any>();
  const Ref4_S2_5 = useRef<any>();
  const Ref4_S3_3 = useRef<any>();
  const Ref4_S4_4 = useRef<any>();
  const Ref4_S4_5 = useRef<any>();
  const Ref4_S5_3 = useRef<any>();
  const Ref5_G2_3 = useRef<any>();
  const Ref5_G4_3 = useRef<any>();
  const duration: number = 2;
  const getPos = (state: number) => {
    return state * duration;
  };

  const [tl] = useState(gsap.timeline({ repeat: -1 }));
  const config: any = {
    gap: 30,
    r: 1.05,
  };
  useEffect(() => {
    tl.set(RefModel.current.position, { z: 0, duration: 0 });
    tl.set(Ref1_S1_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_S2_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_S3_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_S4_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_S5_1.current.rotation, { x: 0, duration: 0 }, 0);

    tl.set(Ref1_S1_1.current.position, { z: 0, duration: 0 }, 0);
    tl.set(Ref1_S2_1.current.position, { z: 0, duration: 0 }, 0);
    tl.set(Ref1_S3_1.current.position, { z: 0, duration: 0 }, 0);
    tl.set(Ref1_S4_1.current.position, { z: 0, duration: 0 }, 0);
    tl.set(Ref1_S5_1.current.position, { z: 0, duration: 0 }, 0);

    tl.set(Ref2_G1_G2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref2_G1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G4_G5.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G5.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref4_S1_3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref4_S2_4.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref4_S2_5.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref4_S3_3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref4_S4_4.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref4_S4_5.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref4_S5_3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref5_G2_3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref5_G4_3.current.rotation, { x: 0, duration: 0 }, 0);
    // To close
    tl.to(
      Ref1_S1_1.current.rotation,
      { x: Math.PI / config.r, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S2_1.current.rotation,
      { x: Math.PI / config.r, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S3_1.current.rotation,
      { x: Math.PI / config.r, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S4_1.current.rotation,
      { x: Math.PI / config.r, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S5_1.current.rotation,
      { x: Math.PI / config.r, duration: duration },
      getPos(0),
    );

    tl.to(
      Ref1_S1_1.current.position,
      { z: thickness * config.gap, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S2_1.current.position,
      { z: thickness * config.gap, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S3_1.current.position,
      { z: thickness * config.gap, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S4_1.current.position,
      { z: thickness * config.gap, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_S5_1.current.position,
      { z: thickness * config.gap, duration: duration },
      getPos(0),
    );

    tl.to(
      Ref2_G1_G2.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(1),
    );
    tl.to(
      Ref2_G1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(1),
    );
    tl.to(
      Ref3_G4_G5.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref3_G5.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref4_S1_3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S2_4.current.rotation,
      { x: -Math.PI, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S2_5.current.rotation,
      { y: -Math.PI, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S3_3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S4_4.current.rotation,
      { x: -Math.PI, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S4_5.current.rotation,
      { y: -Math.PI, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref4_S5_3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref5_G2_3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref5_G4_3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.paused(!isPlay);
    if (!isPlay) {
      tl.progress(actionState / 100);
    }
  }, [isPlay, actionState]);

  return (
    <group
      ref={RefModel}
      rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}
    >
      <group ref={Ref2_G1_G2} name={'G1-G2'} position={[-length / 2, 0, 0]}>
        <group ref={Ref2_G1} name={'G-1'} position={[-width, 0, 0]}>
          <group
            ref={Ref1_S1_1}
            name={'S-1-1'}
            position={[-g / 2, height / 2, 0]}
          >
            <TrapeziumShape
              textureId={17}
              thickness={thickness}
              isLine={false}
              pivot="right-top"
              degree={gDegree}
              g={g}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-1-2'} position={[-g / 2, 0, 0]}>
            <RectangleShape
              textureId={18}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={g}
              y={height}
              textures={textures}
            />
          </group>
          <group
            ref={Ref4_S1_3}
            name={'S-1-3'}
            position={[-g / 2, -height / 2, 0]}
          >
            <TrapeziumShape
              textureId={19}
              thickness={thickness}
              isLine={false}
              pivot="right-bottom"
              degree={gDegree}
              g={g}
              y={bottomHeight}
              textures={textures}
            />
          </group>
        </group>
        <group name={'G-2'}>
          <group
            ref={Ref1_S2_1}
            name={'S-2-1'}
            position={[-width / 2, height / 2, 0]}
          >
            <RectangleShape
              textureId={12}
              pivot="bottom"
              isLine={false}
              thickness={thickness}
              x={width}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-2-2'} position={[-width / 2, 0, 0]}>
            <RectangleShape
              textureId={13}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={width}
              y={height}
              textures={textures}
            />
          </group>
          <group
            ref={Ref5_G2_3}
            name={'G-2-3'}
            position={[-width / 2, -height / 2, 0]}
          >
            <group
              position={[
                -width / 2 + degreeToWidth(45, bottomHeight) / 2,
                -bottomHeight / 2,
                0,
              ]}
              rotation={[0, 0, -Math.PI / 4]}
            >
              <group
                ref={Ref4_S2_4}
                name={'S-2-4'}
                rotation={[0, 0, -Math.PI / 4]}
              >
                <TriangleShape
                  textureId={16}
                  pivot="right"
                  isLine={false}
                  thickness={thickness}
                  x={width}
                  y={bottomHeight}
                  degree={45}
                  textures={textures}
                />
              </group>
            </group>
            <group name={'S-2-3'}>
              <TrapzoidShape
                textureId={15}
                pivot="top"
                isLine={false}
                thickness={thickness}
                x={width}
                y={bottomHeight}
                degree={45}
                textures={textures}
              />
            </group>
            <group
              position={[
                width / 2 - degreeToWidth(45, bottomHeight) / 2,
                -bottomHeight / 2,
                0,
              ]}
              rotation={[0, 0, -Math.PI / 4]}
            >
              <group
                ref={Ref4_S2_5}
                name={'S-2-5'}
                rotation={[0, 0, Math.PI / 4]}
              >
                <TriangleShape
                  textureId={14}
                  pivot="left"
                  isLine={false}
                  thickness={thickness}
                  x={width}
                  y={bottomHeight}
                  degree={45}
                  textures={textures}
                />
              </group>
            </group>
          </group>
        </group>
      </group>
      <group name={'G-3'}>
        <group ref={Ref1_S3_1} name={'S-3-1'} position={[0, height / 2, 0]}>
          <RectangleShape
            textureId={9}
            pivot="bottom"
            isLine={false}
            thickness={thickness}
            x={length}
            y={topHeight}
            textures={textures}
          />
        </group>
        <group name={'S-3-2'}>
          <RectangleShape
            textureId={10}
            pivot="center"
            isLine={false}
            thickness={thickness}
            x={length}
            y={height}
            textures={textures}
          />
        </group>
        <group ref={Ref4_S3_3} name={'S-3-3'} position={[0, -height / 2, 0]}>
          <RectangleShape
            textureId={11}
            pivot="top"
            isLine={false}
            thickness={thickness}
            x={length}
            y={bottomHeight}
            textures={textures}
          />
        </group>
      </group>
      <group ref={Ref3_G4_G5} name={'G4-G5'} position={[length / 2, 0, 0]}>
        <group name={'G-4'}>
          <group
            ref={Ref1_S4_1}
            name={'S-4-1'}
            position={[width / 2, height / 2, 0]}
          >
            <RectangleShape
              textureId={4}
              pivot="bottom"
              isLine={false}
              thickness={thickness}
              x={width}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-4-2'} position={[width / 2, 0, 0]}>
            <RectangleShape
              textureId={5}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={width}
              y={height}
              textures={textures}
            />
          </group>
          <group
            ref={Ref5_G4_3}
            name={'G-4-3'}
            position={[width / 2, -height / 2, 0]}
          >
            <group
              position={[
                -width / 2 + degreeToWidth(45, bottomHeight) / 2,
                -bottomHeight / 2,
                0,
              ]}
              rotation={[0, 0, -Math.PI / 4]}
            >
              <group
                ref={Ref4_S4_4}
                name={'S-4-4'}
                rotation={[0, 0, -Math.PI / 4]}
              >
                <TriangleShape
                  textureId={8}
                  pivot="right"
                  isLine={false}
                  thickness={thickness}
                  x={width}
                  y={bottomHeight}
                  degree={45}
                  textures={textures}
                />
              </group>
            </group>
            <group name={'S-4-3'}>
              <TrapzoidShape
                textureId={7}
                pivot="top"
                isLine={false}
                thickness={thickness}
                x={width}
                y={bottomHeight}
                degree={45}
                textures={textures}
              />
            </group>
            <group
              position={[
                width / 2 - degreeToWidth(45, bottomHeight) / 2,
                -bottomHeight / 2,
                0,
              ]}
              rotation={[0, 0, -Math.PI / 4]}
            >
              <group
                ref={Ref4_S4_5}
                name={'S-4-5'}
                rotation={[0, 0, Math.PI / 4]}
              >
                <TriangleShape
                  textureId={6}
                  pivot="left"
                  isLine={false}
                  thickness={thickness}
                  x={width}
                  y={bottomHeight}
                  degree={45}
                  textures={textures}
                />
              </group>
            </group>
          </group>
        </group>
        <group ref={Ref3_G5} name={'G-5'} position={[(2 * width) / 2, 0, 0]}>
          <group
            ref={Ref1_S5_1}
            name={'S-5-1'}
            position={[length / 2, height / 2, 0]}
          >
            <RectangleShape
              textureId={1}
              pivot="bottom"
              isLine={false}
              thickness={thickness}
              x={length}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-5-2'} position={[length / 2, 0, 0]}>
            <RectangleShape
              textureId={2}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={length}
              y={height}
              textures={textures}
            />
          </group>
          <group
            ref={Ref4_S5_3}
            name={'S-5-3'}
            position={[length / 2, -height / 2, 0]}
          >
            <RectangleShape
              textureId={3}
              pivot="top"
              isLine={false}
              thickness={thickness}
              x={length}
              y={bottomHeight}
              textures={textures}
            />
          </group>
        </group>
      </group>
    </group>
  );
};

export default Main3DShoppingBagB0;
