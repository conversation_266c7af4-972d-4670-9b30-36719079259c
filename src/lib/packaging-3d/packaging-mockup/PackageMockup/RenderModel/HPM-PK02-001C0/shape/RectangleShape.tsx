import React, { useEffect, useState } from 'react';
import { Extrude, useTexture, Decal } from '@react-three/drei';
import { Shape } from 'three';
import _ from 'lodash';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const RectangleShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, pivot, isLine, textures, textureId } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
    } else if (pivot === 'left') {
      setOffset([x / 2, 0, 0]);
    } else if (pivot === 'top') {
      setOffset([0, -y / 2, 0]);
    } else if (pivot === 'right') {
      setOffset([-x / 2, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, y / 2, 0]);
    }
  }, [pivot, x, y]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const shape: any = new Shape();
  shape.moveTo(-x / 2, -y / 2);
  shape.lineTo(-x / 2, y / 2);
  shape.lineTo(x / 2, y / 2);
  shape.lineTo(x / 2, -y / 2);
  shape.lineTo(-x / 2, -y / 2);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, thickness * 0.3]}
            rotation={[0, -Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            // rotation={[0, 0, textureLabel === 'HEIGHT' ? Math.PI : 0]}
            scale={[x, y, thickness]}
            // scale={[
            //   textureLabel === 'HEIGHT' ? 15 : 8,
            //   textureLabel === 'HEIGHT' ? 13 : 8,
            //   thickness,
            // ]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleShape;
