import React, { useLayoutEffect, useMemo, useState } from 'react';
import { Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import _ from 'lodash';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  aX: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const RectangleDash: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, aX, pivot, isLine, textures, textureId } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useLayoutEffect(() => {
    if (pivot === 'top') {
      setOffset([0, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, y / 2, 0]);
    }
  }, [pivot, x, y, isLine]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const shapes: any = useMemo(() => {
    const gapW = x * 0.1;
    const gapH = x * 0.005;
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    if (pivot === 'top') {
      shape.lineTo(-aX, y / 2);
      shape.lineTo(-aX, y / 2 - gapH);
      shape.lineTo(-aX + gapW, y / 2 - gapH);
      shape.lineTo(-aX + gapW, y / 2);
      shape.lineTo(aX - gapW, y / 2);
      shape.lineTo(aX - gapW, y / 2 - gapH);
      shape.lineTo(aX, y / 2 - gapH);
      shape.lineTo(aX, y / 2);
    }
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    if (pivot === 'bottom') {
      shape.lineTo(aX, -y / 2);
      shape.lineTo(aX, -y / 2 + gapH);
      shape.lineTo(aX - gapW, -y / 2 + gapH);
      shape.lineTo(aX - gapW, -y / 2);
      shape.lineTo(-aX + gapW, -y / 2);
      shape.lineTo(-aX + gapW, -y / 2 + gapH);
      shape.lineTo(-aX, -y / 2 + gapH);
      shape.lineTo(-aX, -y / 2);
    }
    shape.lineTo(-x / 2, -y / 2);
    return new Shape(shape.getPoints(10));
  }, [x, y]);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shapes, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, thickness * 0.3]}
            rotation={[0, -Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            scale={[x, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shapes} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleDash;
