import React, { useEffect, useState } from 'react';
import { Shape } from 'three';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import _ from 'lodash';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  color?: string;
  thickness: number;
  pivot?: any;
  isLine?: any;
  degree?: any;
  g?: any;
  y?: any;
  textures?: any;
  textureId?: any;
};

const TrapeziumShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, pivot, isLine, degree, g, y, textures, textureId } = props;

  const [outOffset, setOutOffset] = useState<any>([0, 0, 0]);
  // const [outRotate, setOutRotate] = useState<any>([0, 0, 0]);
  const [inOffset, setInOffset] = useState<any>([0, 0, 0]);
  // const [inRotate, setInRotate] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'right-top') {
      setOutOffset([0, y / 2, thickness * 0.3]);
      setInOffset([0, y / 2, thickness * 1.3]);
    } else if (pivot === 'right-bottom') {
      setOutOffset([0, -y / 2, thickness * 0.3]);
      setInOffset([0, -y / 2, thickness * 1.3]);
    }
  }, [pivot, g, y]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const w: number = degreeToWidth(degree, g);
  const shape: any = new Shape();
  if (pivot === 'right-top') {
    shape.moveTo(g / 2, y);
    shape.lineTo(-g / 2, y - w);
    shape.lineTo(-g / 2, 0);
    shape.lineTo(g / 2, 0);
  } else if (pivot === 'right-bottom') {
    shape.moveTo(g / 2, -y);
    shape.lineTo(-g / 2, -y + w);
    shape.lineTo(-g / 2, 0);
    shape.lineTo(g / 2, 0);
  }

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={outOffset}
            rotation={[0, -Math.PI, 0]}
            scale={[g, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={inOffset}
            rotation={[0, 0, 0]}
            scale={[g, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh castShadow>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default TrapeziumShape;
