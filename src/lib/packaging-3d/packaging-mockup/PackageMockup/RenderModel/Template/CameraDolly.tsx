import { Vector3 } from 'three';
import { useFrame } from '@react-three/fiber';
import _ from 'lodash';
import React from 'react';
import { useRecoilState } from 'recoil';
import { ModelInfoType } from '../../types';
import { modelInfoState } from '../index';

const CameraDolly: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height } = modelInfo.metadata;
  const vec = new Vector3();
  useFrame((state: any) => {
    const totalX: any = width;
    const totalY: any = height;
    const padding: any = 1;
    state.camera.left = -totalX / 2 - padding;
    state.camera.right = totalX / 2 + padding;
    state.camera.top = totalY / 2 + padding;
    state.camera.bottom = -totalY / 2 - padding;
    state.camera.zoom = 1;
    const total_x: any = 2 * width;
    const total_y: any = height;
    const max: any = _.max([total_x, total_y]);
    const step = 0.1;
    const x = 0;
    const y = 0;
    const z = max * 0.75;
    state.camera.position.lerp(vec.set(x, y, z), step);
    state.camera.lookAt(0, 0, 0);
    state.camera.updateProjectionMatrix();
  });
  return null;
};

export default CameraDolly;
