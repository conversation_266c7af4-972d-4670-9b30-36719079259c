import React from 'react';
import { PAPER_SIDE } from '../../../../utils/constants';
import { useRecoilState } from 'recoil';
import { CanvasSizeType, ModelControlsType, ModelInfoType } from '../../types';
import { canvasSizeState, modelControlsState, modelInfoState } from '../index';
import { useFrame } from '@react-three/fiber';
import RectangleHole from './shape/RectangleHole';

const Main3DTemplate: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { height, length, thickness } = metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side } = modelControls;

  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);

  useFrame(() => {
    getCanvasSize();
  });

  const getCanvasSize = () => {
    setCanvasSize({
      ...canvasSize,
      x: 1,
      y: 1,
    });
  };

  // Ref for animation

  return (
    <group rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}>
      <RectangleHole
        textureId={3}
        pivot="center"
        isLine={false}
        thickness={thickness}
        x={length}
        y={height}
        textures={textures}
        textureLabel={'HEIGHT'}
      />
    </group>
  );
};

export default Main3DTemplate;
