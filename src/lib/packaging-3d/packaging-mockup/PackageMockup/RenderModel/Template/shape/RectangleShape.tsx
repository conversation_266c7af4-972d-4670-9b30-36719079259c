import React, { useEffect, useState } from 'react';
import {
  Extrude,
  useTexture,
  Text,
  RenderTexture,
  Decal,
} from '@react-three/drei';
import { RepeatWrapping, Shape } from 'three';
import _ from 'lodash';
import { extrudeSettings } from '../utils/extudeSetting';
import { BaseMaterial } from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
  textureLabel?: string;
};

const RectangleShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, pivot, isLine, textures, textureId, textureLabel } =
    props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
    } else if (pivot === 'left') {
      setOffset([x / 2, 0, 0]);
    } else if (pivot === 'top') {
      setOffset([0, -y / 2, 0]);
    } else if (pivot === 'right') {
      setOffset([-x / 2, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, y / 2, 0]);
    }
  }, [pivot, x, y]);

  const getTexture = (id: any) => {
    const tt: any = _.find(textures, (o) => {
      return Number(o.id) === Number(id);
    });
    if (tt) {
      return tt.base64;
    }
  };

  const texture_out: any = useTexture(
    getTexture(textureId) || getTexture(textureId) || '/texture/paper_0027.jpg',
  );
  texture_out.wrapS = texture_out.wrapT = RepeatWrapping;
  texture_out.repeat.set(10, 10);
  const shape: any = new Shape();
  shape.moveTo(-x / 2, -y / 2);
  shape.lineTo(-x / 2, y / 2);
  shape.lineTo(x / 2, y / 2);
  shape.lineTo(x / 2, -y / 2);
  shape.lineTo(-x / 2, -y / 2);

  // useHelper({ current: camera }, CameraHelper, 1);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, -thickness * 0.3]}
            rotation={[0, Math.PI, 0]}
            // scale={[x, y, thickness]}
            scale={[
              textureLabel === 'HEIGHT' ? 25 : 20,
              textureLabel === 'HEIGHT' ? 15 : 18,
              thickness,
            ]}
          >
            <meshPhysicalMaterial
              transparent
              map={texture_out}
              polygonOffset
              polygonOffsetFactor={-10}
              map-flipY={false}
              map-anisotropy={16}
              // iridescence={1}
              // iridescenceIOR={1}
              // iridescenceThicknessRange={[0, 1400]}
              roughness={1}
              clearcoat={0.5}
              metalness={0.75}
              toneMapped={false}
            >
              <RenderTexture attach="map" anisotropy={16}>
                <Text
                  fontSize={4}
                  color={'#5d5d5d'}
                  rotation={[0, 0, textureLabel === 'HEIGHT' ? Math.PI / 2 : 0]}
                >
                  {textureLabel}
                </Text>
              </RenderTexture>
            </meshPhysicalMaterial>
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleShape;
