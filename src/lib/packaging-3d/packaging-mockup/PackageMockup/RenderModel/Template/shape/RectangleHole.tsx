import React, { useMemo, useLayoutEffect, useState } from 'react';
import { RepeatWrapping, Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { Base, Geometry, Subtraction } from '@react-three/csg';
import { Decal, RenderTexture, Text, useTexture } from '@react-three/drei';
import _ from 'lodash';
import { BaseMaterial } from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
  textureLabel?: string;
};
const RectangleHole: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, pivot, isLine, textures, textureId, textureLabel } =
    props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useLayoutEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
    } else if (pivot === 'left') {
      setOffset([x / 2, 0, 0]);
    } else if (pivot === 'top') {
      setOffset([0, -y / 2, 0]);
    } else if (pivot === 'right') {
      setOffset([-x / 2, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, y / 2, 0]);
    }
  }, [pivot, x, y]);

  const getTexture = (id: any) => {
    const tt: any = _.find(textures, (o) => {
      return Number(o.id) === Number(id);
    });
    if (tt) {
      return tt.base64;
    }
  };

  const texture_out: any = useTexture(
    getTexture(textureId) || getTexture(textureId) || '/texture/paper_0027.jpg',
  );
  texture_out.wrapS = texture_out.wrapT = RepeatWrapping;
  texture_out.repeat.set(10, 10);
  const shapes: any = useMemo(() => {
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    return new Shape(shape.getPoints(10));
  }, [x, y]);
  const holeA: any = useMemo(() => {
    const hole: any = new Shape();
    hole.moveTo(0, 0);
    hole.absarc(-x / 4, y / 2 - y * 0.05, x * 0.015, 0, 2 * Math.PI, false);
    return new Shape(hole.getPoints(10));
  }, [x, y]);
  const holeB: any = useMemo(() => {
    const hole: any = new Shape();
    hole.moveTo(0, 0);
    hole.absarc(x / 4, y / 2 - y * 0.05, x * 0.015, 0, 2 * Math.PI, false);
    return new Shape(hole.getPoints(10));
  }, [x, y]);

  return (
    <group>
      {!isLine ? (
        <mesh>
          <Geometry useGroups>
            <Base position={offset}>
              <extrudeGeometry
                args={[shapes, extrudeSettings({ isLine, thickness })]}
              />
            </Base>
            <Subtraction
              position={[-x / 4, y / 2 - y * 0.05, 0]}
              rotation={[Math.PI / 2, 0, 0]}
            >
              <cylinderGeometry
                args={[x * 0.015, x * 0.015, thickness + 1, 64, 1]}
              />
            </Subtraction>
            <Subtraction
              position={[x / 4, y / 2 - y * 0.05, 0]}
              rotation={[Math.PI / 2, 0, 0]}
            >
              <cylinderGeometry
                args={[x * 0.015, x * 0.015, thickness + 1, 64, 1]}
              />
            </Subtraction>
          </Geometry>
          <BaseMaterial />
          <Decal
            // debug
            position={[0, 0, -thickness * 0.3]}
            rotation={[0, Math.PI, 0]}
            // scale={[x, y, thickness]}
            scale={[
              textureLabel === 'HEIGHT' ? 25 : 20,
              textureLabel === 'HEIGHT' ? 15 : 18,
              thickness,
            ]}
          >
            <meshPhysicalMaterial
              transparent
              map={texture_out}
              polygonOffset
              polygonOffsetFactor={-10}
              roughness={1}
              clearcoat={0.5}
              metalness={0.75}
              toneMapped={false}
            >
              <RenderTexture attach="map" anisotropy={16}>
                <Text
                  fontSize={4}
                  color={'#5d5d5d'}
                  rotation={[0, 0, textureLabel === 'HEIGHT' ? Math.PI / 2 : 0]}
                >
                  {textureLabel}
                </Text>
              </RenderTexture>
            </meshPhysicalMaterial>
          </Decal>
        </mesh>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shapes} />
          <LineByShape shape={holeA} />
          <LineByShape shape={holeB} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleHole;
