import React from 'react';
import { PAPER_SIDE } from '../../../../utils/constants';
import { useRecoilState } from 'recoil';
import { CanvasSizeType, ModelControlsType, ModelInfoType } from '../../types';
import { canvasSizeState, modelControlsState, modelInfoState } from '../index';
import <PERSON>Dolly from './CameraDolly';
import _ from 'lodash';
import { useFrame } from '@react-three/fiber';
import RectangleHole from './shape/RectangleHole';

const MainDieLineTemplate: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness } = metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side } = modelControls;
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);

  useFrame(() => {
    getCanvasSize();
  });

  const getCanvasSize = () => {
    const totalX: any = width;
    const totalY: any = height;
    const gapY: any = 0;
    const spacing: any = 0;
    const w = totalX;
    const h = totalY + (spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group>
      <CameraDolly />
      <group rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}>
        <RectangleHole
          textureId={3}
          pivot="center"
          isLine={true}
          thickness={thickness}
          x={length}
          y={height}
          textures={textures}
          textureLabel={'HEIGHT'}
        />
      </group>
    </group>
  );
};

export default MainDieLineTemplate;
