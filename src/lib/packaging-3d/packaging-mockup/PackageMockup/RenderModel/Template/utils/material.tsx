import { FrontSide, RepeatWrapping } from 'three';
import { useTexture } from '@react-three/drei';
import React from 'react';

export const FrontSideMaterial: React.FC = () => {
  const texture: any = useTexture('/texture/grid-number.png');
  texture.offset.x = 0; // 0.0 - 1.0
  texture.offset.y = 0; // 0.0 - 1.0
  texture.wrapS = texture.wrapT = RepeatWrapping;
  texture.repeat.set(1 / 20, 1 / 20);
  return (
    <meshPhysicalMaterial
      // flatShading
      attach="material"
      color="#fff"
      // map={texture}
      // thickness={thickness}
      // roughness={0.4}
      // clearcoat={1}
      // clearcoatRoughness={1}
      // transmission={0.8}
      // ior={1.25}
    />
  );
};

export const BaseMaterial: React.FC<any> = () => {
  const texture_in: any = useTexture('/texture/paper_0027.jpg');
  texture_in.wrapS = texture_in.wrapT = RepeatWrapping;
  texture_in.repeat.set(10, 10);
  return (
    <meshPhongMaterial
      // map={texture_in}
      attach="material"
      color="#fff"
      // transparent={true}
      opacity={1}
      side={FrontSide}
      // color={color}
      // depthWrite={true}
      // wireframe={true}
    />
    // <meshPhysicalMaterial
    //   flatShading
    //   map={texture}
    //   alphaMap={texture}
    //   wireframe
    //   color="#3E64FF"
    //   thickness={thickness}
    //   roughness={0.4}
    //   clearcoat={1}
    //   clearcoatRoughness={1}
    //   transmission={0.8}
    //   ior={1.25}
    //   attenuationTint="#fff"
    //   attenuationDistance={0}
    // />
  );
};
