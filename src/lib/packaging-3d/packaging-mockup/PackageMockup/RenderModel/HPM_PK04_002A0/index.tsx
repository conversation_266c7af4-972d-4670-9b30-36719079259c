import { useRecoilState } from 'recoil';
import { modelControlsState } from '../index';
import { ModelControlsType } from '../../types';
import React from 'react';
import Main3DStickerRectangle from './Model-3d';
import MainDieLineStickerRectangle from './Model-dieline';

type propsType = {
  type: '3d' | '2d';
};
const HPMPK04002A0: React.FC<propsType> = ({ type }: propsType) => {
  // const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  // const { width, height, length } = modelInfo.metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState } = modelControls;
  // const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
  //   modelSpacialDataState,
  // );
  // const { HPM_SK01_001A0 } = modelSpacialData;

  if (type === '2d') {
    return <MainDieLineStickerRectangle />;
  }
  if (type === '3d') {
    return (
      <group>
        <group>
          <Main3DStickerRectangle />
        </group>
        {actionState === 100 && (
          <></>
          // <ContactShadows
          //   opacity={0.5}
          //   color="black"
          //   position={[0, -height / 2, 0]}
          //   scale={2 * width + 2 * length}
          //   blur={4}
          //   far={100000}
          // />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK04002A0;
