import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
} from '../index';
import _ from 'lodash';
import { getSizePercent } from '../../../../utils/helper';
import RectangleShape from './shape/RectangleShape';
import CameraDolly from './CameraDolly';
import { Center, Line, Text } from '@react-three/drei';

const MainDieLineStickerRectangle: React.FC = () => {
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, height, thickness, initH, initW, unit } = metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side, isDimension } = modelControls;
  const [textScale, setTextScale] = useState<{ x: number; y: number }>({
    x: 1,
    y: 1,
  });
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  useEffect(() => {
    const max: any = (height + width) / 3;
    setTextScale({ x: max / 20, y: max / 20 });
  }, [width, height]);

  useEffect(() => {
    cropSize();
  }, [width, side]);

  const cropSize = () => {
    const padding: any = 0.5;
    const totalX: any = width;
    const totalY: any = height;
    const paddingPercent: any = {
      x: getSizePercent({ value: padding, max: totalX }),
      y: getSizePercent({ value: padding, max: totalY }),
    };
    const widthPercentX: any = getSizePercent({ value: width, max: totalX });
    const heightPercentY: any = getSizePercent({ value: height, max: totalY });
    const outCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y,
        width: widthPercentX,
        height: heightPercentY,
      },
    ];
    const inCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y,
        width: widthPercentX,
        height: heightPercentY,
      },
    ];
    setModelInfo({
      ...modelInfo,
      cropPositions: {
        ...modelInfo.cropPositions,
        in: inCropPositions,
        out: outCropPositions,
      },
    });
  };

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, height]);

  const getCanvasSize = () => {
    const totalX: any = width;
    const totalY: any = height;
    const w = totalX;
    const h = totalY;
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group>
      <CameraDolly />
      <group position={[0, 0, 0]} rotation={[0, 0, 0]}>
        <group>
          <RectangleShape
            textureId={0}
            pivot="center"
            isLine={true}
            thickness={thickness}
            x={width}
            y={height}
            textures={textures}
          />
          {isDimension && (
            <>
              <Line
                points={[
                  [-width / 2 + width * 0.05, -height / 2, 0],
                  [-width / 2 + width * 0.05, height / 2, 0],
                ]}
                color="red"
                lineWidth={1}
                dashScale={1.5}
                segments
                dashed={true}
              />
              <Center position={[-width / 2 + width * 0.2, 0, 0]}>
                <Text
                  fontSize={1}
                  scale={[textScale.x, textScale.y, 1]}
                  color={'red'}
                >
                  {`H : ${initH} ${unit}`}
                </Text>
              </Center>
              <Line
                points={[
                  [-width / 2, height / 2 - width * 0.05, 0],
                  [width / 2, height / 2 - width * 0.05, 0],
                ]}
                color="green"
                lineWidth={1}
                dashScale={1.3}
                segments
                dashed={true}
              />
              <Center position={[0, height / 2 - width * 0.1, 0]}>
                <Text
                  fontSize={1}
                  scale={[textScale.x, textScale.y, 1]}
                  color={'green'}
                >
                  {`W : ${initW} ${unit}`}
                </Text>
              </Center>
            </>
          )}
        </group>
      </group>
    </group>
  );
};

export default MainDieLineStickerRectangle;
