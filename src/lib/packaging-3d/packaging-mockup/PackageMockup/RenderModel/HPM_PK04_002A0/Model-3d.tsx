import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import { CanvasSizeType, FileFormatType, ModelInfoType } from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';
import SoapBottleRectangle from './modelExam/SoapBottle';
import SoapBottleClearRectangle from './modelExam/SoapBottleClear';

const Main3DStickerRectangle: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata } = modelInfo;
  const { width, length, height } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { ex } = modelSpacialData.HPM_SK01_001A0;
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  // Ref for animation

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width;
    const totalY: any = height;
    const gapY: any = length * 0.005;
    const spacing: any = ex * 0.05;
    const w = totalX;
    const h = totalY + 2 * (ex + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  const [modelEx] = useState<'soap-bottle' | 'soap-bottle-clear'>(
    'soap-bottle-clear',
  );

  switch (modelEx) {
    case 'soap-bottle':
      return (
        <group>
          <SoapBottleRectangle />
        </group>
      );
    case 'soap-bottle-clear':
      return (
        <group>
          <SoapBottleClearRectangle />
        </group>
      );
    default:
      return (
        <group>
          <SoapBottleRectangle />
        </group>
      );
  }
};

export default Main3DStickerRectangle;

export const AluminumMaterial: React.FC<{ color: string }> = ({ color }) => {
  return (
    <meshPhysicalMaterial
      transparent
      color={color}
      transmission={1}
      roughness={1}
      clearcoat={0.5}
      metalness={1}
      toneMapped={false}
    />
  );
};
