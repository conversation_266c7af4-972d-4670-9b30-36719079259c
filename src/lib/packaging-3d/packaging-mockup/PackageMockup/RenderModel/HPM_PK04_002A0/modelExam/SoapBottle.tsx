import React, { useEffect, useState } from 'react';
import { Decal, useGLTF, useTexture } from '@react-three/drei';
import { GLTF } from 'three-stdlib';
import * as THREE from 'three';
import { useRecoilState } from 'recoil';
import { ModelInfoType } from '../../../types';
import { modelInfoState } from '../../index';
import _ from 'lodash';

type GLTFResult = GLTF & {
  nodes: {
    Soab_bottle: THREE.Mesh;
    Soab_bottle001: THREE.Mesh;
    Soap_sbottle_body: THREE.Mesh;
    Soap_sbottle_body001: THREE.Mesh;
  };
  materials: {
    Chrome: THREE.MeshStandardMaterial;
    Black_plastic: THREE.MeshStandardMaterial;
    ['Pink Icing']: THREE.MeshStandardMaterial;
    ['Paint Gloss Black _1']: THREE.MeshStandardMaterial;
  };
};

const SoapBottleRectangle: React.FC = (
  props: JSX.IntrinsicElements['group'],
) => {
  const { nodes, materials } = useGLTF('/model/soap-bottle.glb') as GLTFResult;
  const [scale] = useState(800);
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, height } = metadata;
  const [ratio, setRatio] = useState<{ x: number; y: number }>({ x: 1, y: 1 });

  const convertToRatio = () => {
    if (width > height) {
      const hRatio = height / width;
      setRatio({ ...ratio, x: 1, y: hRatio });
    }
    if (width < height) {
      const wRatio = width / height;
      setRatio({ ...ratio, x: wRatio, y: 1 });
    }
    if (width === height) {
      setRatio({ ...ratio, x: 1, y: 1 });
    }
  };

  useEffect(() => {
    convertToRatio();
  }, [width, height]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return '/digiboxs-cover.png';
  };

  return (
    <group
      {...props}
      dispose={null}
      scale={scale}
      rotation={[0, -Math.PI / 2.8, 0]}
      position={[0, -60, 0]}
    >
      <mesh
        geometry={nodes.Soab_bottle.geometry}
        material={materials.Chrome}
        position={[0, 0.127, 0]}
        rotation={[-Math.PI, 1.565, -Math.PI]}
      >
        <meshPhysicalMaterial
          transparent
          color={'#fff'}
          polygonOffset
          polygonOffsetFactor={0}
          polygonOffsetUnits={-10}
          transmission={1}
          roughness={0.3}
          clearcoat={1}
          metalness={1}
          toneMapped={false}
        />
      </mesh>
      <mesh
        geometry={nodes.Soab_bottle001.geometry}
        material={materials.Black_plastic}
        position={[0, 0.127, 0]}
        rotation={[-Math.PI, 1.565, -Math.PI]}
      >
        <meshPhysicalMaterial
          transparent
          color={'#fff'}
          polygonOffset
          polygonOffsetFactor={0}
          polygonOffsetUnits={-10}
          transmission={1}
          roughness={0.3}
          clearcoat={1}
          metalness={1}
          toneMapped={false}
        />
      </mesh>
      <mesh
        geometry={nodes.Soap_sbottle_body.geometry}
        material={materials['Pink Icing']}
        position={[0, 0.056, 0]}
        rotation={[-Math.PI, 1.565, -Math.PI]}
      >
        <Sticker
          url={getTextureOut(1)}
          position={[0, 0.01, 0.1 / 4]}
          rotation={[0, Math.PI, Math.PI]}
          scale={[0.05 * ratio.x, 0.05 * ratio.y, 0.1]}
        />
      </mesh>
      <mesh
        geometry={nodes.Soap_sbottle_body001.geometry}
        material={materials['Paint Gloss Black _1']}
        position={[0, 0.056, 0]}
        rotation={[-Math.PI, 1.565, -Math.PI]}
      >
        <meshPhysicalMaterial
          transparent
          color={'#fff'}
          polygonOffset
          polygonOffsetFactor={0}
          polygonOffsetUnits={-10}
          transmission={1}
          roughness={0.3}
          clearcoat={1}
          metalness={1}
          toneMapped={false}
        />
      </mesh>
    </group>
  );
};

export default SoapBottleRectangle;

function Sticker({ url, ...props }: any) {
  const emoji: any = useTexture(url);
  return (
    <Decal {...props}>
      <meshBasicMaterial
        transparent
        polygonOffset
        polygonOffsetFactor={0}
        polygonOffsetUnits={-10}
        map={emoji}
        map-flipY={false}
        map-anisotropy={16}
        // iridescence={1}
        // iridescenceIOR={1}
        // iridescenceThicknessRange={[0, 1400]}
        // roughness={1}
        // clearcoat={0.5}
        // metalness={0.75}
        toneMapped={false}
      />
    </Decal>
  );
}
