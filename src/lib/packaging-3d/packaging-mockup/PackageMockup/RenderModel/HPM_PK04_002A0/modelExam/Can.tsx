import React, { useState } from 'react';
import { useGLTF } from '@react-three/drei';
import { GLTF } from 'three-stdlib';
import * as THREE from 'three';
import { AluminumMaterial } from '../Model-3d';

type GLTFResult = GLTF & {
  nodes: {
    ringball: THREE.Mesh;
    ringpart1: THREE.Mesh;
    body: THREE.Mesh;
    uppiece: THREE.Mesh;
    base: THREE.Mesh;
    upcontorn: THREE.Mesh;
  };
  materials: {};
};
const Can: React.FC = () => {
  const { nodes } = useGLTF(
    // '/model/Carafe_with_stopper.glb',
    '/model/realistic_soda_can.gltf',
  ) as GLTFResult;

  const [scale] = useState(0.2);

  return (
    <group dispose={null} scale={scale}>
      <group>
        <group position={[0, 200.014 - 200.014 / 2, 0]}>
          <group
            position={[2.135, 183.046, -43.364]}
            rotation={[2.752, 1.556, -2.721]}
            scale={1.867}
          >
            <mesh
              geometry={nodes.ringball.geometry}
              material={nodes.ringball.material}
              position={[-18.913, 3.574, -2.485]}
              rotation={[-1.556, -0.158, -2.96]}
              scale={[0.132, 0.132, 0.048]}
            >
              <AluminumMaterial color={'#d7d7d7'} />
            </mesh>
            <mesh
              geometry={nodes.ringpart1.geometry}
              material={nodes.ringpart1.material}
              position={[-104.829, 10.441, -2.387]}
              rotation={[-Math.PI / 2, 0, -3.141]}
              scale={[0.881, 0.982, 1]}
            >
              <AluminumMaterial color={'#d7d7d7'} />
            </mesh>
          </group>
          <mesh
            geometry={nodes.body.geometry}
            material={nodes.body.material}
            position={[0, -24.393, -0.655]}
            scale={[2.724, 2.775, 2.733]}
          >
            <AluminumMaterial color={'#d7d7d7'} />
          </mesh>
          <mesh
            geometry={nodes.uppiece.geometry}
            material={nodes.uppiece.material}
            position={[-0.735, 177.783, -0.19]}
            rotation={[-Math.PI / 2, 0, 0]}
            scale={[3.854, 3.867, 0.474]}
          >
            <AluminumMaterial color={'#b0b0b0'} />
          </mesh>
          <mesh
            geometry={nodes.base.geometry}
            material={nodes.base.material}
            position={[-0.781, -184.654, 0]}
            rotation={[0, 0, -Math.PI]}
            scale={[-1, 1, 1]}
          >
            <AluminumMaterial color={'#d7d7d7'} />
          </mesh>
          <mesh
            geometry={nodes.upcontorn.geometry}
            material={nodes.upcontorn.material}
            position={[-0.385, 165.816, -0.123]}
            rotation={[0, 0, -Math.PI]}
            scale={[-4.861, 2.385, 4.877]}
          >
            <AluminumMaterial color={'#d7d7d7'} />
          </mesh>
        </group>
      </group>
    </group>
  );
};

export default Can;
