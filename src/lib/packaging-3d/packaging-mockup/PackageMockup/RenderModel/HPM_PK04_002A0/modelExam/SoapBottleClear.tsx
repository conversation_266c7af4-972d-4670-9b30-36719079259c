import React, { useEffect, useState } from 'react';
import { Decal, useGLTF, useTexture } from '@react-three/drei';
import { GLTF } from 'three-stdlib';
import * as THREE from 'three';
import { useRecoilState } from 'recoil';
import { ModelInfoType } from '../../../types';
import { modelInfoState } from '../../index';
import _ from 'lodash';

type GLTFResult = GLTF & {
  nodes: {
    soap_bottle: THREE.Mesh;
    soap_bottle001: THREE.Mesh;
    soap_bottle002: THREE.Mesh;
    soap_bottle003: THREE.Mesh;
    soap_bottle004: THREE.Mesh;
    soap_bottle005: THREE.Mesh;
    soap_bottle006: THREE.Mesh;
    soap_bottle007: THREE.Mesh;
    soap_bottle008: THREE.Mesh;
  };
  materials: {
    ['Material.001']: THREE.MeshStandardMaterial;
    ['Glass 01']: THREE.MeshStandardMaterial;
    soap: THREE.MeshPhysicalMaterial;
    plastic: THREE.MeshPhysicalMaterial;
    chrome: THREE.MeshStandardMaterial;
  };
};

const SoapBottleClearRectangle: React.FC = (
  props: JSX.IntrinsicElements['group'],
) => {
  const { nodes, materials } = useGLTF('/model/soap-clear.glb') as GLTFResult;
  const [scale] = useState(120);
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, height } = metadata;
  const [ratio, setRatio] = useState<{ x: number; y: number }>({ x: 1, y: 1 });
  const convertToRatio = () => {
    if (width > height) {
      const hRatio = height / width;
      setRatio({ ...ratio, x: 1, y: hRatio });
    }
    if (width < height) {
      const wRatio = width / height;
      setRatio({ ...ratio, x: wRatio, y: 1 });
    }
    if (width === height) {
      setRatio({ ...ratio, x: 1, y: 1 });
    }
  };

  useEffect(() => {
    convertToRatio();
  }, [width, height]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return '/digiboxs-cover.png';
  };

  return (
    <group
      {...props}
      dispose={null}
      scale={scale}
      rotation={[0, Math.PI / 10, 0]}
      position={[0, -60, 0]}
    >
      <mesh
        geometry={nodes.soap_bottle.geometry}
        material={materials['Material.001']}
        position={[0, 0.535, 0]}
        scale={[0.037, 0.035, 0.037]}
      />
      <mesh
        geometry={nodes.soap_bottle001.geometry}
        material={materials['Glass 01']}
        position={[0, 0.284, 0]}
        scale={6.421}
      >
        <Sticker
          url={getTextureOut(1)}
          position={[0, 0.01, 0.035]}
          rotation={[0, Math.PI, Math.PI]}
          scale={[0.05 * ratio.x, 0.05 * ratio.y, 0.01]}
        />
        <meshPhysicalMaterial
          transparent
          polygonOffset
          polygonOffsetFactor={0}
          polygonOffsetUnits={-1}
          iridescence={1}
          iridescenceIOR={1}
          iridescenceThicknessRange={[0, 1400]}
          color={'#fff'}
          transmission={1}
          roughness={0}
          clearcoat={0.5}
          metalness={0.3}
          toneMapped={false}
        />
      </mesh>
      <mesh
        geometry={nodes.soap_bottle002.geometry}
        material={materials.chrome}
        position={[0, 0.284, 0]}
        scale={6.421}
      >
        <meshPhysicalMaterial
          transparent
          color={'#fff'}
          polygonOffset
          polygonOffsetFactor={-1}
          polygonOffsetUnits={-10}
          transmission={1}
          roughness={0.3}
          clearcoat={1}
          metalness={1}
          toneMapped={false}
        />
      </mesh>
      <mesh
        geometry={nodes.soap_bottle003.geometry}
        material={materials.soap}
        position={[0, 0.284, 0]}
        scale={6.421}
      />
      <mesh
        geometry={nodes.soap_bottle005.geometry}
        material={materials.plastic}
        position={[0, 0.568, 0]}
        rotation={[0, 0, Math.PI]}
        scale={4.342}
      />
      <mesh
        geometry={nodes.soap_bottle006.geometry}
        material={materials.plastic}
        position={[0, 0.568, 0]}
        scale={5.413}
      />
      <mesh
        geometry={nodes.soap_bottle007.geometry}
        material={materials.chrome}
        position={[-0.136, 0.996, 0]}
        scale={6.045}
      >
        <meshPhysicalMaterial
          transparent
          color={'#fff'}
          polygonOffset
          polygonOffsetFactor={-10}
          polygonOffsetUnits={-10}
          transmission={1}
          roughness={0.3}
          clearcoat={1}
          metalness={1}
          toneMapped={false}
        />
      </mesh>
      <mesh
        geometry={nodes.soap_bottle008.geometry}
        material={materials.plastic}
        position={[0, 0.236, 0]}
        rotation={[0, -0.805, Math.PI / 2]}
        scale={0.152}
      />
    </group>
  );
};

export default SoapBottleClearRectangle;

function Sticker({ url, ...props }: any) {
  const emoji: any = useTexture(url);
  return (
    <Decal {...props}>
      <meshBasicMaterial
        transparent
        polygonOffset={true}
        polygonOffsetFactor={0}
        polygonOffsetUnits={-10}
        map={emoji}
        map-flipY={false}
        map-anisotropy={16}
        toneMapped={false}
      />
    </Decal>
  );
}
