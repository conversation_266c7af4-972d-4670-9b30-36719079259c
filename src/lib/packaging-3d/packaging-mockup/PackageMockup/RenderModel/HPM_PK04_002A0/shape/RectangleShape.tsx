import React, { useEffect, useState } from 'react';
import { Extrude } from '@react-three/drei';
import { Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { BaseMaterial } from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const RectangleShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, pivot, isLine } = props;
  const [offset, setOffset] = useState<any>([0, 0, 0]);

  useEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0.036]);
    }
  }, [pivot, x, y]);

  const shape: any = new Shape();
  shape.moveTo(-x / 2, -y / 2);
  shape.lineTo(-x / 2, y / 2);
  shape.lineTo(x / 2, y / 2);
  shape.lineTo(x / 2, -y / 2);
  shape.lineTo(-x / 2, -y / 2);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shape, extrudeSettings({ isLine, thickness })]}
          scale={(1 / 800) * 0.5}
        >
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleShape;
