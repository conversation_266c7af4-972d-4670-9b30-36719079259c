import { FrontSide, RepeatWrapping } from 'three';
import { RenderTexture, Text, useTexture } from '@react-three/drei';
import React from 'react';

export const OutSideMaterial: React.FC<any> = () => {
  return (
    <meshPhysicalMaterial
      transparent
      // color={'#ca9a69'}
      color={'#fff'}
      polygonOffsetFactor={-20}
      roughness={1}
      // clearcoat={0.5}
      // metalness={0.75}
      // toneMapped={false}
    />
  );
};

export const InSideMaterial: React.FC<{ isTexture: boolean }> = ({
  isTexture,
}) => {
  return (
    <meshPhysicalMaterial
      transparent
      color={isTexture ? '#d7d7d2' : '#919191'}
      // color={isTexture ? '#ca9a69' : '#ca9a69'}
      polygonOffsetFactor={-20}
      roughness={1}
      // clearcoat={0.5}
      // metalness={0.75}
      // toneMapped={false}
    />
  );
};

export const BaseMaterial: React.FC<any> = () => {
  const texture_in: any = useTexture('/texture/craft-paper-2.png');
  texture_in.wrapS = texture_in.wrapT = RepeatWrapping;
  texture_in.repeat.set(10, 10);
  return (
    <meshPhongMaterial
      // map={texture_in}
      attach="material"
      color="#fff"
      // transparent={true}
      opacity={1}
      side={FrontSide}
      // polygonOffsetFactor={-10}
      // roughness={1}
      // clearcoat={0.5}
      // metalness={0.25}
      // toneMapped={false}
      // color={color}
      // depthWrite={true}
      // wireframe={true}
    />
    // <meshPhysicalMaterial
    //   flatShading
    //   map={texture}
    //   alphaMap={texture}
    //   wireframe
    //   color="#3E64FF"
    //   thickness={thickness}
    //   roughness={0.4}
    //   clearcoat={1}
    //   clearcoatRoughness={1}
    //   transmission={0.8}
    //   ior={1.25}
    //   attenuationTint="#fff"
    //   attenuationDistance={0}
    // />
  );
};

export const DimensionMaterial: React.FC<{
  text: string | null | undefined;
}> = ({ text }) => {
  return (
    <meshPhysicalMaterial
      transparent
      polygonOffset
      polygonOffsetFactor={-10}
      // map-flipY={false}
      // map-anisotropy={16}
      // iridescence={1}
      // iridescenceIOR={1}
      // iridescenceThicknessRange={[0, 1400]}
      roughness={1}
      clearcoat={0.5}
      metalness={0.75}
      toneMapped={false}
    >
      <RenderTexture attach="map" anisotropy={16}>
        <Text
          fontSize={4}
          color={'#5d5d5d'}
          rotation={[0, 0, text === 'HEIGHT' ? Math.PI / 2 : 0]}
        >
          {text}
        </Text>
      </RenderTexture>
    </meshPhysicalMaterial>
  );
};
