import React, { useEffect, useState } from 'react';
import { Extrude, useTexture, Decal } from '@react-three/drei';
import { Shape } from 'three';
import _ from 'lodash';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  degree: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const TriangleShape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, degree, pivot, isLine, textures, textureId } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  const [outOffset, setOutOffset] = useState<any>([0, 0, 0]);
  const [outRotate, setOutRotate] = useState<any>([0, 0, 0]);
  const [inOffset, setInOffset] = useState<any>([0, 0, 0]);
  const [inRotate, setInRotate] = useState<any>([0, 0, 0]);
  const w: number = degreeToWidth(degree, y);
  const bt_gap: number = w * 0.2;

  useEffect(() => {
    if (pivot === 'right') {
      setOffset([-w / 2, y / 2, 0]);
      setOutOffset([w / 2, -y / 2 - bt_gap / 2, thickness * 0.3]);
      setOutRotate([-Math.PI, 0, Math.PI / 2]);
      setInOffset([w / 2, -y / 2 - bt_gap / 2, thickness * 1.3]);
      setInRotate([Math.PI, Math.PI, -Math.PI / 2]);
    } else if (pivot === 'left') {
      setOffset([-w / 2, y / 2, 0]);
      setOutOffset([w / 2 + bt_gap / 2, -y / 2, thickness * 0.3]);
      setOutRotate([0, Math.PI, 0]);
      setInOffset([w / 2 + bt_gap / 2, -y / 2, thickness * 1.3]);
      setInRotate([0, 0, 0]);
    }
  }, [pivot, x, y]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );

  const shape: any = new Shape();

  if (pivot === 'right') {
    shape.moveTo(w - bt_gap, -bt_gap);
    shape.lineTo(w, -bt_gap);
    shape.lineTo(w, -y);
    shape.lineTo(0, -y);
  } else if (pivot === 'left') {
    shape.moveTo(w, 0);
    shape.lineTo(w, -y);
    shape.lineTo(bt_gap, -y);
    shape.lineTo(bt_gap, -y + bt_gap);
  }

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          rotation={[0, 0, 0]}
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={outOffset}
            rotation={outRotate}
            scale={[w - bt_gap, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={inOffset}
            rotation={inRotate}
            scale={[w - bt_gap, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default TriangleShape;
