import React, { useEffect, useState } from 'react';
import { PAPER_SIDE } from '../../../../utils/constants';
import { getSizePercent } from '../../../../utils/helper';
import RectangleShape from './shape/RectangleShape';
import A_Shape from './shape/A-Shap';
import G_Shape from './shape/G-Shap';
import C_Shape from './shape/C-Shape';
import FlipRectangleShape from './shape/FlipRectangleShape';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { Center, Line, Text } from '@react-three/drei';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';
import <PERSON>Dolly from './CameraDolly';

const MainDieLineTuckEndBoxesDoubleTrayB: React.FC = () => {
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness, initW, initH, initL, unit } =
    metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c, g, aDegree, gDegree } = modelSpacialData.HPM_PK01_001B0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side, isDimension } = modelControls;
  const [textScale, setTextScale] = useState<{ x: number; y: number }>({
    x: 1,
    y: 1,
  });
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  useEffect(() => {
    const max: any = (height + width + length) / 3;
    setTextScale({ x: max / 20, y: max / 20 });
  }, [width, height, length]);

  useEffect(() => {
    cropSize();
  }, [width, length, g, c, side]);

  const cropSize = () => {
    const padding: any = 0;
    const totalX: any = width * 2 + length * 2 + g + 2 * padding;
    const totalY: any = width * 2 + height + c * 2 + 2 * padding;
    const paddingPercent: any = {
      x: getSizePercent({ value: padding, max: totalX }),
      y: getSizePercent({ value: padding, max: totalY }),
    };
    const widthPercentX: any = getSizePercent({ value: width, max: totalX });
    const widthPercentY: any = getSizePercent({ value: width, max: totalY });
    const heightPercent: any = getSizePercent({ value: height, max: totalY });
    const lengthPercentX: any = getSizePercent({ value: length, max: totalX });
    const lengthPercentY: any = getSizePercent({ value: length, max: totalY });
    const gPercent: any = getSizePercent({ value: g, max: totalX });
    const cPercent: any = getSizePercent({ value: c, max: totalY });
    const inCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + 2 * lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 2,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + lengthPercentX,
        y: paddingPercent.y + widthPercentY + cPercent,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 3,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 4,
        unit: '%',
        x: paddingPercent.x + gPercent,
        y: paddingPercent.y + widthPercentY + cPercent,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 5,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX + widthPercentX,
        y: paddingPercent.y + cPercent,
        width: lengthPercentX,
        height: widthPercentY,
      },
      {
        id: 6,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX + widthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: lengthPercentX,
        height: widthPercentY,
      },
      {
        id: 7,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + 2 * lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY - lengthPercentY / 2,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 8,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY - lengthPercentY / 2,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 9,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + 2 * lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 10,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 11,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX + widthPercentX,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: cPercent,
      },
      {
        id: 12,
        unit: '%',
        x: paddingPercent.x + gPercent + lengthPercentX + widthPercentX,
        y: paddingPercent.y + cPercent + 2 * widthPercentY + heightPercent,
        width: lengthPercentX,
        height: cPercent,
      },
      {
        id: 13,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + cPercent + widthPercentY,
        width: gPercent,
        height: heightPercent,
      },
    ];
    const outCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + widthPercentY + cPercent,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 2,
        unit: '%',
        x: paddingPercent.x + widthPercentX,
        y: paddingPercent.y + widthPercentY + cPercent,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 3,
        unit: '%',
        x: paddingPercent.x + widthPercentX + lengthPercentX,
        y: paddingPercent.y + widthPercentY + cPercent,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 4,
        unit: '%',
        x: paddingPercent.x + 2 * widthPercentX + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 5,
        unit: '%',
        x: paddingPercent.x + widthPercentX,
        y: paddingPercent.y + cPercent,
        width: lengthPercentX,
        height: widthPercentY,
      },
      {
        id: 6,
        unit: '%',
        x: paddingPercent.x + widthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: lengthPercentX,
        height: widthPercentY,
      },
      {
        id: 7,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + cPercent + widthPercentY - lengthPercentY / 2,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 8,
        unit: '%',
        x: paddingPercent.x + widthPercentX + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY - lengthPercentY / 2,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 9,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 10,
        unit: '%',
        x: paddingPercent.x + widthPercentX + lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY + heightPercent,
        width: widthPercentX,
        height: lengthPercentY / 2,
      },
      {
        id: 11,
        unit: '%',
        x: paddingPercent.x + widthPercentX,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: cPercent,
      },
      {
        id: 12,
        unit: '%',
        x: paddingPercent.x + widthPercentX,
        y: paddingPercent.y + cPercent + 2 * widthPercentY + heightPercent,
        width: lengthPercentX,
        height: cPercent,
      },
      {
        id: 13,
        unit: '%',
        x: paddingPercent.x + 2 * widthPercentX + 2 * lengthPercentX,
        y: paddingPercent.y + cPercent + widthPercentY,
        width: gPercent,
        height: heightPercent,
      },
    ];
    setModelInfo({
      ...modelInfo,
      cropPositions: {
        ...modelInfo.cropPositions,
        in: inCropPositions,
        out: outCropPositions,
      },
    });
  };

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = width * 2 + height;
    const gapY: any = length * 0.005;
    const spacing: any = c * 0.05;
    const w = totalX;
    const h = totalY + 2 * (c + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group position={[side === PAPER_SIDE.Back ? g / 2 : -g / 2, 0, 0]}>
      <CameraDolly />
      <group
        position={[
          side === PAPER_SIDE.Back ? -width / 2 : width / 2,
          0,
          thickness / 2,
        ]}
        rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}
      >
        <group>
          <group>
            <RectangleShape
              textureId={3}
              pivot="center"
              isLine={true}
              thickness={thickness}
              x={width}
              y={height}
              textures={textures}
            />
            {isDimension && (
              <>
                <Line
                  points={[
                    [-width / 2, height / 8, 0],
                    [width / 2, height / 8, 0],
                  ]}
                  color="green"
                  lineWidth={1}
                  dashScale={1.3}
                  segments
                  dashed={true}
                />
                <Center
                  rotation={
                    side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                  }
                >
                  <Text
                    fontSize={1}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'green'}
                  >
                    {`W : ${initW} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group position={[0, height / 2, 0]} rotation={[0, 0, 0]}>
            <A_Shape
              textureId={8}
              thickness={thickness}
              pivot={'bottom'}
              curve={'left'}
              isLine={true}
              a={length / 2}
              x={width}
              degree={aDegree}
              textures={textures}
            />
          </group>
          <group position={[0, -height / 2, 0]}>
            <A_Shape
              textureId={10}
              thickness={thickness}
              pivot={'top'}
              curve={'left'}
              isLine={true}
              a={length / 2}
              x={width}
              degree={aDegree}
              textures={textures}
            />
          </group>
        </group>
        <group position={[-width / 2, 0, 0]}>
          <group>
            <RectangleShape
              textureId={4}
              pivot="right"
              isLine={true}
              thickness={thickness}
              x={length}
              y={height}
              textures={textures}
            />

            {isDimension && (
              <>
                <Line
                  points={[
                    [-length / 1.5, -height / 2, 0],
                    [-length / 1.5, height / 2, 0],
                  ]}
                  color="red"
                  lineWidth={1}
                  dashScale={1.5}
                  segments
                  dashed={true}
                />
                <Center
                  position={[-length / 2, 0, 0]}
                  rotation={
                    side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                  }
                >
                  <Text
                    fontSize={1}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'red'}
                  >
                    {`H : ${initH} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group position={[-length, 0, 0]}>
            <G_Shape
              textureId={13}
              thickness={thickness}
              isLine={true}
              pivot="right"
              degree={gDegree}
              g={g}
              y={height}
              textures={textures}
            />
          </group>
          <group position={[width + length / 2, height / 2, 0]}>
            <group position={[0, width, 0]}>
              <C_Shape
                textureId={11}
                thickness={thickness}
                isLine={true}
                pivot="bottom"
                x={length}
                c={c}
                textures={textures}
              />
            </group>
            <group>
              <FlipRectangleShape
                textureId={5}
                thickness={thickness}
                x={length}
                y={width}
                isLine={true}
                pivot="bottom"
                textures={textures}
              />
            </group>
          </group>
        </group>
        <group position={[width / 2, 0, 0]}>
          <group>
            <group>
              <RectangleShape
                textureId={2}
                pivot="left"
                isLine={true}
                thickness={thickness}
                x={length}
                y={height}
                textures={textures}
              />
              {isDimension && (
                <>
                  <Line
                    points={[
                      [0, -height / 8, 0],
                      [length, -height / 8, 0],
                    ]}
                    color="blue"
                    lineWidth={1}
                    dashScale={1.3}
                    segments
                    dashed={true}
                  />
                  <Center
                    position={[length / 2, 0, 0]}
                    rotation={
                      side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                    }
                  >
                    <Text
                      fontSize={1}
                      scale={[textScale.x, textScale.y, 1]}
                      color={'blue'}
                    >
                      {`L : ${initL} ${unit}`}
                    </Text>
                  </Center>
                </>
              )}
            </group>
            <group position={[length / 2, -height / 2, 0]}>
              <group position={[0, -width, 0]}>
                <C_Shape
                  textureId={12}
                  thickness={thickness}
                  isLine={true}
                  pivot="top"
                  x={length}
                  c={c}
                  textures={textures}
                />
              </group>
              <group>
                <FlipRectangleShape
                  textureId={6}
                  thickness={thickness}
                  x={length}
                  y={width}
                  isLine={true}
                  pivot="top"
                  textures={textures}
                />
              </group>
            </group>
          </group>
          <group position={[length, 0, 0]}>
            <group>
              <RectangleShape
                textureId={1}
                pivot="left"
                isLine={true}
                thickness={thickness}
                x={width}
                y={height}
                textures={textures}
              />
            </group>
            <group position={[width / 2, height / 2, 0]}>
              <A_Shape
                textureId={7}
                thickness={thickness}
                pivot={'bottom'}
                curve={'right'}
                isLine={true}
                a={length / 2}
                x={width}
                degree={aDegree}
                textures={textures}
              />
            </group>
            <group
              position={[width / 2, -height / 2, 0]}
              // rotation-x={rotation_top_3}
            >
              <A_Shape
                textureId={9}
                thickness={thickness}
                pivot={'top'}
                curve={'right'}
                isLine={true}
                a={length / 2}
                x={width}
                degree={aDegree}
                textures={textures}
              />
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default MainDieLineTuckEndBoxesDoubleTrayB;
