import React, { useEffect, useRef, useState } from 'react';
import { PAPER_SIDE } from '../../../../utils/constants';
import RectangleShape from './shape/RectangleShape';
import A_Shape from './shape/A-Shap';
import G_Shape from './shape/G-Shap';
import C_Shape from './shape/C-Shape';
import FlipRectangleShape from './shape/FlipRectangleShape';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import gsap from 'gsap';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';

const Main3DTuckEndBoxesDoubleTrayB: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c, g, aDegree, gDegree } = modelSpacialData.HPM_PK01_001B0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, side, isPlay } = modelControls;
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  // Ref for animation
  const RefModel = useRef<any>();
  const RefA_1 = useRef<any>();
  const RefA_2 = useRef<any>();
  const RefA_3 = useRef<any>();
  const RefA_4 = useRef<any>();
  const RefRec_1 = useRef<any>();
  const RefRec_2 = useRef<any>();
  const RefRec_3 = useRef<any>();
  const RefRec_4 = useRef<any>();
  const RefFlipRec_1 = useRef<any>();
  const RefFlipRec_2 = useRef<any>();
  const RefC_1 = useRef<any>();
  const RefC_2 = useRef<any>();
  const RefG_1 = useRef<any>();
  const duration: number = 3;
  const getPos = (state: number) => {
    return state * duration;
  };
  const [tl] = useState(gsap.timeline({ repeat: -1 }));
  useEffect(() => {
    // tl.set(RefModel.current.rotation, { x: -Math.PI / 0, duration: 0 });
    // tl.set(RefModel.current.position, { z: height / 0, duration: 0 });
    tl.set(RefG_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(RefC_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefC_2.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefA_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefA_2.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefA_3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefA_4.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefRec_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(RefRec_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(RefRec_3.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(RefRec_4.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(RefFlipRec_1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(RefFlipRec_2.current.rotation, { x: 0, duration: 0 }, 0);
    // To close
    tl.to(
      RefG_1.current.rotation,
      { y: Math.PI / 1.9, duration: duration },
      getPos(0),
    );
    tl.to(
      RefC_1.current.rotation,
      { x: -Math.PI / 1.9, duration: duration },
      getPos(0),
    );
    tl.to(
      RefC_2.current.rotation,
      { x: Math.PI / 1.9, duration: duration },
      getPos(0),
    );
    tl.to(
      RefRec_1.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(1),
    );
    tl.to(
      RefRec_2.current.rotation,
      {
        y: -Math.PI / 2,
        duration: duration,
      },
      getPos(2),
    );
    // tl.to(RefModel.current.position, {
    //   z: 0,
    //   duration: duration,
    // });
    // tl.to(RefModel.current.rotation, {
    //   x: 0,
    //   duration: duration,
    // });
    tl.to(
      RefRec_3.current.rotation,
      { y: 0 / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      RefRec_4.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(4),
    );
    tl.to(
      RefA_1.current.rotation,
      { x: Math.PI / 1.9, duration: duration },
      getPos(5),
    );
    tl.to(
      RefA_2.current.rotation,
      { x: Math.PI / 1.9, duration: duration },
      getPos(5),
    );
    tl.to(
      RefA_3.current.rotation,
      { x: -Math.PI / 1.9, duration: duration },
      getPos(5),
    );
    tl.to(
      RefA_4.current.rotation,
      { x: -Math.PI / 1.9, duration: duration },
      getPos(5),
    );
    tl.to(
      RefFlipRec_2.current.rotation,
      {
        x: -Math.PI / 2,
        duration: duration,
      },
      getPos(6),
    );
    tl.to(
      RefFlipRec_1.current.rotation,
      {
        x: Math.PI / 2,
        duration: duration,
      },
      getPos(6),
    );
    tl.paused(!isPlay);
    if (!isPlay) {
      tl.progress(actionState / 100);
    }
  }, [actionState, isPlay]);

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = width * 2 + height;
    const gapY: any = length * 0.005;
    const spacing: any = c * 0.05;
    const w = totalX;
    const h = totalY + 2 * (c + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group
      ref={RefModel}
      position={[side === PAPER_SIDE.Back ? g / 2 : -g / 2, 0, 0]}
    >
      <group
        position={[
          side === PAPER_SIDE.Back ? -width / 2 : width / 2,
          0,
          thickness / 2,
        ]}
        rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}
      >
        <group>
          <group ref={RefRec_3}>
            <RectangleShape
              textureId={3}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={width}
              y={height}
              textures={textures}
            />
          </group>
          <group
            ref={RefA_2}
            position={[0, height / 2, 0]}
            rotation={[0, 0, 0]}
          >
            <A_Shape
              textureId={8}
              thickness={thickness}
              pivot={'bottom'}
              curve={'left'}
              isLine={false}
              a={length / 2}
              x={width}
              degree={aDegree}
              textures={textures}
            />
          </group>
          <group ref={RefA_4} position={[0, -height / 2, 0]}>
            <A_Shape
              textureId={10}
              thickness={thickness}
              pivot={'top'}
              curve={'left'}
              isLine={false}
              a={length / 2}
              x={width}
              degree={aDegree}
              textures={textures}
            />
          </group>
        </group>
        <group ref={RefRec_4} position={[-width / 2, 0, 0]}>
          <group>
            <RectangleShape
              textureId={4}
              pivot="right"
              isLine={false}
              thickness={thickness}
              x={length}
              y={height}
              textures={textures}
            />
          </group>
          <group ref={RefG_1} position={[-length, 0, 0]}>
            <G_Shape
              textureId={13}
              thickness={thickness}
              isLine={false}
              pivot="right"
              degree={gDegree}
              g={g}
              y={height}
              textures={textures}
            />
          </group>
          {/* <group */}
          {/*  ref={RefFlipRec_1} */}
          {/*  position={[width + length / 2, height / 2, 0]} */}
          {/* > */}
          {/*  <group ref={RefC_2} position={[0, width, 0]}> */}
          {/*    <C_Shape */}
          {/*      textureId={11} */}
          {/*      thickness={thickness} */}
          {/*      isLine={false} */}
          {/*      pivot="bottom" */}
          {/*      x={length} */}
          {/*      c={c} */}
          {/*      textures={textures} */}
          {/*    /> */}
          {/*  </group> */}
          {/*  <group> */}
          {/*    <FlipRectangleShape */}
          {/*      textureId={5} */}
          {/*      thickness={thickness} */}
          {/*      x={length} */}
          {/*      y={width} */}
          {/*      isLine={false} */}
          {/*      pivot="bottom" */}
          {/*      textures={textures} */}
          {/*      textureLabel={'WIDTH'} */}
          {/*    /> */}
          {/*  </group> */}
          {/* </group> */}
        </group>
        <group ref={RefRec_2} position={[width / 2, 0, 0]}>
          <group>
            <group ref={RefFlipRec_1} position={[length / 2, height / 2, 0]}>
              <group ref={RefC_2} position={[0, width, 0]}>
                <C_Shape
                  textureId={11}
                  thickness={thickness}
                  isLine={false}
                  pivot="bottom"
                  x={length}
                  c={c}
                  textures={textures}
                />
              </group>
              <group>
                <FlipRectangleShape
                  textureId={5}
                  thickness={thickness}
                  x={length}
                  y={width}
                  isLine={false}
                  pivot="bottom"
                  textures={textures}
                />
              </group>
            </group>
            <group>
              <RectangleShape
                textureId={2}
                pivot="left"
                isLine={false}
                thickness={thickness}
                x={length}
                y={height}
                textures={textures}
              />
            </group>
            <group ref={RefFlipRec_2} position={[length / 2, -height / 2, 0]}>
              <group ref={RefC_1} position={[0, -width, 0]}>
                <C_Shape
                  textureId={12}
                  thickness={thickness}
                  isLine={false}
                  pivot="top"
                  x={length}
                  c={c}
                  textures={textures}
                />
              </group>
              <group>
                <FlipRectangleShape
                  textureId={6}
                  thickness={thickness}
                  x={length}
                  y={width}
                  isLine={false}
                  pivot="top"
                  textures={textures}
                />
              </group>
            </group>
          </group>
          <group ref={RefRec_1} position={[length, 0, 0]}>
            <group>
              <RectangleShape
                textureId={1}
                pivot="left"
                isLine={false}
                thickness={thickness}
                x={width}
                y={height}
                textures={textures}
              />
            </group>
            <group ref={RefA_1} position={[width / 2, height / 2, 0]}>
              <A_Shape
                textureId={7}
                thickness={thickness}
                pivot={'bottom'}
                curve={'right'}
                isLine={false}
                a={length / 2}
                x={width}
                degree={aDegree}
                textures={textures}
              />
            </group>
            <group ref={RefA_3} position={[width / 2, -height / 2, 0]}>
              <A_Shape
                textureId={9}
                thickness={thickness}
                pivot={'top'}
                curve={'right'}
                isLine={false}
                a={length / 2}
                x={width}
                degree={aDegree}
                textures={textures}
              />
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default Main3DTuckEndBoxesDoubleTrayB;
