import React, { Fragment } from 'react';
import { Shape } from 'three';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import _ from 'lodash';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  color?: string;
  thickness: number;
  pivot?: any;
  isLine?: any;
  degree?: any;
  g?: any;
  y?: any;
  textures?: any;
  textureId?: any;
};

const G_Shape: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, pivot, isLine, degree, g, y, textures, textureId } = props;
  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const w: number = degreeToWidth(degree, g);
  const shape: any = new Shape();
  if (pivot === 'right') {
    shape.moveTo(0, y / 2);
    shape.lineTo(-g, y / 2 - w);
    shape.lineTo(-g, -y / 2 + w);
    shape.lineTo(0, -y / 2);
  } else if (pivot === 'left') {
    shape.moveTo(g, y / 2 - w);
    shape.lineTo(0, y / 2);
    shape.lineTo(0, -y / 2);
    shape.lineTo(g, -y / 2 + w);
  }

  return (
    <group>
      {!isLine ? (
        <Fragment>
          <Extrude
            castShadow
            args={[shape, extrudeSettings({ isLine, thickness })]}
          >
            <Decal
              // debug
              position={[-g / 2, 0, -thickness * 0.3]}
              rotation={[0, Math.PI, 0]}
              scale={[g, y, thickness]}
              map={getTextureOut(textureId) ? texture_out : null}
            >
              <OutSideMaterial />
            </Decal>
            {/* inside */}
            <Decal
              // debug
              position={[-g / 2, 0, thickness * 1.3]}
              rotation={[0, 0, 0]}
              scale={[g, y, thickness]}
              map={getTextureIn(textureId) ? texture_in : null}
            >
              <InSideMaterial isTexture={getTextureIn(textureId)} />
            </Decal>
            <BaseMaterial />
          </Extrude>
        </Fragment>
      ) : (
        <mesh castShadow>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default G_Shape;
