import React, { useEffect, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import gsap from 'gsap';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';
import RectangleConnector from './shape/RectangleConnector';
import { degreeToWidth } from '../../../../utils/helper';
import RectangleShape from './shape/RectangleShape';
import RectangleRounded from './shape/RectangleRounded';
import TriangleCurve from './shape/TriangleCurve';
import TrapezoidShape from './shape/Trapezoid';

const Main3DTrayBoxesB: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c, connectW, gapWidth, holeW } = modelSpacialData.HPM_PK01_002B0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, isPlay } = modelControls;
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  // Ref for animation
  const RefModel = useRef<any>();
  const Ref1_G3_1 = useRef<any>();
  const Ref1_G3_2 = useRef<any>();
  const Ref2_G3 = useRef<any>();
  const Ref3_G1_1_1 = useRef<any>();
  const Ref3_G1_1_2 = useRef<any>();
  const Ref4_G1 = useRef<any>();
  const Ref5_G2_1 = useRef<any>();
  const Ref5_G2_2 = useRef<any>();
  const Ref6_G2_1_1 = useRef<any>();
  const Ref6_G2_1_2 = useRef<any>();
  const Ref6_G2_2_1 = useRef<any>();
  const Ref6_G2_2_2 = useRef<any>();
  const Ref7_G1_2_1_1 = useRef<any>();
  const Ref7_G1_2_1_2 = useRef<any>();
  const Ref8_G1_2 = useRef<any>();
  const Ref9_G1_2_2_1 = useRef<any>();
  const Ref9_G1_2_2_2 = useRef<any>();
  const Ref10_G1_2_2 = useRef<any>();
  const duration: number = 2;
  const getPos = (state: number) => {
    return state * duration;
  };

  const [tl] = useState(gsap.timeline({ repeat: -1 }));
  const config: any = {
    gap: 30,
    r: 0.9,
  };
  useEffect(() => {
    tl.set(RefModel.current.rotation, { x: -Math.PI / 2, duration: 0 });
    tl.set(Ref2_G3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_G3_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref1_G3_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G1_1_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G1_1_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref4_G1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref5_G2_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref5_G2_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_1_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_1_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_2_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_2_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref7_G1_2_1_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref7_G1_2_1_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref8_G1_2.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref9_G1_2_2_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref9_G1_2_2_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref10_G1_2_2.current.rotation, { x: 0, duration: 0 }, 0);
    // to close
    tl.to(
      Ref1_G3_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_G3_2.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref2_G3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(1),
    );
    tl.to(
      Ref3_G1_1_1.current.rotation,
      { y: (Math.PI / 2) * 1.05, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref3_G1_1_2.current.rotation,
      { y: (-Math.PI / 2) * 1.05, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref4_G1.current.rotation,
      { x: Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref5_G2_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(4),
    );
    tl.to(
      Ref5_G2_2.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(4),
    );
    tl.to(
      Ref6_G2_1_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref6_G2_1_2.current.rotation,
      { y: (Math.PI / 2) * 0.95, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref6_G2_2_1.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref6_G2_2_2.current.rotation,
      { y: (-Math.PI / 2) * 0.95, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref7_G1_2_1_1.current.rotation,
      { y: Math.PI / 2 / config.r, duration: duration },
      getPos(6),
    );
    tl.to(
      Ref7_G1_2_1_2.current.rotation,
      { y: -Math.PI / 2 / config.r, duration: duration },
      getPos(6),
    );
    tl.to(
      Ref8_G1_2.current.rotation,
      { x: Math.PI / 2, duration: duration },
      getPos(7),
    );
    tl.to(
      Ref9_G1_2_2_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(8),
    );
    tl.to(
      Ref9_G1_2_2_2.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(8),
    );
    tl.to(
      Ref10_G1_2_2.current.rotation,
      { x: Math.PI / 2, duration: duration },
      getPos(9),
    );
    tl.paused(!isPlay);
    if (!isPlay) {
      tl.progress(actionState / 100);
    }
  }, [actionState, isPlay]);

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width + 4 * height;
    const totalY: any = length * 2 + 3 * height;
    const gapY: any = length * 0.005;
    const spacing: any = c * 0.05;
    const w = totalX;
    const h = totalY + 2 * (c + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group position={[-(width / 2), -height / 2, length / 2]}>
      <group ref={RefModel} position={[0, 0, 0]} rotation={[0, 0, 0]}>
        <group name="G1" position={[0, length / 2, 0]} ref={Ref4_G1}>
          <group name="G1-1" position={[0, height / 2, 0]}>
            <group
              name="G1-1-1"
              position={[-width / 2, 0, 0]}
              ref={Ref3_G1_1_1}
            >
              <RectangleShape
                textureId={9}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G1-1-0">
              <RectangleShape
                textureId={8}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G1-1-2" position={[width / 2, 0, 0]} ref={Ref3_G1_1_2}>
              <RectangleShape
                textureId={7}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
          <group name="G1-2" position={[0, height, 0]} ref={Ref8_G1_2}>
            <group name="G1-2-1" position={[0, length / 2, 0]}>
              <group
                name="G1-2-1-1"
                position={[-width / 2, 0, 0]}
                ref={Ref7_G1_2_1_1}
              >
                <RectangleRounded
                  textureId={6}
                  pivot={'right'}
                  thickness={thickness}
                  x={height}
                  y={length}
                  isLine={false}
                  textures={textures}
                />
              </group>
              <group name="G1-2-1-0">
                <RectangleShape
                  textureId={5}
                  pivot={'center'}
                  thickness={thickness}
                  x={width}
                  y={length}
                  isLine={false}
                  textures={textures}
                />
              </group>
              <group
                name="G1-2-1-2"
                position={[width / 2, 0, 0]}
                ref={Ref7_G1_2_1_2}
              >
                <RectangleRounded
                  textureId={4}
                  pivot={'left'}
                  thickness={thickness}
                  x={height}
                  y={length}
                  isLine={false}
                  textures={textures}
                />
              </group>
              <group
                name="G1-2-2"
                position={[0, length / 2, 0]}
                ref={Ref10_G1_2_2}
              >
                <group position={[0, height / 2, 0]}>
                  <group
                    name="G1-2-2-1"
                    position={[-width / 2, 0, 0]}
                    ref={Ref9_G1_2_2_1}
                  >
                    <TriangleCurve
                      textureId={3}
                      pivot={'right'}
                      thickness={thickness}
                      x={height}
                      y={height}
                      isLine={false}
                      textures={textures}
                    />
                  </group>
                  <group name="G1-2-2-0">
                    <RectangleShape
                      textureId={2}
                      pivot={'center'}
                      thickness={thickness}
                      x={width}
                      y={height}
                      isLine={false}
                      textures={textures}
                    />
                  </group>
                  <group
                    name="G1-2-2-2"
                    position={[width / 2, 0, 0]}
                    ref={Ref9_G1_2_2_2}
                  >
                    <TriangleCurve
                      textureId={1}
                      pivot={'left'}
                      thickness={thickness}
                      x={height}
                      y={height}
                      isLine={false}
                      textures={textures}
                    />
                  </group>
                </group>
              </group>
            </group>
          </group>
        </group>
        <group name="G2">
          <group name="G2-1" position={[-width / 2, 0, 0]} ref={Ref5_G2_1}>
            <group name="G2-1-1" position={[-height, 0, 0]} ref={Ref6_G2_1_1}>
              <TrapezoidShape
                textureId={15}
                pivot={'right'}
                x={gapWidth}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
              <group
                name="G2-1-2"
                position={[-gapWidth, 0, 0]}
                ref={Ref6_G2_1_2}
              >
                <RectangleConnector
                  textureId={16}
                  pivot="out-right"
                  isLine={false}
                  thickness={thickness}
                  x={height}
                  y={length - 2 * degreeToWidth(45, gapWidth)}
                  aX={length * 0.2 - degreeToWidth(45, gapWidth)}
                  connectW={connectW}
                  holeW={holeW}
                  textures={textures}
                />
              </group>
            </group>
            <group name="G2-1-0" position={[0, 0, 0]}>
              <RectangleShape
                textureId={14}
                pivot={'right'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
          <group name="G2-0">
            <RectangleConnector
              textureId={13}
              pivot="in-left-right"
              isLine={false}
              thickness={thickness}
              x={width}
              y={length}
              aX={length * 0.2}
              connectW={connectW}
              holeW={holeW}
              textures={textures}
            />
          </group>
          <group
            name="G2-2"
            position={[width / 2, 0, 0]}
            rotation={[0, -Math.PI / 2, 0]}
            ref={Ref5_G2_2}
          >
            <group name="G2-2-0">
              <RectangleShape
                textureId={12}
                pivot={'left'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G2-2-1" position={[height, 0, 0]} ref={Ref6_G2_2_1}>
              <TrapezoidShape
                textureId={11}
                pivot={'left'}
                x={gapWidth}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
              <group
                name="G2-2-2"
                position={[gapWidth, 0, 0]}
                ref={Ref6_G2_2_2}
              >
                <RectangleConnector
                  textureId={10}
                  pivot="out-left"
                  isLine={false}
                  thickness={thickness}
                  x={height}
                  y={length - 2 * degreeToWidth(45, gapWidth)}
                  aX={length * 0.2 - degreeToWidth(45, gapWidth)}
                  connectW={connectW}
                  holeW={holeW}
                  textures={textures}
                />
              </group>
            </group>
          </group>
        </group>
        <group position={[0, -length / 2, 0]} name="G3" ref={Ref2_G3}>
          <group position={[0, -height / 2, 0]}>
            <group name="G3-1" position={[-width / 2, 0, 0]} ref={Ref1_G3_1}>
              <RectangleShape
                textureId={19}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G3-0">
              <RectangleShape
                textureId={18}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G3-2" position={[width / 2, 0, 0]} ref={Ref1_G3_2}>
              <RectangleShape
                textureId={17}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default Main3DTrayBoxesB;
