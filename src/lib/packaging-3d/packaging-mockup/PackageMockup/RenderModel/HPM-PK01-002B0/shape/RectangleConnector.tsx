import React, { useLayoutEffect, useMemo, useState } from 'react';
import { Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import _ from 'lodash';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  aX: number;
  connectW: number;
  holeW: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
  textureLabel?: string;
};
const RectangleConnector: React.FC<PropsType> = (props: PropsType) => {
  const {
    thickness,
    x,
    y,
    aX,
    connectW,
    holeW,
    pivot,
    isLine,
    textures,
    textureId,
  } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  const [offsetIn, setOffsetIn] = useState<any>([0, 0, 0]);
  const [offsetOut, setOffsetOut] = useState<any>([0, 0, 0]);
  const [textureScaleIn, setTextureScaleIn] = useState<any>([0, 0, 0]);
  const [textureScaleOut, setTextureScaleOut] = useState<any>([0, 0, 0]);
  useLayoutEffect(() => {
    if (pivot === 'in-left-right') {
      setOffset([0, 0, 0]);
      setOffsetIn([0, 0, thickness * 1.3]);
      setOffsetOut([0, 0, thickness * 0.3]);
      setTextureScaleIn([x, y, thickness]);
      setTextureScaleOut([x, y, thickness]);
    } else if (pivot === 'out-right') {
      setOffset([-x / 2, 0, 0]);
      setOffsetIn([-holeW / 2, 0, thickness * 1.3]);
      setOffsetOut([-holeW / 2, 0, thickness * 0.3]);
      setTextureScaleIn([x + holeW, y, thickness]);
      setTextureScaleOut([x + holeW, y, thickness]);
    } else if (pivot === 'out-left') {
      setOffset([x / 2, 0, 0]);
      setOffsetIn([holeW / 2, 0, thickness * 1.3]);
      setOffsetOut([holeW / 2, 0, thickness * 0.3]);
      setTextureScaleIn([x + holeW, y, thickness]);
      setTextureScaleOut([x + holeW, y, thickness]);
    }
  }, [pivot, x, y, isLine]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const shapes: any = useMemo(() => {
    const gapW = holeW;
    const gapH = connectW;
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    if (pivot === 'in-left-right') {
      shape.lineTo(-x / 2, -y / 2 + aX);
      shape.lineTo(-x / 2 + gapW, -y / 2 + aX);
      shape.lineTo(-x / 2 + gapW, -y / 2 + aX + gapH);
      shape.lineTo(-x / 2, -y / 2 + aX + gapH);
      shape.lineTo(-x / 2, y / 2 - aX - gapH);
      shape.lineTo(-x / 2 + gapW, y / 2 - aX - gapH);
      shape.lineTo(-x / 2 + gapW, y / 2 - aX);
      shape.lineTo(-x / 2, y / 2 - aX);
    }
    if (pivot === 'out-right') {
      shape.lineTo(-x / 2, -y / 2 + aX);
      shape.lineTo(-x / 2 - gapW, -y / 2 + aX);
      shape.lineTo(-x / 2 - gapW, -y / 2 + aX + gapH);
      shape.lineTo(-x / 2, -y / 2 + aX + gapH);
      shape.lineTo(-x / 2, y / 2 - aX - gapH);
      shape.lineTo(-x / 2 - gapW, y / 2 - aX - gapH);
      shape.lineTo(-x / 2 - gapW, y / 2 - aX);
      shape.lineTo(-x / 2, y / 2 - aX);
    }
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    if (pivot === 'in-left-right') {
      shape.lineTo(x / 2, y / 2 - aX);
      shape.lineTo(x / 2 - gapW, y / 2 - aX);
      shape.lineTo(x / 2 - gapW, y / 2 - aX - gapH);
      shape.lineTo(x / 2, y / 2 - aX - gapH);
      shape.lineTo(x / 2, -y / 2 + aX + gapH);
      shape.lineTo(x / 2 - gapW, -y / 2 + aX + gapH);
      shape.lineTo(x / 2 - gapW, -y / 2 + aX);
      shape.lineTo(x / 2, -y / 2 + aX);
    }
    if (pivot === 'out-left') {
      shape.lineTo(x / 2, y / 2 - aX);
      shape.lineTo(x / 2 + gapW, y / 2 - aX);
      shape.lineTo(x / 2 + gapW, y / 2 - aX - gapH);
      shape.lineTo(x / 2, y / 2 - aX - gapH);
      shape.lineTo(x / 2, -y / 2 + aX + gapH);
      shape.lineTo(x / 2 + gapW, -y / 2 + aX + gapH);
      shape.lineTo(x / 2 + gapW, -y / 2 + aX);
      shape.lineTo(x / 2, -y / 2 + aX);
    }
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    return new Shape(shape.getPoints(10));
  }, [x, y]);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shapes, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={offsetOut}
            rotation={[0, -Math.PI, 0]}
            scale={textureScaleOut}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={offsetIn}
            rotation={[0, 0, 0]}
            scale={textureScaleIn}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shapes} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleConnector;
