import React, { useEffect, useState } from 'react';
import { Extrude, useTexture, Decal } from '@react-three/drei';
import { MathUtils, Shape } from 'three';
import _ from 'lodash';
import { extrudeSettings } from '../utils/extudeSetting';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';
import { degreeToWidth } from '../../../../../utils/helper';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
  textureLabel?: string;
};

const RectangleRounded: React.FC<PropsType> = (props: PropsType) => {
  const { thickness, x, y, pivot, isLine, textures, textureId } = props;
  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useEffect(() => {
    if (pivot === 'left') {
      setOffset([x / 2, 0, 0]);
    } else if (pivot === 'right') {
      setOffset([-x / 2, 0, 0]);
    }
  }, [pivot, x, y]);
  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const degree: number = 3;
  const shape: any = new Shape();
  const w: any = degreeToWidth(degree, y);
  const a2: any = 90 + degree;
  const a4: any = (180 - 90 - a2 / 2) * 2;
  const r: number = w / Math.tan(MathUtils.degToRad(a4 / 2));
  if (pivot === 'left') {
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2 - w, y / 2 - w);
    shape.absarc(
      x / 2 - r,
      y / 2 - w - r,
      r,
      MathUtils.degToRad(90),
      MathUtils.degToRad(90 - a4),
      true,
    );
    shape.lineTo(x / 2, -y / 2 + w + w);
    shape.absarc(
      x / 2 - r,
      -y / 2 + w + r,
      r,
      MathUtils.degToRad(0),
      MathUtils.degToRad(0 - a4),
      true,
    );
    shape.lineTo(-x / 2, -y / 2);
  }
  if (pivot === 'right') {
    shape.moveTo(-x / 2, -y / 2 + w + w);
    shape.lineTo(-x / 2, y / 2 - w - w);
    shape.absarc(
      -x / 2 + r,
      y / 2 - w - r,
      r,
      MathUtils.degToRad(180),
      MathUtils.degToRad(180 - a4),
      true,
    );
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2 + w, -y / 2 + w);
    shape.absarc(
      -x / 2 + r,
      -y / 2 + w + r,
      r,
      MathUtils.degToRad(180 + a4),
      MathUtils.degToRad(180),
      true,
    );
  }

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shape, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, -thickness * 0.3]}
            rotation={[0, -Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            scale={[x, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleRounded;
