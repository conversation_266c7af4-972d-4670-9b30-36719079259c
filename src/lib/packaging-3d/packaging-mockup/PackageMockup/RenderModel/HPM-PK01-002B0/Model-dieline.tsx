import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import _ from 'lodash';
import RectangleConnector from './shape/RectangleConnector';
import TrapezoidShape from './shape/Trapezoid';
import { degreeToWidth, getSizePercent } from '../../../../utils/helper';
import RectangleShape from './shape/RectangleShape';
import RectangleRounded from './shape/RectangleRounded';
import TriangleCurve from './shape/TriangleCurve';
import CameraDolly from './CameraDolly';
import { ModelSpecialDataType } from '../../spacialTypes';
import { Center, Line, Text } from '@react-three/drei';

const MainDieLineTrayBoxesB: React.FC = () => {
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness, initW, initL, initH, unit } =
    metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { gapWidth, connectW, holeW } = modelSpacialData.HPM_PK01_002B0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side, isDimension } = modelControls;
  const [textScale, setTextScale] = useState<{ x: number; y: number }>({
    x: 1,
    y: 1,
  });
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  useEffect(() => {
    const max: any = (height + width + length) / 3;
    setTextScale({ x: max / 20, y: max / 20 });
  }, [width, height, length]);

  useEffect(() => {
    cropSize();
  }, [width, length, side]);

  const cropSize = () => {
    const padding: any = 0.5;
    const totalX: any = width + 4 * height + 2 * gapWidth + 2 * holeW;
    const totalY: any = length * 2 + 3 * height;
    const paddingPercent: any = {
      x: getSizePercent({ value: padding, max: totalX }),
      y: getSizePercent({ value: padding, max: totalY }),
    };
    const widthPercentX: any = getSizePercent({ value: width, max: totalX });
    const heightPercentX: any = getSizePercent({ value: height, max: totalX });
    const heightPercentY: any = getSizePercent({ value: height, max: totalY });
    const lengthPercentY: any = getSizePercent({ value: length, max: totalY });
    const gapWidthPercentX: any = getSizePercent({
      value: gapWidth,
      max: totalX,
    });
    const gapHeightPercentY: any = getSizePercent({
      value: degreeToWidth(45, gapWidth),
      max: totalY,
    });
    const outConnectPercentY: any = getSizePercent({
      value: length - 2 * degreeToWidth(45, gapWidth),
      max: totalY,
    });
    const holeWPercentX: any = getSizePercent({ value: holeW, max: totalX });
    const zoneA: any =
      paddingPercent.x + holeWPercentX + gapWidthPercentX + 2 * heightPercentX;
    const outCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: zoneA - heightPercentX,
        y: paddingPercent.y,
        width: heightPercentX,
        height: heightPercentY,
      },
      {
        id: 2,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 3,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y,
        width: heightPercentX,
        height: heightPercentY,
      },
      {
        id: 4,
        unit: '%',
        x: zoneA - heightPercentX,
        y: paddingPercent.y + heightPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 5,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + heightPercentY,
        width: widthPercentX,
        height: lengthPercentY,
      },
      {
        id: 6,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + heightPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 7,
        unit: '%',
        x: zoneA - heightPercentX * 1.2,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 8,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 9,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 10,
        unit: '%',
        x: paddingPercent.x,
        y:
          paddingPercent.y +
          2 * heightPercentY +
          lengthPercentY +
          gapHeightPercentY,
        width: heightPercentX + holeWPercentX,
        height: outConnectPercentY,
      },
      {
        id: 11,
        unit: '%',
        x: paddingPercent.x + holeWPercentX + heightPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: gapWidthPercentX,
        height: lengthPercentY,
      },
      {
        id: 12,
        unit: '%',
        x: paddingPercent.x + holeWPercentX + heightPercentX + gapWidthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 13,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: widthPercentX,
        height: lengthPercentY,
      },
      {
        id: 14,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 15,
        unit: '%',
        x: zoneA + widthPercentX + heightPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: gapWidthPercentX,
        height: lengthPercentY,
      },
      {
        id: 16,
        unit: '%',
        x: zoneA + widthPercentX + heightPercentX + gapWidthPercentX,
        y:
          paddingPercent.y +
          2 * heightPercentY +
          lengthPercentY +
          gapHeightPercentY,
        width: heightPercentX + holeWPercentX,
        height: outConnectPercentY,
      },
      {
        id: 17,
        unit: '%',
        x: zoneA - heightPercentX * 1.2,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 18,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 19,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
    ];
    const inCropPositions: any[] = [
      {
        id: 3,
        unit: '%',
        x: zoneA - heightPercentX,
        y: paddingPercent.y,
        width: heightPercentX,
        height: heightPercentY,
      },
      {
        id: 2,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 1,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y,
        width: heightPercentX,
        height: heightPercentY,
      },
      {
        id: 6,
        unit: '%',
        x: zoneA - heightPercentX,
        y: paddingPercent.y + heightPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 5,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + heightPercentY,
        width: widthPercentX,
        height: lengthPercentY,
      },
      {
        id: 4,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + heightPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 9,
        unit: '%',
        x: zoneA - heightPercentX * 1.2,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 8,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 7,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + heightPercentY + lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 16,
        unit: '%',
        x: paddingPercent.x,
        y:
          paddingPercent.y +
          2 * heightPercentY +
          lengthPercentY +
          gapHeightPercentY,
        width: heightPercentX + holeWPercentX,
        height: outConnectPercentY,
      },
      {
        id: 15,
        unit: '%',
        x: paddingPercent.x + holeWPercentX + heightPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: gapWidthPercentX,
        height: lengthPercentY,
      },
      {
        id: 14,
        unit: '%',
        x: paddingPercent.x + holeWPercentX + heightPercentX + gapWidthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 13,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: widthPercentX,
        height: lengthPercentY,
      },
      {
        id: 12,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: heightPercentX,
        height: lengthPercentY,
      },
      {
        id: 11,
        unit: '%',
        x: zoneA + widthPercentX + heightPercentX,
        y: paddingPercent.y + 2 * heightPercentY + lengthPercentY,
        width: gapWidthPercentX,
        height: lengthPercentY,
      },
      {
        id: 10,
        unit: '%',
        x: zoneA + widthPercentX + heightPercentX + gapWidthPercentX,
        y:
          paddingPercent.y +
          2 * heightPercentY +
          lengthPercentY +
          gapHeightPercentY,
        width: heightPercentX + holeWPercentX,
        height: outConnectPercentY,
      },
      {
        id: 19,
        unit: '%',
        x: zoneA - heightPercentX * 1.2,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
      {
        id: 18,
        unit: '%',
        x: zoneA,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: widthPercentX,
        height: heightPercentY,
      },
      {
        id: 17,
        unit: '%',
        x: zoneA + widthPercentX,
        y: paddingPercent.y + 2 * heightPercentY + 2 * lengthPercentY,
        width: heightPercentX * 1.2,
        height: heightPercentY,
      },
    ];
    setModelInfo({
      ...modelInfo,
      cropPositions: {
        ...modelInfo.cropPositions,
        in: inCropPositions,
        out: outCropPositions,
      },
    });
  };

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    // const gap: any = degreeToWidth(45, height * 0.025);
    const totalX: any = width + 4 * height + 2 * gapWidth + 2 * holeW;
    const totalY: any = length * 2 + 3 * height;
    // const gapY: any = length * 0;
    const w = totalX;
    const h = totalY;
    // const h = totalY + 2 * gapY;
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group position={[0, -(length / 2 + height / 2), 0]}>
      <CameraDolly />
      <group position={[0, 0, 0]} rotation={[0, 0, 0]}>
        <group name="G1" position={[0, length / 2, 0]}>
          <group name="G1-1" position={[0, height / 2, 0]}>
            <group name="G1-1-1" position={[-width / 2, 0, 0]}>
              <RectangleShape
                textureId={0}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G1-1-0">
              <RectangleShape
                textureId={0}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={true}
                textures={textures}
              />
              {isDimension && (
                <>
                  <Line
                    points={[
                      [-width / 8, -height / 2, 0],
                      [-width / 8, height / 2, 0],
                    ]}
                    color="red"
                    lineWidth={1}
                    dashScale={1.5}
                    segments
                    dashed={true}
                  />
                  <Center position={[width / 8, 0, 0]}>
                    <Text
                      fontSize={1}
                      scale={[textScale.x, textScale.y, 1]}
                      color={'red'}
                    >
                      {`H : ${initH} ${unit}`}
                    </Text>
                  </Center>
                </>
              )}
            </group>
            <group name="G1-1-2" position={[width / 2, 0, 0]}>
              <RectangleShape
                textureId={0}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={true}
                textures={textures}
              />
            </group>
          </group>
          <group name="G1-2" position={[0, height, 0]}>
            <group name="G1-2-1" position={[0, length / 2, 0]}>
              <group name="G1-2-1-1" position={[-width / 2, 0, 0]}>
                <RectangleRounded
                  textureId={0}
                  pivot={'right'}
                  thickness={thickness}
                  x={height}
                  y={length}
                  isLine={true}
                  textures={textures}
                />
              </group>
              <group name="G1-2-1-0">
                <RectangleShape
                  textureId={0}
                  pivot={'center'}
                  thickness={thickness}
                  x={width}
                  y={length}
                  isLine={true}
                  textures={textures}
                />
                {isDimension && (
                  <>
                    <Line
                      points={[
                        [-width / 2, height / 8, 0],
                        [width / 2, height / 8, 0],
                      ]}
                      color="green"
                      lineWidth={1}
                      dashScale={1.3}
                      segments
                      dashed={true}
                    />
                    <Center>
                      <Text
                        fontSize={1}
                        scale={[textScale.x, textScale.y, 1]}
                        color={'green'}
                      >
                        {`W : ${initW} ${unit}`}
                      </Text>
                    </Center>
                  </>
                )}
              </group>
              <group name="G1-2-1-2" position={[width / 2, 0, 0]}>
                <RectangleRounded
                  textureId={0}
                  pivot={'left'}
                  thickness={thickness}
                  x={height}
                  y={length}
                  isLine={true}
                  textures={textures}
                />
              </group>
              <group name="G1-2-2" position={[0, length / 2, 0]}>
                <group position={[0, height / 2, 0]}>
                  <group name="G1-2-2-1" position={[-width / 2, 0, 0]}>
                    <TriangleCurve
                      textureId={0}
                      pivot={'right'}
                      thickness={thickness}
                      x={height}
                      y={height}
                      isLine={true}
                      textures={textures}
                    />
                  </group>
                  <group name="G1-2-2-0">
                    <RectangleShape
                      textureId={0}
                      pivot={'center'}
                      thickness={thickness}
                      x={width}
                      y={height}
                      isLine={true}
                      textures={textures}
                    />
                  </group>
                  <group name="G1-2-2-2" position={[width / 2, 0, 0]}>
                    <TriangleCurve
                      textureId={0}
                      pivot={'left'}
                      thickness={thickness}
                      x={height}
                      y={height}
                      isLine={true}
                      textures={textures}
                    />
                  </group>
                </group>
              </group>
            </group>
          </group>
        </group>
        <group name="G2">
          <group name="G2-1" position={[-width / 2, 0, 0]}>
            <group name="G2-1-1" position={[-height - gapWidth, 0, 0]}>
              <RectangleConnector
                textureId={0}
                pivot="out-right"
                isLine={true}
                thickness={thickness}
                x={height}
                y={length - 2 * degreeToWidth(45, gapWidth)}
                aX={length * 0.2 - degreeToWidth(45, gapWidth)}
                connectW={connectW}
                holeW={holeW}
                textures={textures}
              />
            </group>
            <group name="G2-1-2" position={[-height, 0, 0]}>
              <TrapezoidShape
                textureId={0}
                pivot={'right'}
                x={gapWidth}
                y={length}
                thickness={thickness}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G2-1-0" position={[0, 0, 0]}>
              <RectangleShape
                textureId={0}
                pivot={'right'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={true}
                textures={textures}
              />
            </group>
          </group>
          <group name="G2-0">
            <RectangleConnector
              textureId={0}
              pivot="in-left-right"
              isLine={true}
              thickness={thickness}
              x={width}
              y={length}
              aX={length * 0.2}
              connectW={connectW}
              holeW={holeW}
              textures={textures}
            />
            {isDimension && (
              <>
                <Line
                  points={[
                    [width / 8, -length / 2, 0],
                    [width / 8, length / 2, 0],
                  ]}
                  color="blue"
                  lineWidth={1}
                  dashScale={1.3}
                  segments
                  dashed={true}
                />
                <Center position={[-width / 8, 0, 0]}>
                  <Text
                    fontSize={1}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'blue'}
                  >
                    {`L : ${initL} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group name="G2-2" position={[width / 2, 0, 0]}>
            <group name="G2-2-0">
              <RectangleShape
                textureId={0}
                pivot={'left'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G2-2-2" position={[height, 0, 0]}>
              <TrapezoidShape
                textureId={0}
                pivot={'left'}
                x={gapWidth}
                y={length}
                thickness={thickness}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G2-2-1" position={[height + gapWidth, 0, 0]}>
              <RectangleConnector
                textureId={0}
                pivot="out-left"
                isLine={true}
                thickness={thickness}
                x={height}
                y={length - 2 * degreeToWidth(45, gapWidth)}
                aX={length * 0.2 - degreeToWidth(45, gapWidth)}
                connectW={connectW}
                holeW={holeW}
                textures={textures}
              />
            </group>
          </group>
        </group>
        <group name="G3" position={[0, -length / 2, 0]}>
          <group position={[0, -height / 2, 0]}>
            <group name="G3-1" position={[-width / 2, 0, 0]}>
              <RectangleShape
                textureId={0}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G3-0">
              <RectangleShape
                textureId={0}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={true}
                textures={textures}
              />
            </group>
            <group name="G3-2" position={[width / 2, 0, 0]}>
              <RectangleShape
                textureId={0}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={true}
                textures={textures}
              />
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default MainDieLineTrayBoxesB;
