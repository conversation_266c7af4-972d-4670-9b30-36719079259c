import React, { useEffect, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import gsap from 'gsap';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';
import RectangleShape from './shape/RectangleShape';
import RectangleRounded from './shape/RectangleRounded';

const Main3DTrayBoxesA: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c } = modelSpacialData.HPM_PK01_002A0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, isPlay } = modelControls;
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  // Ref for animation
  const RefModel = useRef<any>();
  const Ref1_G3_1 = useRef<any>();
  const Ref1_G3_2 = useRef<any>();
  const Ref2_G3 = useRef<any>();
  const Ref3_G1_1_1 = useRef<any>();
  const Ref3_G1_1_2 = useRef<any>();
  const Ref4_G1 = useRef<any>();
  const Ref5_G2_1 = useRef<any>();
  const Ref5_G2_2 = useRef<any>();
  const Ref6_G2_1_1 = useRef<any>();
  const Ref6_G2_2_1 = useRef<any>();
  const Ref8_G1_2 = useRef<any>();
  const Ref10_G1_2_2 = useRef<any>();
  const duration: number = 2;
  const getPos = (state: number) => {
    return state * duration;
  };

  const [tl] = useState(gsap.timeline({ repeat: -1 }));

  useEffect(() => {
    tl.set(RefModel.current.rotation, { x: -Math.PI / 2, duration: 0 });
    tl.set(Ref2_G3.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref1_G3_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref1_G3_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G1_1_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref3_G1_1_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref4_G1.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref5_G2_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref5_G2_2.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_1_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref6_G2_2_1.current.rotation, { y: 0, duration: 0 }, 0);
    tl.set(Ref8_G1_2.current.rotation, { x: 0, duration: 0 }, 0);
    tl.set(Ref10_G1_2_2.current.rotation, { x: 0, duration: 0 }, 0);
    // to close
    tl.to(
      Ref1_G3_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref1_G3_2.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(0),
    );
    tl.to(
      Ref2_G3.current.rotation,
      { x: -Math.PI / 2, duration: duration },
      getPos(1),
    );
    tl.to(
      Ref3_G1_1_1.current.rotation,
      { y: (Math.PI / 2) * 1.05, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref3_G1_1_2.current.rotation,
      { y: (-Math.PI / 2) * 1.05, duration: duration },
      getPos(2),
    );
    tl.to(
      Ref4_G1.current.rotation,
      { x: Math.PI / 2, duration: duration },
      getPos(3),
    );
    tl.to(
      Ref5_G2_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(4),
    );
    tl.to(
      Ref5_G2_2.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(4),
    );
    tl.to(
      Ref6_G2_1_1.current.rotation,
      { y: Math.PI / 2, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref6_G2_2_1.current.rotation,
      { y: -Math.PI / 2, duration: duration },
      getPos(5),
    );
    tl.to(
      Ref8_G1_2.current.rotation,
      { x: Math.PI / 2, duration: duration },
      getPos(7),
    );
    tl.to(
      Ref10_G1_2_2.current.rotation,
      { x: (Math.PI / 2) * 1.05, duration: duration },
      getPos(6),
    );
    tl.paused(!isPlay);
    if (!isPlay) {
      tl.progress(actionState / 100);
    }
  }, [actionState, isPlay]);

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width + 4 * height;
    const totalY: any = length * 2 + 3 * height;
    const gapY: any = length * 0.005;
    const spacing: any = c * 0.05;
    const w = totalX;
    const h = totalY + 2 * (c + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group position={[-(width / 2), -height / 2, length / 2]}>
      <group ref={RefModel} position={[0, 0, 0]} rotation={[0, 0, 0]}>
        <group name="G1" position={[0, length / 2, 0]} ref={Ref4_G1}>
          <group name="G1-1" position={[0, height / 2, 0]}>
            <group
              name="G1-1-1"
              position={[-width / 2, 0, 0]}
              ref={Ref3_G1_1_1}
            >
              <RectangleShape
                textureId={9}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G1-1-0">
              <RectangleShape
                textureId={8}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G1-1-2" position={[width / 2, 0, 0]} ref={Ref3_G1_1_2}>
              <RectangleShape
                textureId={7}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
          <group name="G1-2" position={[0, height, 0]} ref={Ref8_G1_2}>
            <group name="G1-2-1" position={[0, length / 2, 0]}>
              <group name="G1-2-1-0">
                <RectangleShape
                  textureId={5}
                  pivot={'center'}
                  thickness={thickness}
                  x={width}
                  y={length}
                  isLine={false}
                  textures={textures}
                />
              </group>
              <group
                name="G1-2-2"
                position={[0, length / 2, 0]}
                ref={Ref10_G1_2_2}
              >
                <group position={[0, 0, 0]}>
                  <group name="G1-2-2-0">
                    <RectangleRounded
                      textureId={2}
                      pivot={'bottom'}
                      thickness={thickness}
                      x={width}
                      y={height * 0.4}
                      isLine={false}
                      textures={textures}
                    />
                  </group>
                </group>
              </group>
            </group>
          </group>
        </group>
        <group name="G2">
          <group name="G2-1" position={[-width / 2, 0, 0]} ref={Ref5_G2_1}>
            <group name="G2-1-1" position={[-height, 0, 0]} ref={Ref6_G2_1_1}>
              <RectangleRounded
                textureId={15}
                pivot={'right'}
                thickness={thickness}
                x={width * 0.1}
                y={length}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G2-1-0" position={[0, 0, 0]}>
              <RectangleShape
                textureId={14}
                pivot={'right'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
          <group name="G2-0">
            <RectangleShape
              textureId={13}
              pivot="center"
              isLine={false}
              thickness={thickness}
              x={width}
              y={length}
              textures={textures}
            />
          </group>
          <group
            name="G2-2"
            position={[width / 2, 0, 0]}
            rotation={[0, -Math.PI / 2, 0]}
            ref={Ref5_G2_2}
          >
            <group name="G2-2-0">
              <RectangleShape
                textureId={12}
                pivot={'left'}
                x={height}
                y={length}
                thickness={thickness}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G2-2-1" position={[height, 0, 0]} ref={Ref6_G2_2_1}>
              <RectangleRounded
                textureId={11}
                pivot={'left'}
                thickness={thickness}
                x={width * 0.1}
                y={length}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
        </group>
        <group position={[0, -length / 2, 0]} name="G3" ref={Ref2_G3}>
          <group position={[0, -height / 2, 0]}>
            <group name="G3-1" position={[-width / 2, 0, 0]} ref={Ref1_G3_1}>
              <RectangleShape
                textureId={19}
                pivot={'right'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G3-0">
              <RectangleShape
                textureId={18}
                pivot={'center'}
                thickness={thickness}
                x={width}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
            <group name="G3-2" position={[width / 2, 0, 0]} ref={Ref1_G3_2}>
              <RectangleShape
                textureId={17}
                pivot={'left'}
                thickness={thickness}
                x={height * 0.9}
                y={height}
                isLine={false}
                textures={textures}
              />
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default Main3DTrayBoxesA;
