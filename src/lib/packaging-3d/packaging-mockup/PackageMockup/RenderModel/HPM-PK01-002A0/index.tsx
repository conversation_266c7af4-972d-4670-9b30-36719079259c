import { useRecoilState } from 'recoil';
import { modelControlsState, modelInfoState } from '../index';
import { ModelControlsType, ModelInfoType } from '../../types';
import { ContactShadows } from '@react-three/drei';
import React from 'react';
import DimensionLabel from '../DimensionLabel';
import * as THREE from 'three';
import MainDieLineTrayBoxesA from './Model-dieline';
import Main3DTrayBoxesA from './Model-3d';

type propsType = {
  type: '3d' | '2d';
};

const HPMPK01002A0: React.FC<propsType> = ({ type }: propsType) => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height, length } = modelInfo.metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, isDimension, isPlay } = modelControls;
  if (type === '2d') {
    return <MainDieLineTrayBoxesA />;
  }
  if (type === '3d') {
    return (
      <group>
        {isDimension && actionState === 100 && !isPlay && (
          <group>
            <DimensionLabel
              origin={new THREE.Vector3(width / 2, -height / 2.01, -length / 2)}
              yConfig={{ label: 'H', color: '#06BA31', length: height + 30 }}
              xConfig={{ label: 'W', color: '#FF590F', length: width + 30 }}
              zConfig={{ label: 'L', color: '#605DEC', length: length + 30 }}
            />
          </group>
        )}
        <group position={[width / 2, 0, -length / 2]}>
          <Main3DTrayBoxesA />
        </group>
        {actionState === 100 && (
          <ContactShadows
            opacity={0.5}
            color="black"
            position={[0, -height / 1.5, 0]}
            scale={2 * width + 2 * length}
            blur={4}
            far={100000}
          />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK01002A0;
