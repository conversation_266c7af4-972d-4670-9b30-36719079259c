import { ContactShadows } from '@react-three/drei';
import React from 'react';
import { useRecoilState } from 'recoil';
import { ModelControlsType, ModelInfoType } from '../../types';
import {
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import MainDieLineShoppingBagA1 from './Model-dieline';
import Main3DShoppingBagA1 from './Model-3d';
import DimensionLabel from '../DimensionLabel';
import * as THREE from 'three';

type propsType = {
  type: '3d' | '2d';
};

const HPMPK02001A1: React.FC<propsType> = ({ type }: propsType) => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height, length } = modelInfo.metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState, isDimension, isPlay } = modelControls;
  const { HPM_PK02_001A1 } = modelSpacialData;

  if (type === '2d') {
    return <MainDieLineShoppingBagA1 />;
  }
  if (type === '3d') {
    return (
      <group>
        {isDimension && actionState === 100 && !isPlay && (
          <group>
            <DimensionLabel
              origin={new THREE.Vector3(length / 2, -height / 2.01, -width / 2)}
              yConfig={{ label: 'H', color: '#06BA31', length: height + 20 }}
              xConfig={{ label: 'L', color: '#605DEC', length: length + 20 }}
              zConfig={{ label: 'W', color: '#FF590F', length: width + 20 }}
            />
          </group>
        )}
        <group position={[0, 0, -width / 2]}>
          <Main3DShoppingBagA1 />
        </group>
        {actionState === 100 && (
          <ContactShadows
            opacity={0}
            color="black"
            position={[0, -height / 1.5, 0]}
            scale={2 * width + 2 * length + HPM_PK02_001A1.g}
            blur={4}
            far={100000}
          />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK02001A1;
