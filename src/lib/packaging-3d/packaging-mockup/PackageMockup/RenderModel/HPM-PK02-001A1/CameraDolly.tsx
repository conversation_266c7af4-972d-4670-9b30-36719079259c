import { Vector3 } from 'three';
import { useFrame } from '@react-three/fiber';
import _ from 'lodash';
import React from 'react';
import { useRecoilState } from 'recoil';
import { ModelInfoType } from '../../types';
import { modelInfoState, modelSpacialDataState } from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';

const CameraDolly: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, length, height } = modelInfo.metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { g, topHeight, bottomHeight } = modelSpacialData.HPM_PK02_001A1;
  const vec = new Vector3();
  useFrame((state: any) => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = height + topHeight + bottomHeight;
    const padding: any = 1;
    state.camera.left = -totalX / 2 - padding;
    state.camera.right = totalX / 2 + padding;
    state.camera.top = totalY / 2 + padding;
    state.camera.bottom = -totalY / 2 - padding;
    state.camera.zoom = 1;
    const max: any = _.max([totalX, totalY]);
    const step = 0.1;
    const x = 0;
    const y = 0;
    const z = max * 0.75;
    state.camera.position.lerp(vec.set(x, y, z), step);
    state.camera.lookAt(0, 0, 0);
    state.camera.updateProjectionMatrix();
  });
  return null;
};

export default CameraDolly;
