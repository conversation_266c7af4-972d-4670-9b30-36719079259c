import React, { useLayoutEffect, useMemo, useState } from 'react';
import { Path, Shape } from 'three';
import { extrudeSettings } from '../utils/extudeSetting';
import { Decal, Extrude, useTexture } from '@react-three/drei';
import _ from 'lodash';
import {
  BaseMaterial,
  InSideMaterial,
  OutSideMaterial,
} from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  x: number;
  y: number;
  aX: number;
  aY: number;
  aRadius: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};
const RectangleHole: React.FC<PropsType> = (props: PropsType) => {
  const {
    thickness,
    x,
    y,
    aX,
    aY,
    aRadius,
    pivot,
    isLine,
    textures,
    textureId,
  } = props;

  const [offset, setOffset] = useState<any>([0, 0, 0]);
  useLayoutEffect(() => {
    if (pivot === 'center') {
      setOffset([0, 0, 0]);
    } else if (pivot === 'left') {
      setOffset([x / 2, 0, 0]);
    } else if (pivot === 'top') {
      setOffset([0, -y / 2, 0]);
    } else if (pivot === 'right') {
      setOffset([-x / 2, 0, 0]);
    } else if (pivot === 'bottom') {
      setOffset([0, y / 2, 0]);
    }
  }, [pivot, x, y, isLine]);

  const getTextureOut = (id: any) => {
    const texture: any = _.find(textures?.out, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const getTextureIn = (id: any) => {
    const texture: any = _.find(textures?.in, (o) => {
      return Number(o.id) === Number(id);
    });
    if (texture) {
      return texture.base64;
    }
    return null;
  };
  const texture_out: any = useTexture(
    getTextureOut(textureId) || '/texture/paper-texture.jpg',
  );
  const texture_in: any = useTexture(
    getTextureIn(textureId) || '/texture/paper-texture.jpg',
  );
  const shapes3D: any = useMemo(() => {
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    const hole1: any = new Path();
    const hole2: any = new Path();
    hole1.absarc(-aX, aY, aRadius, 0, 2 * Math.PI, false);
    hole2.absarc(aX, aY, aRadius, 0, 2 * Math.PI, false);
    shape.holes = [hole1, hole2];
    return shape;
  }, [x, y]);
  const shapes: any = useMemo(() => {
    const shape: any = new Shape();
    shape.moveTo(-x / 2, -y / 2);
    shape.lineTo(-x / 2, y / 2);
    shape.lineTo(x / 2, y / 2);
    shape.lineTo(x / 2, -y / 2);
    shape.lineTo(-x / 2, -y / 2);
    return new Shape(shape.getPoints(10));
  }, [x, y]);
  const holeA: any = useMemo(() => {
    const hole: any = new Shape();
    hole.moveTo(0, 0);
    hole.absarc(-aX, aY, aRadius, 0, 2 * Math.PI, false);
    return new Shape(hole.getPoints(10));
  }, [x, y, isLine]);
  const holeB: any = useMemo(() => {
    const hole: any = new Shape();
    hole.moveTo(0, 0);
    hole.absarc(aX, aY, aRadius, 0, 2 * Math.PI, false);
    return new Shape(hole.getPoints(10));
  }, [x, y, isLine]);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          position={offset}
          args={[shapes3D, extrudeSettings({ isLine, thickness })]}
        >
          <Decal
            // debug
            position={[0, 0, thickness * 0.3]}
            rotation={[0, -Math.PI, 0]}
            scale={[x, y, thickness]}
            map={getTextureOut(textureId) ? texture_out : null}
          >
            <OutSideMaterial />
            {/* <DimensionMaterial text={textureLabel} /> */}
          </Decal>
          {/* inside */}
          <Decal
            // debug
            position={[0, 0, thickness * 1.3]}
            rotation={[0, 0, 0]}
            scale={[x, y, thickness]}
            map={getTextureIn(textureId) ? texture_in : null}
          >
            <InSideMaterial isTexture={getTextureIn(textureId)} />
          </Decal>
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh position={offset}>
          <LineByShape shape={shapes} />
          <LineByShape shape={holeA} />
          <LineByShape shape={holeB} />
        </mesh>
      )}
    </group>
  );
};

export default RectangleHole;
