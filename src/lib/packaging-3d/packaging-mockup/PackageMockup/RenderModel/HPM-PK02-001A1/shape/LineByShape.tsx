import React, { useEffect, useRef } from 'react';
import { Vector3 } from 'three';

type propsType = {
  shape: any;
};

export const LineByShape: React.FC<propsType> = ({ shape }: propsType) => {
  const refLine = useRef<any>();
  const points = shape.getPoints();
  const vertices = points.map((point: any) => new Vector3(point.x, point.y, 0));

  useEffect(() => {
    if (refLine.current) {
      refLine.current.geometry.setFromPoints(vertices);
    }
  }, [vertices]);

  return (
    <line ref={refLine}>
      <bufferGeometry />
      <lineBasicMaterial
        attach="material"
        color={'#0d0082'}
        linewidth={2}
        linecap={'round'}
        linejoin={'round'}
      />
    </line>
    // <Extrude
    //   castShadow
    //   args={[shape, extrudeSettings({ isLine: false, thickness: 0.01 })]}
    // >
    //   <BaseMaterial />
    // </Extrude>
  );
};
