import React from 'react';
import { Extrude } from '@react-three/drei';
import { Shape } from 'three';
import { BaseMaterial } from '../utils/material';
import { LineByShape } from './LineByShape';

type PropsType = {
  thickness: number;
  r: number;
  pivot?: any;
  isLine?: any;
  textures?: any;
  textureId?: any;
};

const CircleShape: React.FC<PropsType> = (props: PropsType) => {
  const { r, isLine } = props;

  const shape: any = new Shape();
  shape.moveTo(0, 0);
  shape.absarc(0, 0, r, 0, 2 * Math.PI, false);

  return (
    <group>
      {!isLine ? (
        <Extrude
          castShadow
          args={[
            shape,
            {
              curveSegments: 50,
              steps: 1,
              depth: 0,
              bevelEnabled: false,
            },
          ]}
        >
          <BaseMaterial />
        </Extrude>
      ) : (
        <mesh>
          <LineByShape shape={shape} />
        </mesh>
      )}
    </group>
  );
};

export default CircleShape;
