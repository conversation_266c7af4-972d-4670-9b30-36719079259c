import { useRecoilState } from 'recoil';
import { modelControlsState } from '../index';
import { ModelControlsType } from '../../types';
import React from 'react';
import Main3DStickerCircle from './Model-3d';
import MainDieLineStickerCircle from './Model-dieline';

type propsType = {
  type: '3d' | '2d';
};

const HPMPK04001A0: React.FC<propsType> = ({ type }: propsType) => {
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState } = modelControls;

  if (type === '2d') {
    return <MainDieLineStickerCircle />;
  }
  if (type === '3d') {
    return (
      <group>
        <group>
          <Main3DStickerCircle />
        </group>
        {actionState === 100 && (
          <></>
          // <ContactShadows
          //   opacity={0.5}
          //   color="black"
          //   position={[0, -height / 2, 0]}
          //   scale={2 * width + 2 * length}
          //   blur={4}
          //   far={100000}
          // />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK04001A0;
