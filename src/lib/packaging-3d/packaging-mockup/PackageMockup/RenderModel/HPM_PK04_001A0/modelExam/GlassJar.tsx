import React, { useState } from 'react';
import { useGLTF } from '@react-three/drei';
import { GLTF } from 'three-stdlib';
import * as THREE from 'three';

type GLTFResult = GLTF & {
  nodes: {
    ['Node-Mesh']: THREE.Mesh;
    ['Node-Mesh_1']: THREE.Mesh;
    ['Node-Mesh_2']: THREE.Mesh;
  };
  materials: {
    mat25: THREE.MeshStandardMaterial;
    mat24: THREE.MeshStandardMaterial;
    mat15: THREE.MeshStandardMaterial;
  };
};
const GlassJar: React.FC = () => {
  const { nodes, materials } = useGLTF('/model/Glass-Jar.glb') as GLTFResult;

  const [scale] = useState(200);

  return (
    <group dispose={null} scale={scale}>
      <mesh geometry={nodes['Node-Mesh'].geometry} material={materials.mat25} />
      <mesh
        geometry={nodes['Node-Mesh_1'].geometry}
        material={materials.mat24}
      />
      <mesh
        geometry={nodes['Node-Mesh_2'].geometry}
        material={materials.mat15}
      />
    </group>
  );
};

export default GlassJar;
