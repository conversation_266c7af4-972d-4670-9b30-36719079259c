import React, { useState } from 'react';
import { Decal, useGLTF, useTexture } from '@react-three/drei';
import { GLTF } from 'three-stdlib';
import * as THREE from 'three';

type GLTFResult = GLTF & {
  nodes: {
    Bottle: THREE.Mesh;
    stopper: THREE.Mesh;
    water: THREE.Mesh;
  };
  materials: {
    Glass: THREE.MeshStandardMaterial;
    Material: THREE.MeshStandardMaterial;
    Juice: THREE.MeshStandardMaterial;
  };
};
const GlassBottle: React.FC = () => {
  const { nodes, materials } = useGLTF(
    '/model/Carafe_with_stopper.glb',
  ) as GLTFResult;
  const texture: any = useTexture('/texture/locablock-logo.jpeg');
  const [scale] = useState(400);
  // const [bgColor] = useState<any>(new Color(0xffffff));

  return (
    <group dispose={null} scale={scale}>
      <mesh geometry={nodes.Bottle.geometry}>
        <meshPhysicalMaterial
          transparent
          color={'#fffff'}
          // color={isTexture ? '#ca9a69' : '#ca9a69'}
          transmission={1}
          roughness={0}
          clearcoat={0.5}
          // metalness={0.75}
          toneMapped={false}
        />
        <Decal
          // debug
          position={[0, 0, 0.1 / 2]}
          rotation={[0, 0, 0]}
          scale={[0.1, 0.1, 0.1]}
        >
          <meshPhysicalMaterial
            // transparent
            // color={'#fffff'}
            // color={isTexture ? '#ca9a69' : '#ca9a69'}
            map={texture}
            polygonOffset
            polygonOffsetFactor={-10}
            // transmission={1}
            // roughness={0}
            // clearcoat={0.5}
            // metalness={0.75}
            toneMapped={false}
          />
          {/*  <RenderTexture attach="map" anisotropy={16}> */}
          {/*    <color */}
          {/*      attach="background" */}
          {/*      args={[bgColor.r, bgColor.g, bgColor.b]} */}
          {/*    /> */}
          {/*    <Image */}
          {/*      url="/texture/locablock-logo.jpeg" */}
          {/*      transparent */}
          {/*      opacity={1} */}
          {/*      scale={7} */}
          {/*    /> */}
          {/*  </RenderTexture> */}
          {/* </meshPhysicalMaterial> */}
        </Decal>
      </mesh>
      <mesh
        geometry={nodes.stopper.geometry}
        material={materials.Material}
        position={[0, 0.129, 0]}
        scale={0.985}
      />
      <mesh
        geometry={nodes.water.geometry}
        material={materials.Juice}
        position={[0, -0.006, 0]}
        scale={0.923}
      />
    </group>
  );
};

export default GlassBottle;
