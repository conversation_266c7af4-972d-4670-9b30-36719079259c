import { Vector3 } from 'three';
import { useFrame } from '@react-three/fiber';
import _ from 'lodash';
import React from 'react';
import { useRecoilState } from 'recoil';
import { ModelInfoType } from '../../types';
import { modelInfoState, modelSpacialDataState } from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';

const CameraDolly: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, length, height } = modelInfo.metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c, g } = modelSpacialData.HPM_PK01_002A0;
  const vec = new Vector3();
  useFrame((state: any) => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = width * 2 + height + c * 2;
    // const gapY: any = length * 0.005;
    // const spacing: any = c * 0.05;
    const padding: any = 0.5;
    state.camera.left = -totalX / 2 - padding;
    state.camera.right = totalX / 2 + padding;
    // state.camera.top = totalY / 2 + c + spacing + gapY;
    // state.camera.bottom = -totalY / 2 - c - spacing - gapY;
    state.camera.top = totalY / 2 + padding;
    state.camera.bottom = -totalY / 2 - padding;
    state.camera.zoom = 1;
    const total_x: any = 2 * width + 2 * length;
    const total_y: any = 2 * width + height;
    const max: any = _.max([total_x, total_y]);
    const step = 0.1;
    const x = 0;
    const y = 0;
    const z = max * 0.75;
    state.camera.position.lerp(vec.set(x, y, z), step);
    // state.camera.position.lerp(vec.set(0, 0, z), step);
    state.camera.lookAt(0, 0, 0);
    state.camera.updateProjectionMatrix();
  });
  return null;
};

export default CameraDolly;
