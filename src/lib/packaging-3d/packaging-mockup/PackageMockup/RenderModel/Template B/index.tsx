import Main3DTuckEndBoxesDoubleTray from './Model-3d';
import { useRecoilState } from 'recoil';
import {
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import { ModelControlsType, ModelInfoType } from '../../types';
import { ContactShadows } from '@react-three/drei';
import React from 'react';
import MainDieLineTrayBoxesA from './Model-dieline';

type propsType = {
  type: '3d' | '2d';
};

const HPMPK01002A0: React.FC<propsType> = ({ type }: propsType) => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height, length } = modelInfo.metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { actionState } = modelControls;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { HPM_PK01_002A0 } = modelSpacialData;

  if (type === '2d') {
    return <MainDieLineTrayBoxesA />;
  }
  if (type === '3d') {
    return (
      <group>
        <group position={[-(HPM_PK01_002A0.g / 2 - width / 2), 0, -length / 2]}>
          <Main3DTuckEndBoxesDoubleTray />
        </group>
        {actionState === 100 && (
          <ContactShadows
            opacity={0.5}
            color="black"
            position={[0, -height / 1.5, 0]}
            scale={2 * width + 2 * length + HPM_PK01_002A0.g}
            blur={4}
            far={100000}
          />
        )}
      </group>
    );
  }
  return null;
};

export default HPMPK01002A0;
