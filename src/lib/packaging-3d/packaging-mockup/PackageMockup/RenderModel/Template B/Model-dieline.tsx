import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import {
  CanvasSizeType,
  FileFormatType,
  ModelControlsType,
  ModelInfoType,
} from '../../types';
import {
  canvasSizeState,
  fileFormatState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import _ from 'lodash';
import CameraDolly from './CameraDolly';

const MainDieLineTrayBoxesA: React.FC = () => {
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata } = modelInfo;
  const { width, length, height } = metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { c, g } = modelSpacialData.HPM_PK01_002A0;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side } = modelControls;

  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);
  const [fileFormat] = useRecoilState<FileFormatType>(fileFormatState);

  useEffect(() => {
    // const max: any = _.max([width, height, length]);
  }, [width, height, length]);

  useEffect(() => {
    cropSize();
  }, [width, length, g, c, side]);

  const cropSize = () => {
    // const padding: any = 0;
    // const totalX: any = width * 2 + length * 2 + g + 2 * padding;
    // const totalY: any = width * 2 + height + c * 2 + 2 * padding;
    // const paddingPercent: any = {
    //   x: getSizePercent({ value: padding, max: totalX }),
    //   y: getSizePercent({ value: padding, max: totalY }),
    // };
    // const widthPercentX: any = getSizePercent({ value: width, max: totalX });
    // const widthPercentY: any = getSizePercent({ value: width, max: totalY });
    // const heightPercent: any = getSizePercent({ value: height, max: totalY });
    // const lengthPercentX: any = getSizePercent({ value: length, max: totalX });
    // const lengthPercentY: any = getSizePercent({ value: length, max: totalY });
    // const gPercent: any = getSizePercent({ value: g, max: totalX });
    // const cPercent: any = getSizePercent({ value: c, max: totalY });
    const inCropPositions: any[] = [];
    const outCropPositions: any[] = [];
    setModelInfo({
      ...modelInfo,
      cropPositions: {
        ...modelInfo.cropPositions,
        in: inCropPositions,
        out: outCropPositions,
      },
    });
  };

  useEffect(() => {
    getCanvasSize();
  }, [fileFormat, width, length, height]);

  const getCanvasSize = () => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = width * 2 + height;
    const gapY: any = length * 0.005;
    const spacing: any = c * 0.05;
    const w = totalX;
    const h = totalY + 2 * (c + spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  return (
    <group position={[0, 0, 0]}>
      <CameraDolly />
      <group position={[0, 0, 0]} rotation={[0, 0, 0]}></group>
    </group>
  );
};

export default MainDieLineTrayBoxesA;
