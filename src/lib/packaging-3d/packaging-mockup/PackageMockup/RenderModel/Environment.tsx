import React, { Fragment, useState } from 'react';
import {
  MapControls,
  OrbitControls,
  PerspectiveCamera,
} from '@react-three/drei';
import { useRecoilState } from 'recoil';
import { ModelControlsType, ModelInfoType } from '../types';
import { modelControlsState, modelInfoState } from './index';

const Env: React.FC = () => {
  const [modelInfo] = useRecoilState<ModelInfoType>(modelInfoState);
  const { width, height, length } = modelInfo.metadata;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { isFreeControl, isMapControl } = modelControls;
  // const [bgColor] = useState<any>(new Color(0xffffff));
  const [intensity] = useState<number>(7.5);

  return (
    <Fragment>
      <directionalLight
        position={[0, height * 5, 0]}
        intensity={intensity * 1.5}
      />
      <directionalLight
        position={[-width * 5, 0, 0]}
        intensity={intensity * 0.9}
      />
      <directionalLight
        position={[width * 5, 0, 0]}
        intensity={intensity * 0.75}
      />
      <directionalLight
        position={[0, 0, length * 5]}
        intensity={intensity * 0.5}
      />
      <directionalLight
        position={[0, 0, -length * 5]}
        intensity={intensity * 0.5}
      />
      <directionalLight
        position={[0, -height * 5, 0]}
        intensity={intensity * 0.75}
      />
      <PerspectiveCamera
        position={[-100 - 4 * width, 70 + 4 * height, 125 + 4 * length]}
        fov={15}
        far={100000}
        makeDefault
      />
      <OrbitControls
        makeDefault={isFreeControl}
        // minPolarAngle={0}
        // maxPolarAngle={Math.PI / 2.1}
        enabled={isFreeControl}
        // enableZoom={isFreeControl}
      />
      {isMapControl && (
        <MapControls
          enableDamping={true}
          dampingFactor={0.08}
          minDistance={100}
          maxDistance={500}
          maxPolarAngle={Math.PI / 2}
        />
      )}
      {/* <color attach="background" args={[bgColor.r, bgColor.g, bgColor.b]} /> */}
      {/* <Environment */}
      {/*  // background */}
      {/*  // files={'/background/studio_small_4k.hdr'} */}
      {/*  preset="city" */}
      {/*  // blur={1} */}
      {/*  near={0.001} */}
      {/*  // resolution={256} */}
      {/*  far={1000000} */}
      {/* /> */}
      {/* <SpotLight */}
      {/*  castShadow */}
      {/*  position={[0, (height / 2 + length) * 2, 0]} */}
      {/*  penumbra={1} */}
      {/*  radiusTop={0.5} */}
      {/*  radiusBottom={1000} */}
      {/*  distance={1000} */}
      {/*  angle={0.75} */}
      {/*  attenuation={0} */}
      {/*  anglePower={10} */}
      {/*  intensity={15000} */}
      {/*  opacity={1} */}
      {/* /> */}
      {/* <SpotLight */}
      {/*  castShadow */}
      {/*  position={[0, height, length * 5]} */}
      {/*  penumbra={1} */}
      {/*  radiusTop={0.5} */}
      {/*  radiusBottom={1000} */}
      {/*  distance={1000} */}
      {/*  angle={0.35} */}
      {/*  attenuation={0} */}
      {/*  anglePower={10} */}
      {/*  intensity={15000} */}
      {/*  opacity={1} */}
      {/* /> */}
      {/* <SpotLight */}
      {/*  castShadow */}
      {/*  position={[-(width * 5), height, 0]} */}
      {/*  penumbra={1} */}
      {/*  radiusTop={0.5} */}
      {/*  radiusBottom={1000} */}
      {/*  distance={1000} */}
      {/*  angle={0.35} */}
      {/*  attenuation={0} */}
      {/*  anglePower={10} */}
      {/*  intensity={20000} */}
      {/*  opacity={1} */}
      {/* /> */}
    </Fragment>
  );
};

export default Env;
