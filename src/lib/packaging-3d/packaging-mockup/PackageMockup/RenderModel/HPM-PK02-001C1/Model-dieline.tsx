import React, { useEffect, useState } from 'react';
import { PAPER_SIDE } from '../../../../utils/constants';
import RectangleShape from './shape/RectangleShape';
import { useRecoilState } from 'recoil';
import { CanvasSizeType, ModelControlsType, ModelInfoType } from '../../types';
import {
  canvasSizeState,
  modelControlsState,
  modelInfoState,
  modelSpacialDataState,
} from '../index';
import { ModelSpecialDataType } from '../../spacialTypes';
import CameraDolly from './CameraDolly';
import _ from 'lodash';
import TrapeziumShape from './shape/TrapeziumShape';
import TrapzoidShape from './shape/TrapzoidShape';
import { getSizePercent } from '../../../../utils/helper';
import { Center, Line, Text } from '@react-three/drei';
import RectangleDash from './shape/RectangleDash';

const MainDieLineShoppingBagC1: React.FC = () => {
  const [modelInfo, setModelInfo] =
    useRecoilState<ModelInfoType>(modelInfoState);
  const { metadata, textures } = modelInfo;
  const { width, length, height, thickness, initW, initH, initL, unit } =
    metadata;
  const [modelSpacialData] = useRecoilState<ModelSpecialDataType>(
    modelSpacialDataState,
  );
  const { gDegree, g, topHeight, bottomHeight } =
    modelSpacialData.HPM_PK02_001C1;
  const [modelControls] = useRecoilState<ModelControlsType>(modelControlsState);
  const { side, is3D, isDimension } = modelControls;
  const [textScale, setTextScale] = useState<{ x: number; y: number }>({
    x: 1,
    y: 1,
  });
  const [canvasSize, setCanvasSize] =
    useRecoilState<CanvasSizeType>(canvasSizeState);

  useEffect(() => {
    if (!is3D) {
      getCanvasSize();
    }
  }, [is3D, width, height, length, g]);

  useEffect(() => {
    const max: any = (height + width + length) / 3;
    setTextScale({ x: max / 10, y: max / 10 });
  }, [width, height, length]);

  const getCanvasSize = () => {
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = height + topHeight + bottomHeight;
    const gapY: any = 0;
    const spacing: any = 0;
    const w = totalX;
    const h = totalY + (spacing + gapY);
    const mx = _.max([w, h]);
    const mn = _.min([w, h]);
    const min_ratio = (mn * 1) / mx;
    const ratio = { x: w > h ? 1 : min_ratio, y: w < h ? 1 : min_ratio };
    setCanvasSize({
      ...canvasSize,
      x: ratio.x,
      y: ratio.y,
    });
  };

  useEffect(() => {
    cropSize();
  }, [width, length, height, g, side]);

  const cropSize = () => {
    const padding: any = 0;
    const totalX: any = width * 2 + length * 2 + g;
    const totalY: any = height + topHeight + bottomHeight;
    const paddingPercent: any = {
      x: getSizePercent({ value: padding, max: totalX }),
      y: getSizePercent({ value: padding, max: totalY }),
    };
    const widthPercentX: any = getSizePercent({ value: width, max: totalX });
    const heightPercent: any = getSizePercent({ value: height, max: totalY });
    const lengthPercentX: any = getSizePercent({ value: length, max: totalX });
    const topHeightPercentY: any = getSizePercent({
      value: topHeight,
      max: totalY,
    });
    const bottomHeightPercentY: any = getSizePercent({
      value: bottomHeight,
      max: totalY,
    });
    const gPercent: any = getSizePercent({ value: g, max: totalX });
    // const bottomGapWidth: number = degreeToWidth(45, bottomHeight);
    // const bt_gap: number = bottomGapWidth * 0.2;
    // const bottomGapWidthPercentX: any = getSizePercent({
    //   value: bottomGapWidth - bt_gap,
    //   max: totalX,
    // });
    const inCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x + gPercent + 2 * widthPercentX + lengthPercentX,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 2,
        unit: '%',
        x: paddingPercent.x + gPercent + 2 * widthPercentX + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 3,
        unit: '%',
        x: paddingPercent.x + gPercent + 2 * widthPercentX + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: lengthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 4,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + lengthPercentX,
        y: paddingPercent.y,
        width: widthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 5,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 7,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: widthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 9,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 10,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 11,
        unit: '%',
        x: paddingPercent.x + gPercent + widthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: lengthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 12,
        unit: '%',
        x: paddingPercent.x + gPercent,
        y: paddingPercent.y,
        width: widthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 13,
        unit: '%',
        x: paddingPercent.x + gPercent,
        y: paddingPercent.y + topHeightPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 15,
        unit: '%',
        x: paddingPercent.x + gPercent,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: widthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 17,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y,
        width: gPercent,
        height: topHeightPercentY,
      },
      {
        id: 18,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + topHeightPercentY,
        width: gPercent,
        height: heightPercent,
      },
    ];
    const outCropPositions: any[] = [
      {
        id: 1,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 2,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + topHeightPercentY,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 3,
        unit: '%',
        x: paddingPercent.x,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: lengthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 4,
        unit: '%',
        x: paddingPercent.x + lengthPercentX,
        y: paddingPercent.y,
        width: widthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 5,
        unit: '%',
        x: paddingPercent.x + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 7,
        unit: '%',
        x: paddingPercent.x + lengthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: widthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 9,
        unit: '%',
        x: paddingPercent.x + lengthPercentX + widthPercentX,
        y: paddingPercent.y,
        width: lengthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 10,
        unit: '%',
        x: paddingPercent.x + lengthPercentX + widthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: lengthPercentX,
        height: heightPercent,
      },
      {
        id: 11,
        unit: '%',
        x: paddingPercent.x + lengthPercentX + widthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: lengthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 12,
        unit: '%',
        x: paddingPercent.x + 2 * lengthPercentX + widthPercentX,
        y: paddingPercent.y,
        width: widthPercentX,
        height: topHeightPercentY,
      },
      {
        id: 13,
        unit: '%',
        x: paddingPercent.x + 2 * lengthPercentX + widthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: widthPercentX,
        height: heightPercent,
      },
      {
        id: 15,
        unit: '%',
        x: paddingPercent.x + 2 * lengthPercentX + widthPercentX,
        y: paddingPercent.y + topHeightPercentY + heightPercent,
        width: widthPercentX,
        height: bottomHeightPercentY,
      },
      {
        id: 17,
        unit: '%',
        x: paddingPercent.x + 2 * lengthPercentX + 2 * widthPercentX,
        y: paddingPercent.y,
        width: gPercent,
        height: topHeightPercentY,
      },
      {
        id: 18,
        unit: '%',
        x: paddingPercent.x + 2 * lengthPercentX + 2 * widthPercentX,
        y: paddingPercent.y + topHeightPercentY,
        width: gPercent,
        height: heightPercent,
      },
    ];
    setModelInfo({
      ...modelInfo,
      cropPositions: {
        ...modelInfo.cropPositions,
        in: inCropPositions,
        out: outCropPositions,
      },
    });
  };

  return (
    <group
      position={[
        side === PAPER_SIDE.Back ? -length / 2 + g / 2 : length / 2 - g / 2,
        (bottomHeight - topHeight) / 2,
        0,
      ]}
    >
      <CameraDolly />
      <group rotation={[0, side === PAPER_SIDE.Back ? 0 : Math.PI, 0]}>
        <group name={'G-1'} position={[-(width + length + g) / 2, 0, 0]}>
          <group name={'S-1-1'} position={[-width / 2, height / 2, 0]}>
            <TrapeziumShape
              textureId={17}
              thickness={thickness}
              isLine={true}
              pivot="right-top"
              degree={0}
              g={g}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-1-2'} position={[-width / 2, height / 2, 0]}>
            <TrapeziumShape
              textureId={17}
              thickness={thickness}
              isLine={true}
              pivot="right-bottom"
              degree={gDegree}
              g={g}
              y={height}
              textures={textures}
            />
          </group>
        </group>
        <group name={'G-2'} position={[-(width + length) / 2, 0, 0]}>
          <group name={'S-2-1'} position={[0, height / 2, 0]}>
            <RectangleDash
              textureId={3}
              pivot="bottom"
              isLine={true}
              thickness={thickness}
              x={width}
              y={topHeight}
              aX={width / 4}
              textures={textures}
            />
          </group>
          <group name={'S-2-2'}>
            <RectangleDash
              textureId={3}
              pivot="top"
              isLine={true}
              thickness={thickness}
              x={width}
              y={height}
              aX={width / 4}
              textures={textures}
            />
            {isDimension && (
              <>
                <Line
                  points={[
                    [-width / 2, height / 8, 0],
                    [width / 2, height / 8, 0],
                  ]}
                  color="green"
                  lineWidth={1}
                  dashScale={3}
                  segments
                  dashed={true}
                />
                <Center
                  rotation={
                    side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                  }
                >
                  <Text
                    fontSize={0.5}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'green'}
                  >
                    {`W : ${initW} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group name={'G-2-3'} position={[0, -height / 2, 0]}>
            <group name={'S-2-3'}>
              <TrapzoidShape
                textureId={3}
                pivot="top"
                isLine={true}
                thickness={thickness}
                x={width}
                y={bottomHeight}
                degree={45}
                textures={textures}
              />
            </group>
          </group>
        </group>
        <group name={'G-3'}>
          <group name={'S-3-1'} position={[0, height / 2, 0]}>
            <RectangleShape
              textureId={3}
              pivot="bottom"
              isLine={true}
              thickness={thickness}
              x={length}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-3-2'}>
            <RectangleShape
              textureId={3}
              pivot="center"
              isLine={true}
              thickness={thickness}
              x={length}
              y={height}
              textures={textures}
            />
            {isDimension && (
              <>
                <Line
                  points={[
                    [-length / 2, -height / 8, 0],
                    [length / 2, -height / 8, 0],
                  ]}
                  color="blue"
                  lineWidth={1}
                  dashScale={3}
                  segments
                  dashed={true}
                />
                <Center
                  position={[0, 0, 0]}
                  rotation={
                    side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                  }
                >
                  <Text
                    fontSize={0.5}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'blue'}
                  >
                    {`L : ${initL} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group name={'S-3-3'} position={[0, -height / 2, 0]}>
            <TrapzoidShape
              textureId={3}
              pivot="top"
              isLine={true}
              thickness={thickness}
              x={length}
              y={bottomHeight}
              degree={45}
              textures={textures}
            />
          </group>
        </group>
        <group name={'G-4'} position={[(width + length) / 2, 0, 0]}>
          <group name={'S-4-1'} position={[0, height / 2, 0]}>
            <RectangleDash
              textureId={3}
              pivot="bottom"
              isLine={true}
              thickness={thickness}
              x={width}
              y={topHeight}
              aX={width / 4}
              textures={textures}
            />
          </group>
          <group name={'S-4-2'}>
            <RectangleDash
              textureId={3}
              pivot="top"
              isLine={true}
              thickness={thickness}
              x={width}
              y={height}
              aX={width / 4}
              textures={textures}
            />
            {isDimension && (
              <>
                <Line
                  points={[
                    [width * -0.3, height / 2, 0],
                    [width * -0.3, -height / 2, 0],
                  ]}
                  color="red"
                  lineWidth={1}
                  dashScale={3}
                  segments
                  dashed={true}
                />
                <Center
                  position={[0, 0, 0]}
                  rotation={
                    side === PAPER_SIDE.Front ? [0, Math.PI, 0] : [0, 0, 0]
                  }
                >
                  <Text
                    fontSize={0.5}
                    scale={[textScale.x, textScale.y, 1]}
                    color={'red'}
                  >
                    {`H : ${initH} ${unit}`}
                  </Text>
                </Center>
              </>
            )}
          </group>
          <group name={'G-4-3'} position={[0, -height / 2, 0]}>
            <group name={'S-4-3'}>
              <TrapzoidShape
                textureId={3}
                pivot="top"
                isLine={true}
                thickness={thickness}
                x={width}
                y={bottomHeight}
                degree={45}
                textures={textures}
              />
            </group>
          </group>
        </group>
        <group name={'G-5'} position={[(2 * width + 2 * length) / 2, 0, 0]}>
          <group name={'S-5-1'} position={[0, height / 2, 0]}>
            <RectangleShape
              textureId={3}
              pivot="bottom"
              isLine={true}
              thickness={thickness}
              x={length}
              y={topHeight}
              textures={textures}
            />
          </group>
          <group name={'S-5-2'}>
            <RectangleShape
              textureId={3}
              pivot="center"
              isLine={true}
              thickness={thickness}
              x={length}
              y={height}
              textures={textures}
            />
          </group>
          <group name={'S-5-3'} position={[0, -height / 2, 0]}>
            <TrapzoidShape
              textureId={3}
              pivot="top"
              isLine={true}
              thickness={thickness}
              x={length}
              y={bottomHeight}
              degree={45}
              textures={textures}
            />
          </group>
        </group>
      </group>
    </group>
  );
};

export default MainDieLineShoppingBagC1;
