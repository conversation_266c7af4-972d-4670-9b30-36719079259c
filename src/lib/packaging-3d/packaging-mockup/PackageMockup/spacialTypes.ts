export type ModelSpecialDataType =
  | {
      template: {
        ex: number;
      };
      HPM_PK01_001A0: {
        c: number;
        g: number;
        aDegree: number;
        gDegree: number;
      };
      HPM_PK01_001B0: {
        c: number;
        g: number;
        aDegree: number;
        gDegree: number;
      };
      HPM_PK01_001C0: {
        c: number;
        g: number;
        aDegree: number;
        gDegree: number;
      };
      HPM_PK01_002A0: {
        c: number;
        g: number;
        aDegree: number;
        gDegree: number;
      };
      HPM_PK01_002B0: {
        c: number;
        g: number;
        aDegree: number;
        gDegree: number;
        gapWidth: number;
        connectW: number;
        holeW: number;
      };
      HPM_PK02_001A0: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001A1: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001B0: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001B1: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001C0: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001C1: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_PK02_001D0: {
        g: number;
        gDegree: number;
        topHeight: number;
        bottomHeight: number;
        bottomGap: number;
      };
      HPM_SK01_001A0: {
        ex: number;
      };
    }
  | any;

export const initialModelSpecialInfo = {
  template: {
    ex: 0,
  },
  HPM_PK01_001A0: {
    c: 40 * 0.1,
    g: 30 * 0.1,
    aDegree: 15,
    gDegree: 15,
  },
  HPM_PK01_001B0: {
    c: 40 * 0.1,
    g: 30 * 0.1,
    aDegree: 15,
    gDegree: 15,
  },
  HPM_PK01_001C0: {
    c: 40 * 0.1,
    g: 30 * 0.1,
    aDegree: 15,
    gDegree: 15,
  },
  HPM_PK01_002A0: {
    c: 40 * 0.1,
    g: 30 * 0.1,
    aDegree: 15,
    gDegree: 15,
    gapWidth: 1,
    connectW: 1,
    holeW: 1,
  },
  HPM_PK01_002B0: {
    c: 40 * 0.1,
    g: 30 * 0.1,
    aDegree: 15,
    gDegree: 15,
    gapWidth: 1,
    connectW: 1,
    holeW: 1,
  },
  HPM_PK02_001A0: {
    g: 40 * 0.1,
    gDegree: 0,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001A1: {
    g: 40 * 0.1,
    gDegree: 15,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001B0: {
    g: 40 * 0.1,
    gDegree: 0,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001B1: {
    g: 40 * 0.1,
    gDegree: 15,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001C0: {
    g: 40 * 0.1,
    gDegree: 0,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001C1: {
    g: 40 * 0.1,
    gDegree: 0,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_PK02_001D0: {
    g: 40 * 0.1,
    gDegree: 0,
    topHeight: 10,
    bottomHeight: 15,
    bottomGap: 10,
  },
  HPM_SK01_001A0: {
    ex: 1,
  },
};
