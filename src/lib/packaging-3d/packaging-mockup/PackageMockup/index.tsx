import React from 'react';
import RenderModel from './RenderModel';
import { RecoilRoot } from 'recoil';

export type PropsType = {
  modelId: string | null;
  width: number;
  height: number;
  length: number;
  unit: 'mm' | 'cm' | 'inch';
  is3D: boolean;
  isFreeControl: boolean;
  actionState: number;
  side: number;
  mode: number;
  isHelper: boolean;
  isPlay: boolean;
  isMapControl: boolean;
  onCanvasSize: any | null;
  onCropPositions: any | null;
  textures: { in: any[]; out: any[] } | null;
  material: any | null;
  baseSize: number | null;
  zoom: number;
  isDimension: boolean;
  rotation: { x: any; y: any; z: any };
};

const PackageMockup: React.FC<PropsType> = (props) => {
  return (
    <RecoilRoot>
      <RenderModel data={props} />
    </RecoilRoot>
  );
};

export default PackageMockup;
