import { getRatioSize } from './helper';

type propsType = {
  initial: any;
  data: any;
  onSpacial: any;
};

export const setSpacialData: any = ({
  initial,
  data,
  onSpacial,
}: propsType) => {
  const w = getRatioSize({
    x: data.width,
    y: data.height,
    z: data.length,
  }).x;
  const h = getRatioSize({
    x: data.width,
    y: data.height,
    z: data.length,
  }).y;
  const l = getRatioSize({
    x: data.width,
    y: data.height,
    z: data.length,
  }).z;
  switch (data?.modelId) {
    case 'HPM-PK01-001A0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK01_001A0: {
            ...initial.HPM_PK01_001A0,
            c: (w + l) * 0.055,
            g: l * 0.1,
            aDegree: 2,
          },
        },
      });
    case 'HPM-PK01-001B0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK01_001B0: {
            ...initial.HPM_PK01_001B0,
            c: (w + l) * 0.055,
            g: l * 0.1,
            aDegree: 2,
          },
        },
      });
    case 'HPM-PK01-001C0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK01_001C0: {
            ...initial.HPM_PK01_001C0,
            c: (w + l) * 0.055,
            g: l * 0.1,
            aDegree: 2,
          },
        },
      });
    case 'HPM-PK01-002A0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK01_002A0: {
            ...initial.HPM_PK01_002A0,
            c: (w + l) * 0.055,
            g: l * 0.1,
            aDegree: 2,
          },
        },
      });
    case 'HPM-PK01-002B0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK01_002B0: {
            ...initial.HPM_PK01_002B0,
            c: (w + l) * 0.055,
            g: l * 0.1,
            aDegree: 2,
            gapWidth: h * 0.025,
            connectW: l * 0.1,
            holeW: l * 0.01,
          },
        },
      });
    case 'HPM-PK02-001A0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001A0: {
            ...initial.HPM_PK02_001A0,
            g: w * 0.1,
            // gDegree: 15,
            gDegree: 0,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001A1':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001A1: {
            ...initial.HPM_PK02_001A1,
            g: w * 0.1,
            gDegree: 45,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001B0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001B0: {
            ...initial.HPM_PK02_001B0,
            g: w * 0.1,
            gDegree: 0,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001B1':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001B1: {
            ...initial.HPM_PK02_001B1,
            g: w * 0.1,
            gDegree: 45,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001C0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001C0: {
            ...initial.HPM_PK02_001C0,
            g: w * 0.1,
            // gDegree: 15,
            gDegree: 0,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001C1':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001C1: {
            ...initial.HPM_PK02_001C1,
            g: w * 0.1,
            gDegree: 45,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    case 'HPM-PK02-001D0':
      return onSpacial({
        modelId: data?.modelId,
        spacial: {
          ...initial,
          HPM_PK02_001D0: {
            ...initial.HPM_PK02_001D0,
            g: w * 0.1,
            // gDegree: 15,
            gDegree: 0,
            topHeight: h * 0.1,
            bottomHeight: l * 0.6,
            bottomGap: 10,
          },
        },
      });
    default:
      return onSpacial({
        modelId: data?.modelId,
        spacial: initial,
      });
  }
};
