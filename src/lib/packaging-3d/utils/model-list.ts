type ModelType = {
  code: string;
  name: string;
  isAnimation: boolean;
  defaultSize: {
    width: number;
    height: number;
    length: number;
    minWidth: number;
    minHeight: number;
    minLength: number;
  };
};
export const ModelList: ModelType[] = [
  {
    code: 'HPM-PK01-001A0',
    name: 'Tuck End Boxes A',
    isAnimation: true,
    defaultSize: {
      width: 20,
      height: 20,
      length: 20,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK01-001B0',
    name: 'Tuck End Boxes B',
    isAnimation: true,
    defaultSize: {
      width: 20,
      height: 20,
      length: 20,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK01-001C0',
    name: 'Tuck End Boxes C',
    isAnimation: true,
    defaultSize: {
      width: 20,
      height: 20,
      length: 20,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK01-002A0',
    name: 'Tray Boxes A',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 10,
      length: 40,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK01-002B0',
    name: 'Tray Boxes B',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 10,
      length: 40,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001A0',
    name: 'Shopping Bags A',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001A1',
    name: 'Shopping Bags A1',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001B0',
    name: 'Shopping Bags B',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001B1',
    name: 'Shopping Bags B1',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001C0',
    name: 'Shopping Bags C',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001C1',
    name: 'Shopping Bags C1',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK02-001D0',
    name: 'Shopping Bags D',
    isAnimation: true,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK04-002A0',
    name: 'Sticker Rectangle',
    isAnimation: false,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
  {
    code: 'HPM-PK04-001A0',
    name: 'Sticker Circle',
    isAnimation: false,
    defaultSize: {
      width: 40,
      height: 40,
      length: 10,
      minWidth: 0,
      minHeight: 0,
      minLength: 0,
    },
  },
];
