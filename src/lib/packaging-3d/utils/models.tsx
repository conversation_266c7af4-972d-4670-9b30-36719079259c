import React from 'react';
import HPMPK01001A0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK01-001A0';
import HPMPK01001B0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK01-001B0';
import HPMPK01001C0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK01-001C0';
import HPMPK01002A0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/Template B';
import HPMPK01002B0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK01-002B0';
import HPMPK02001A0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001A0';
import HPMPK02001A1 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001A1';
import HPMPK02001B0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001B0';
import HPMPK02001B1 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001B1';
import HPMPK02001C0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001C0';
import HPMPK02001C1 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001C1';
import HPMPK02001D0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM-PK02-001D0';
import MainDieLineTemplate from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/Template/Model-dieline';
import Main3DTemplate from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/Template/Model-3d';
import HPMPK04001A0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM_PK04_001A0';
import HPMPK04002A0 from '@/lib/packaging-3d/packaging-mockup/PackageMockup/RenderModel/HPM_PK04_002A0';

type propsType = {
  modelId: any;
};

export const getModelComponent = ({ modelId }: propsType) => {
  switch (modelId) {
    case 'HPM-PK01-001A0':
      return {
        id: 1,
        code: 'HPM-PK01-001A0',
        component: {
          m2d: () => <HPMPK01001A0 type={'2d'} />,
          m3d: () => <HPMPK01001A0 type={'3d'} />,
        },
      };
    case 'HPM-PK01-001B0':
      return {
        id: 1,
        code: 'HPM-PK01-001B0',
        component: {
          m2d: () => <HPMPK01001B0 type={'2d'} />,
          m3d: () => <HPMPK01001B0 type={'3d'} />,
        },
      };
    case 'HPM-PK01-001C0':
      return {
        id: 1,
        code: 'HPM-PK01-001C0',
        component: {
          m2d: () => <HPMPK01001C0 type={'2d'} />,
          m3d: () => <HPMPK01001C0 type={'3d'} />,
        },
      };
    case 'HPM-PK01-002A0':
      return {
        id: 1,
        code: 'HPM-PK01-002A0',
        component: {
          m2d: () => <HPMPK01002A0 type={'2d'} />,
          m3d: () => <HPMPK01002A0 type={'3d'} />,
        },
      };
    case 'HPM-PK01-002B0':
      return {
        id: 1,
        code: 'HPM-PK01-002B0',
        component: {
          m2d: () => <HPMPK01002B0 type={'2d'} />,
          m3d: () => <HPMPK01002B0 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001A0':
      return {
        id: 7,
        code: 'HPM-PK02-001A0',
        component: {
          m2d: () => <HPMPK02001A0 type={'2d'} />,
          m3d: () => <HPMPK02001A0 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001A1':
      return {
        id: 7,
        code: 'HPM-PK02-001A1',
        component: {
          m2d: () => <HPMPK02001A1 type={'2d'} />,
          m3d: () => <HPMPK02001A1 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001B0':
      return {
        id: 7,
        code: 'HPM-PK02-001B0',
        component: {
          m2d: () => <HPMPK02001B0 type={'2d'} />,
          m3d: () => <HPMPK02001B0 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001B1':
      return {
        id: 7,
        code: 'HPM-PK02-001B1',
        component: {
          m2d: () => <HPMPK02001B1 type={'2d'} />,
          m3d: () => <HPMPK02001B1 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001C0':
      return {
        id: 7,
        code: 'HPM-PK02-001C0',
        component: {
          m2d: () => <HPMPK02001C0 type={'2d'} />,
          m3d: () => <HPMPK02001C0 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001C1':
      return {
        id: 7,
        code: 'HPM-PK02-001C1',
        component: {
          m2d: () => <HPMPK02001C1 type={'2d'} />,
          m3d: () => <HPMPK02001C1 type={'3d'} />,
        },
      };
    case 'HPM-PK02-001D0':
      return {
        id: 7,
        code: 'HPM-PK02-001D0',
        component: {
          m2d: () => <HPMPK02001D0 type={'2d'} />,
          m3d: () => <HPMPK02001D0 type={'3d'} />,
        },
      };
    case 'HPM-PK04-001A0':
      return {
        id: 7,
        code: 'HPM-PK04-001A0',
        component: {
          m2d: () => <HPMPK04001A0 type={'2d'} />,
          m3d: () => <HPMPK04001A0 type={'3d'} />,
        },
      };
    case 'HPM-PK04-002A0':
      return {
        id: 7,
        code: 'HPM-PK04-002A0',
        component: {
          m2d: () => <HPMPK04002A0 type={'2d'} />,
          m3d: () => <HPMPK04002A0 type={'3d'} />,
        },
      };
    default:
      return {
        id: 0,
        code: '',
        component: {
          m2d: () => <MainDieLineTemplate />,
          m3d: () => <Main3DTemplate />,
        },
      };
  }
};
