import _ from 'lodash';

export type CalRatioType = {
  width: number;
  height: number;
  length: number;
  thickness: number;
  max_ratio?: any;
};

export const getSizeRatio = ({
  width,
  height,
  length,
  thickness,
  max_ratio,
}: CalRatioType) => {
  const values: number[] = [width, height, length, thickness];
  const max: number = Number(_.max(values));
  const width_ratio: number = (width * max_ratio) / max;
  const height_ratio: number = (height * max_ratio) / max;
  const length_ratio: number = (length * max_ratio) / max;
  const thickness_ratio: number = (thickness * max_ratio) / max;
  const response: any = {
    width: width_ratio,
    height: height_ratio,
    length: length_ratio,
    thickness: thickness_ratio,
  };
  return response;
};

const toRadians = (degrees: any) => {
  const pi = Math.PI;
  return degrees * (pi / 180);
};
export const degreeToWidth = (degree: number, height: number) => {
  const tan: number = Math.tan(toRadians(degree));
  const width: number = tan * height;
  return width;
};

export const getSizePercent = (props: { value: number; max: number }) => {
  const { value, max } = props;
  const percentage: number = (value * 100) / max;
  return percentage;
};

export const unitConvertor = (props: {
  value: number;
  unit: 'mm' | 'cm' | 'inch';
  toUnit: 'mm' | 'cm' | 'inch';
}) => {
  const { value, unit, toUnit } = props;
  switch (`${unit}|${toUnit}`) {
    case 'mm|mm':
      return value;
    case 'mm|cm':
      return value * 0.1;
    case 'mm|inch':
      return value * 0.0393701;
    case 'cm|mm':
      return value * 10;
    case 'cm|cm':
      return value;
    case 'cm|inch':
      return value * 0.393701;
    case 'inch|mm':
      return value * 25.4;
    case 'inch|cm':
      return value * 2.54;
    case 'inch|inch':
      return value;
    default:
      return value;
  }
};

export const getRatioSize = (props: { x: number; y: number; z: number }) => {
  const radioMax: number = 100;
  const { x, y, z } = props;
  const max = _.max([x, y, z]);
  return {
    x: (x * radioMax) / Number(max),
    y: (y * radioMax) / Number(max),
    z: (z * radioMax) / Number(max),
  };
};
