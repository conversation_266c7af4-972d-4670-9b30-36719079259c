import { isEmpty } from 'lodash';

export const sumValueInArray = (value: number[]) =>
  value.reduce((accumulator, currentValue) => accumulator + currentValue, 0);

export const calculatePercentage = (price: number, percent: number) => {
  const percentCal = percent / 100;
  return price - price * percentCal;
};

export const calculateTotalSummaryPrice = (summaryPrice: any) => {
  if (!summaryPrice || typeof summaryPrice !== 'object') {
    return 0;
  }

  return Object.values(summaryPrice).reduce((total: number, price: any) => {
    const numericPrice = typeof price === 'number' ? price : 0;
    return total + numericPrice;
  }, 0);
};

export const calculateSpecialTechniquesTotal = (data: any[]) => {
  if (isEmpty(data)) return 0;

  return data.reduce((total: number, item: any) => {
    return total + (item.price || 0);
  }, 0);
};

export const calculateCompletionDate = (maxPeriod: number) => {
  const today = new Date();
  const completionDate = new Date(today);
  completionDate.setDate(today.getDate() + maxPeriod);
  return completionDate.getTime();
};
