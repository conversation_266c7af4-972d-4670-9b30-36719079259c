// Utility functions for interacting with Pacdora Plus API

/**
 * Get box information including project ID, model ID, and screenshot
 */
export const getBoxInfo = async () => {
  if (!(window as any).Pacdora) {
    console.error('Pacdora is not loaded');
    return null;
  }

  try {
    return (window as any).Pacdora.getBoxInfo();
  } catch (error) {
    console.error('Failed to get box info:', error);
    return null;
  }
};
/**
 * Set material for the box
 */
export const setMaterial = async (options: {
  name?: string;
  image?: string;
  async?: boolean;
  side?: string;
  insideName?: string;
  insideImage?: string;
}) => {
  if (!(window as any).Pacdora) {
    console.error('Pacdora is not loaded');
    return false;
  }
  try {
    setTimeout(() => {
      (window as any).Pacdora.setMaterial(options);
    }, 500);
    return true;
  } catch (error) {
    console.error('Failed to set material:', error);
    return false;
  }
};

export const createScene = async (options: {
  modelId: number;
  width?: number;
  height?: number;
  length?: number;
}) => {
  if (!(window as any).Pacdora) {
    console.error('Pacdora is not loaded');
    return false;
  }
  const initail: any = {
    modelId: options.modelId,
    isShowLoading: true,
    showSize: true,
    showSide: true,
    doneBtn: 'Save',
    // theme: '#f5f5f5',
    isCreatePreview: true,
    // packagingColors: ['#ff0000', '#00ff00', '#0000ff', '#893829']
  };
  if (options.width) {
    initail.width = options.width;
  }
  if (options.height) {
    initail.height = options.height;
  }
  if (options.length) {
    initail.length = options.length;
  }
  try {
    await (window as any).Pacdora.destroy3d('view3d');
    await (window as any).Pacdora.createScene(initail);
    setTimeout(() => {
      (window as any).Pacdora.set3DBackground('view3d', 'color', '#f5f5f5');
      (window as any).Pacdora.enable3DDrag('view3d', true);
      (window as any).Pacdora.enable3DScale('view3d', true);
      (window as any).Pacdora.set3DDimensionView('view3d', false);
    }, 500);
    return true;
  } catch (error) {
    console.error('Failed to set material:', error);
    return false;
  }
};

/**
 * Set size for the box
 */
export const setSize = async (options: {
  width?: number;
  height?: number;
  length?: number;
  type?: 'id' | 'ed' | 'dm';
  async?: boolean;
}) => {
  if (!(window as any).Pacdora) {
    console.error('Pacdora is not loaded');
    return false;
  }

  try {
    await (window as any).Pacdora.setSize(options);
    return true;
  } catch (error) {
    console.error('Failed to set size:', error);
    return false;
  }
};

export const setCollapse = async (options: { id: string; score: number }) => {
  if (!(window as any).Pacdora) {
    console.error('Pacdora is not loaded');
    return false;
  }

  try {
    await (window as any).Pacdora.collapse(options.id, options.score);
    return true;
  } catch (error) {
    console.error('Failed to collapse:', error);
    return false;
  }
};
