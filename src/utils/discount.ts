import dayjs from 'dayjs';
import { calculatePercentage, sumValueInArray } from '@/utils/calculator';
import { isEmpty } from 'lodash';

export const discountCategory = (data: any) => {
  return data?.reduce((acc: any, item: any) => {
    const { name } = item.discount?.discountCategory || item.discountCategory;
    if (!acc[name]) {
      acc[name] = [];
    }
    if (
      dayjs().unix() < dayjs(item.discount?.endDate || item.endDate).unix() &&
      item.isActive === true
    ) {
      acc[name].push(item);
    }
    return acc;
  }, {});
};

export const discountAll = (data: any) => {
  return data?.filter((item: any) => {
    return (
      dayjs().unix() < dayjs(item.discount?.endDate || item.endDate).unix() &&
      item.isActive === true
    );
  });
};

export const isActiveDiscount = (data: any) => {
  return data?.filter((item: any) => {
    return item.isActive === false;
  });
};
export const discountPriceList = (discounts: any, totalPrice: number) => {
  if (!isEmpty(discounts)) {
    const result = discounts?.map((item: any) => {
      if (item.discountCategory?.id === 2) {
        return null;
      }
      const { percentage, maxDiscount } = item;
      return calculatePercentage(totalPrice, percentage) > maxDiscount
        ? maxDiscount
        : Number(calculatePercentage(totalPrice, percentage));
    });
    return sumValueInArray(result);
  }
  return 0;
};
export const priceDiscountVat = (totalPrice?: number, discounts?: any) => {
  if (!totalPrice) {
    return null;
  }
  const vat = 7 / 100;
  const priceVat = totalPrice * vat;
  if (!isEmpty(discounts)) {
    const discountPrice = discountPriceList(discounts, totalPrice);
    return (totalPrice - discountPrice) * vat;
  }
  return priceVat;
};

export const resultSumDiscount = (
  discounts: any,
  totalPrice: number,
  shippingCost: number,
  vat: number,
) => {
  const discountPrice = discounts.map((item: any) => {
    const { percentage, maxDiscount } = item;
    if (!totalPrice) return null;
    return calculatePercentage(totalPrice, percentage) > maxDiscount
      ? maxDiscount
      : calculatePercentage(totalPrice, percentage);
  });
  return discountPrice && sumValueInArray(discountPrice)
    ? totalPrice + shippingCost + vat - sumValueInArray(discountPrice)
    : totalPrice + shippingCost + vat;
};

export const dataResult = (
  discounts: any,
  totalPrice: number,
  shippingCost: number,
  vat: number,
) => {
  if (!totalPrice) return null;
  const discountPrice = discountPriceList(discounts, totalPrice);
  return {
    discounts: discounts,
    sumDiscountPrice: discountPrice,
    totalPrice: totalPrice,
    sumTotalPrice: totalPrice - discountPrice + (shippingCost + vat),
  };
};
