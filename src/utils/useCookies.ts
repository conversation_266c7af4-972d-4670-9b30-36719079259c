import { deleteC<PERSON>ie, getCookie, setCookie } from 'cookies-next';

export const useCookies = (key: string) => {
  const setCookieValue = (value: unknown) => {
    try {
      setCookie(key, value);
    } catch (error) {
      console.log(error);
    }
  };

  const getCookieValue = () => {
    try {
      return getCookie(key);
    } catch (error) {
      console.log(error);
    }
  };

  const removeCookieValue = () => {
    try {
      deleteCookie(key);
    } catch (error) {
      console.log(error);
    }
  };

  return { setCookieValue, getCookieValue, removeCookieValue };
};
