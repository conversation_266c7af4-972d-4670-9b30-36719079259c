import { toast } from 'sonner';

export const handleCopyToClipboard = (text: string) => {
  const textarea = document.createElement('textarea');
  // Set its value to the text you want to copy
  textarea.value = text;
  // Append it to the document
  document.body.appendChild(textarea);
  // Select the text inside the textarea
  textarea.select();
  // Execute the copy command
  document.execCommand('copy');
  // Remove the temporary textarea
  document.body.removeChild(textarea);
  toast.info('คัดลอกแล้ว');
};
