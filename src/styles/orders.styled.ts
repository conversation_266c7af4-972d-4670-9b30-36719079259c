import styled, { keyframes } from 'styled-components';
import { Card } from '@/components/ui/card';

const scaleUpAnimate = keyframes`
  from {
    transform: scale(0.6) translateY(0);
    opacity: 0;
  }
  20% {
    transform: scale(0.6) translateY(0);
    opacity: 0.1;
  }
  60% {
    transform: scale(1) translateY(0px);
    opacity: 1;
  }
  to {
    transform: scale(1.5) translateY(0px);
    opacity: 0;
  }
`;

export const OrdersStyle = styled.div`
  width: 100%;
  padding-top: 80px;
  @media (max-width: 991px) {
    padding-top: 57px;
  }
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .card-container {
    width: 40%;
    margin: auto;
    padding-top: 80px;
    @media (max-width: 991px) {
      padding-top: 40px;
      width: 90%;
    }
    @media only screen and (max-width: 430px) {
      padding-top: 10px;
      width: 90%;
    }
    .price-wrapper {
      border-top: 1px solid #dbe2e5;
      padding: 16px;
      .price-text-wrapper {
        display: flex;
        justify-content: space-between;
        .caption {
          font-size: 12px;
          font-weight: 400;
        }
        .subtitle {
          font-size: 20px;
          font-weight: 700;
        }
      }
    }
  }
  .coupon-container {
    border: 1px solid #dbe2e5;
    background: #fff;
    padding: 16px;
    border-radius: 16px;
    margin-bottom: 24px;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      .title {
        display: flex;
        align-items: center;
        gap: 5px;
      }
      .see-coupon {
        button {
          padding-left: unset !important;
          padding-right: unset !important;
          background-color: unset !important;
          color: #212121 !important;
          &:hover {
            text-decoration: underline;
          }
          &:focus {
            border: none;
          }
        }
      }
    }
    .detail-grid {
      margin-top: 1rem;
      display: flex;
      align-items: center;
      gap: 15px;
      button {
        min-width: unset;
        width: 30px;
        padding: 0;
      }
      .detail-price {
        width: 100%;
        border-radius: 4px;
        background: #ffecec;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px 8px;
        .detail {
          max-width: 500px;
        }
        span {
          color: #e5200c !important;
        }
      }
    }
  }
  .result-container {
    width: 100%;
    .subtitle {
      width: 100%;
      font-size: 20px;
      font-weight: 700;
    }
    .result-text-wrapper {
      margin-top: 16px;
      padding-top: 16px;
      border-top: dotted 2px #212121;
      display: flex;
      .content-1 {
        font-size: 14px;
        font-weight: 400;
      }
      .content-2 {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
  .total-container {
    background-color: #212121f2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 24px auto;
    padding: 24px;
    border-radius: 8px;
    @media (max-width: 425px) {
      padding: 14px;
    }
    .subtitle {
      color: white;
      font-size: 20px;
      font-weight: 700;
      @media (max-width: 575px) {
        font-size: 18px;
      }
      @media (max-width: 375px) {
        font-size: 14px;
      }
    }
    .subheader {
      color: white;
      font-size: 36px;
      font-weight: 700;
      @media (max-width: 575px) {
        font-size: 26px;
      }
      @media (max-width: 375px) {
        font-size: 22px;
      }
    }
  }
  .confirm-button {
    width: 100%;
    height: 48px;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
    border-radius: 8px;
    padding: 8px 0;
  }
  .success-container {
    width: 100%;
    justify-content: center;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 80px);
    .flare {
      position: absolute;
      transform: translate(-50%, -50%);
      animation: ${scaleUpAnimate} 1s linear infinite;
    }
    .success-wrapper {
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      @media only screen and (max-width: 430px) {
      }
      .subtitle {
        font-size: 20px;
        font-weight: 700;
      }
      .subheader {
        margin-top: 16px;
        font-size: 36px;
        font-weight: 700;
      }
      .content {
        font-size: 14px;
        font-weight: 400;
        @media only screen and (max-width: 430px) {
          text-align: center;
          width: 80%;
        }
      }
      .content-bold {
        font-size: 14px;
        font-weight: 700;
      }
      .button {
        width: 345px;
        margin-top: 48px;
        background-color: #ff4f00;
        padding: 16px;
        border-radius: 8px;
        font-size: 20px;
        font-weight: 700;
        :hover {
          background-color: #ff6b33;
        }
      }
    }
  }
`;

export const OrdersHeaderStyle = styled(OrdersStyle)`
  display: flex;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #dbe2e5;
  //position: fixed;
  width: 100%;
  background-color: white;
  z-index: 1;
  align-items: center;
  .subtitle {
    font-size: 20px;
    font-weight: 700;
  }
`;

export const CardOrdersCustomsStyle = styled(Card)`
  display: flex;
  margin: 24px 0;
  position: relative;
  flex-direction: column;
  .header-wrapper {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #dbe2e5;
    align-items: center;
    .text-wrapper {
      .subtitle {
        font-size: 20px;
        font-weight: 700;
      }
    }
    .action-wrapper {
      display: flex;
      .delete {
        margin-left: 8px;
        cursor: pointer;
        width: 24px;
        height: 24px;
      }
      .edit {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
    }
  }
  .content-wrapper {
    display: flex;
    border-bottom: 1px solid #dbe2e5;
    padding: 16px;
    @media only screen and (max-width: 430px) {
      padding: 16px 12px !important;
    }
    .text-container {
      display: flex;
      flex-direction: column;
      margin: 0 24px;
      @media only screen and (max-width: 430px) {
        margin: 0 10px !important;
      }
      justify-content: space-between;
      .text-wrapper {
        display: flex;
        flex-direction: column;
        .subtitle {
          font-size: 14px;
          font-weight: 400;
          @media only screen and (max-width: 375px) {
            font-size: 12px;
          }
        }
      }
      .amount-container {
        .amount {
          font-size: 14px;
          font-weight: 700;
          @media only screen and (max-width: 375px) {
            font-size: 12px;
          }
        }
        .price {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 400;
          @media only screen and (max-width: 375px) {
            font-size: 12px;
          }
        }
      }

      .price-container {
        .price {
          font-size: 14px;
          font-weight: 700;
        }
        .unit {
          margin-left: 8px;

          font-size: 14px;
          font-weight: 700;
        }
      }
    }
  }
  .detail-wrapper {
    .content {
      font-size: 14px;
      font-weight: 400;
    }
    .show-more-container {
      .show-more-wrapper {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #dbe2e5;
        padding: 16px;
        .special-wrapper {
          display: flex;
          flex-direction: column;
          text-align: right;
          .special-text {
            font-size: 14px;
            font-weight: 700;
          }
        }
        .head-text {
          font-size: 14px;
          font-weight: 400;
        }
        .content-text {
          font-size: 14px;
          font-weight: 700;
        }
      }
    }
  }
  .location-container {
    display: flex;
    flex-direction: column;
    padding: 16px;
    @media (max-width: 430px) {
      padding: 10px;
    }
    .location-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      @media (max-width: 375px) {
        display: unset;
      }
      .select-location-wrapper {
        align-items: center;
        display: flex;
        @media (max-width: 375px) {
          margin-bottom: 0.5rem;
          justify-content: center;
        }
        .content {
          font-size: 14px;
          font-weight: 700;
          margin-right: 4px;
        }
        .primary {
          color: #ff4f00;
        }
      }
      .change-address-wrapper {
        display: flex;
        cursor: pointer;
        align-items: center;
        @media (max-width: 375px) {
          justify-content: center;
        }
        .address {
          font-size: 14px;
          color: #ff4f00;
        }
      }
    }
    .location-text-wrapper {
      background-color: #f5f7f8;
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
      display: flex;
    }
  }
  .location-text-wrapper {
    flex-direction: column;
    .content {
      font-size: 14px;
      font-weight: 400;
    }
    .change-location {
      color: black;
      text-decoration: underline;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      cursor: pointer;
    }
  }
`;
