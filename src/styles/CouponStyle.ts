import styled from 'styled-components';
import { Fadein, slideInTop } from '@/styles/animation';

const couponType2 = {
  primaryColor: '#31d3b6',
  secondaryColor: '#E3FAF6',
};
const CouponStyle = styled.div`
  background: #fff;
  color: #212121;
  width: 100%;
  .container {
    max-width: 1300px !important;
    padding: 0 2rem !important;
    @media (max-width: 768px) {
      padding: 0 1rem !important;
    }
  }
  .zone-coupon {
    padding-top: 10rem;
    header {
      h2 {
        color: #212121;
        font-size: 80px;
        font-weight: 700;
      }
    }

    .box-paragraph {
      color: #212121;
      display: flex;
      align-items: end;
      justify-content: space-between;
      margin-bottom: 4rem;
      .btn-all {
        background: #212121;
        color: #fff;
        width: 115px;
        height: 40px;
        border-radius: 8px;
        @media (min-width: 426px) {
          svg {
            display: none;
          }
        }
      }
    }
    .box-banner-coupon {
      //background: url('/images/banner-coupon.svg') no-repeat center;
      //background-size: cover;
      //width: 100%;
      //height: 600px;
      //border-radius: 40px;
      margin-bottom: 3rem;
      border-radius: 24px;
      overflow: hidden;
      img {
        width: 100%;
      }
    }

    .box-coupon-products {
      background: #fafafa;
      padding: 1.5rem;
      border-radius: 24px;
      margin-bottom: 3rem;
      header {
        margin-bottom: 1rem;
        h3 {
          font-size: 30px;
          font-weight: 700;
          color: #212121;
        }
        p {
        }
      }
      .zone-card-coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        .card-coupon {
          border: 1px solid #dbe2e5;
          width: 400px;
          height: 132px;
          border-radius: 8px;
          background: #fff;
          display: flex;
          overflow: hidden;

          .detail {
            padding: 0.8rem;
            width: 70%;
            display: grid;
            align-items: center;
            header {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-bottom: 0.5rem;
              h4 {
                font-weight: 700;
                font-size: 20px;
              }
            }
            p {
              font-size: 12px;
              line-height: 18px;
              color: #212121;
              &:last-child {
                color: #9e9e9e;
              }
              a {
                color: #601feb;
                text-decoration: underline;
              }
            }
          }
          .btn-get-coupon {
            width: 30%;
            background: #ffeff2;
            display: flex;
            align-items: center;
            justify-content: center;
            button {
              background: #ff4f6e;
              box-shadow: none;
              height: 32px;
            }
          }
        }
      }
    }
  }
  .child-card-coupon {
    animation: ${slideInTop} 0.3s ease-out;
    @media screen and (max-width: 1354px) {
      animation: ${Fadein} 0.3s ease-in;
    }
    &.inModal {
      margin: 1rem 1rem 0 1rem;
    }
    .card-coupon {
      border-radius: 8px;
      overflow: hidden;
      position: relative;
      height: 100%;
      &.isMinPriceDiscount {
        opacity: 0.5;
      }
      &.collected {
        pointer-events: none;
        filter: grayscale(100%) !important;
      }
      &.type-coupon-2 {
        .box-ic-type,
        .tag-limit {
          background: ${couponType2.primaryColor} !important;
        }
        .box-btn {
          button {
            border: 1px solid ${couponType2.primaryColor} !important;
            color: ${couponType2.primaryColor} !important;
          }
        }
        .box-detail-coupon {
          background: ${couponType2.secondaryColor} !important;
        }
      }
      .dot {
        width: 100%;
        &:before {
          position: absolute;
          top: 50%;
          left: -8px;
          content: '';
          display: flex;
          width: 17px;
          height: 17px;
          border-radius: 50%;
          background: #fff;
          transform: translateY(-50%);
        }
        &:after {
          position: absolute;
          top: 50%;
          right: -8px;
          content: '';
          display: flex;
          width: 17px;
          height: 17px;
          border-radius: 50%;
          background: #fff;
          transform: translateY(-50%);
        }
      }
      .box-ic-type {
        background: #ff4f6e;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          margin: auto;
        }
        p {
          color: #fff;
          font-size: 12px;
        }
      }
      .box-detail-coupon {
        font-size: 14px;
        padding: 0.5rem 1rem;
        text-align: left;
        background: #ffeff2;
        .detail {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: start;
          min-height: 75px;
          .box-name {
            p {
              font-weight: 700;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            span {
              font-size: 12px;
              color: #424242;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }

        .tag-limit {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          background: #ff4f6e;
          font-size: 12px;
          width: 90px;
          border-radius: 4px;
          padding: 1px;
          &.w-tag-100 {
            width: 115px;
          }
          span {
            color: #fff;
          }
        }

        .box-btn {
          height: 100%;
          text-align: right;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: end;
          .MuiCheckbox-root {
            color: #ff4f6e !important;
          }
          span {
            text-decoration: underline;
            color: #212121;
            cursor: pointer;
            font-size: 12px;
          }
          button {
            color: #ff4f6e;
            background: #fff;
            box-shadow: unset;
            font-size: 12px;
            font-weight: 700;
            border: 1px solid #ff4f6e;
            min-width: unset;
            height: 28px;
            width: 43px;
          }
        }
      }
    }
  }
  .not-found-coupon {
    width: 100%;
    text-align: center;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909090;
  }
  .zone-coupon-page {
    padding: 7rem 0 3rem;
    .box-banner-coupon {
      margin-bottom: 3rem;
      border-radius: 24px;
      overflow: hidden;
      img {
        width: 100%;
      }
    }
    .box-detail-coupon {
      text-align: center;
      h2 {
        font-size: 80px;
        font-weight: 700;
      }
    }
    .tab-menu {
      margin: 3rem 0;
      .amount {
        background: #b9e901;
        color: #212121;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 12px;
      }
      .MuiTabs-flexContainer {
        width: fit-content;
        margin: auto;
        background: #f5f5f5;
        border-radius: 40px;
        padding: 0.3rem;
        .MuiButtonBase-root {
          min-height: unset;
          border-radius: 43px !important;
          &.Mui-selected {
            background: #212121;
            color: #fff;
          }
        }
      }
      .MuiTabs-indicator {
        display: none !important;
      }
    }
    .box-more {
      margin-top: 2rem;
      text-align: center;
      button {
        color: #212121;
        &:hover {
          background: unset;
        }
      }
    }
  }
  .type-coupon-2 {
    .box-ic-type,
    .tag-limit {
      background: ${couponType2.primaryColor} !important;
    }
    .box-btn {
      button {
        border: 1px solid ${couponType2.primaryColor} !important;
        color: ${couponType2.primaryColor} !important;
      }
    }
    .box-detail-coupon {
      background: ${couponType2.secondaryColor} !important;
    }
  }
  @media (max-width: 1440px) {
    .zone-coupon-page {
      padding: 6rem 0 3rem;
      .box-detail-coupon {
        h2 {
          font-size: 70px;
        }
      }
    }
    .zone-coupon {
      header {
        h2 {
          font-size: 70px;
        }
      }
      .box-paragraph {
        margin-bottom: 2rem;
      }
      .box-banner-coupon {
        margin-bottom: 2rem;
      }
    }
  }
  @media (max-width: 1355px) {
    .zone-coupon {
      header {
        h2 {
          font-size: 66px;
        }
      }
      > .MuiBox-root {
        .child-card-coupon {
          &:nth-child(n + 7) {
            display: none;
          }
        }
      }
    }
  }
  @media (max-width: 1230px) {
    .zone-coupon-page {
      padding: 6rem 0 3rem;
      .box-detail-coupon {
        h2 {
          font-size: 60px;
        }
        p {
          font-size: 14px;
        }
      }
    }
    .zone-coupon {
      header {
        h2 {
          font-size: 66px;
        }
      }
    }
  }
  @media (max-width: 991px) {
    .zone-coupon {
      header {
        h2 {
          font-size: 56px;
        }
      }
      .box-paragraph {
        p {
          font-size: 14px;
        }
      }
      > .MuiBox-root {
        .child-card-coupon {
          .box-detail-coupon {
            p,
            span {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  @media (max-width: 768px) {
    .zone-coupon-page {
      padding: 6rem 0 3rem;
      .tab-menu {
        .MuiTabs-flexContainer {
          width: 100%;
          flex-wrap: wrap;
          border-radius: 20px;
          button {
            font-size: 14px;
            width: 181px;
            padding: 9px 5px;
          }
        }
      }
      .box-detail-coupon {
        h2 {
          font-size: 42px;
        }
        p {
          font-size: 14px;
        }
      }
    }
    .zone-coupon {
      header {
        h2 {
          font-size: 40px;
        }
      }
      .box-paragraph {
        p {
          br {
            display: none;
          }
        }
      }
      > .MuiBox-root {
        .child-card-coupon {
          &:nth-child(n + 7) {
            display: none;
          }
          .box-detail-coupon {
            p,
            span {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  @media (max-width: 576px) {
    .zone-coupon-page {
      padding: 6rem 0 3rem;
      .tab-menu {
        .MuiTabs-flexContainer {
          width: 100%;
          flex-wrap: wrap;
          border-radius: 10px;
          button {
            font-size: 14px;
            width: 181px;
            padding: 9px 5px;
            &.MuiButtonBase-root {
              border-radius: 8px !important;
            }
          }
        }
      }
      .box-detail-coupon {
        h2 {
          font-size: 38px;
        }
        p {
          font-size: 14px;
        }
      }
    }
    .zone-coupon {
      header {
        h2 {
          font-size: 40px;
        }
      }
      .box-paragraph {
        p {
          width: 70%;
          br {
            display: none;
          }
        }
      }
      .box-banner-coupon {
        border-radius: 16px;
      }
      > .MuiBox-root {
        .child-card-coupon {
          .box-detail-coupon {
            p,
            span {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  @media (max-width: 425px) {
    .zone-coupon-page {
      padding: 5rem 0 3rem;
      .tab-menu {
        margin: 1rem 0;
        .MuiTabs-flexContainer {
          width: 100%;
          flex-wrap: wrap;
          border-radius: 10px;
          button {
            font-size: 14px;
            width: 160px;
            padding: 9px 5px;
            &.MuiButtonBase-root {
              border-radius: 8px !important;
            }
          }
        }
      }
      .box-banner-coupon {
        margin-bottom: 1rem;
        border-radius: 8px !important;
      }
      .box-detail-coupon {
        h2 {
          font-size: 32px;
        }
        p {
          font-size: 12px;
        }
      }
    }
    .zone-coupon {
      .box-paragraph {
        .btn-all {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          min-width: unset;
          span {
            display: none;
          }
        }
      }
    }
  }
  @media (max-width: 375px) {
    .zone-coupon-page {
      padding: 5rem 0 3rem;
      .tab-menu {
        .MuiTabs-flexContainer {
          width: 100%;
          flex-wrap: wrap;
          border-radius: 10px;
          button {
            font-size: 14px;
            width: 100%;
            padding: 9px 5px;
            &.MuiButtonBase-root {
              border-radius: 8px !important;
            }
          }
        }
      }
      .box-banner-coupon {
        margin-bottom: 1rem;
        border-radius: 8px !important;
      }
      .box-detail-coupon {
        h2 {
          font-size: 22px;
        }
      }
    }
    .zone-coupon {
      header {
        h2 {
          font-size: 36px;
        }
      }
      .box-paragraph {
        p {
          width: 65%;
          font-size: 12px;
          br {
            display: none;
          }
        }
      }
      .box-banner-coupon {
        border-radius: 8px;
      }
    }
  }
`;
export default CouponStyle;
