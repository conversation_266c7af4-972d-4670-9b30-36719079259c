@tailwind base;
@tailwind components;
@tailwind utilities;

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

#__next {
    height: 100%;
}

html,
body {
    scroll-behavior: smooth;
    height: 100%;
    /*width: 100vw;*/
    margin: 0;
    span {
        color: #212121;
    }
    /*div[data-radix-popper-content-wrapper=''] {*/
    /*    background-color: red;*/
    /*    height: 700px;*/
    /*    z-index: 1000;*/
    /*    div[role="option"]{*/
    /*        z-index: 1000;*/
    /*    }*/
    /*}*/
}

.address-container {
    /*display: block;*/
    /*height: 600px;*/
    max-width: 640px;
    border-radius: 16px;
    padding: 0;
    .address-header-wrapper {
        font-size: 20px;
        font-weight: 700;
        padding: 16px;
        border-bottom: 1px solid #DBE2E5
    }
    .address-have-content-wrapper {
        display: flex;
        flex-direction: column;
        padding: 16px;
        margin-bottom: 80px;
        /*height: 470px;*/
        max-height: 470px;
        overflow-y: auto;
        @media (max-width: 991px) {
            @media (max-height: 575px) {
                min-height: unset;
                max-height: 250px;
            }
            @media (max-height: 375px) {
                max-height: 200px;
            }
            @media (max-height: 320px) {
                max-height: 160px;
            }
        }
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-address-button {
            background: #FF4F00;
            width: fit-content;
            &:focus{
                outline:none;
                box-shadow: none;
            }
        }
    }
    .address-content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 24px 0;
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-address-button {
            margin-top: 24px;
            background: #FF4F00;
        }
    }
    .address-add-wrapper {
        min-height: 450px;
        max-height:550px;
        overflow-y: scroll;
        margin-bottom: 88px;
        @media (max-width: 991px) {
            @media (max-height: 575px) {
                min-height: unset;
                max-height:250px;
            }
            @media (max-height: 575px) {
                max-height:200px;
            }
            @media (max-height: 575px) {
                max-height: 150px;
            }
        }
        .form-address-custom-container {
            .text-head {
                font-size: 20px;
                font-weight: 700;
                margin-bottom: 24px;
                position: relative;
            }
        }
        .footer-wrapper {
            display: flex;
            flex-direction: row;
            position: fixed;
            bottom: 0;
            width: 100%;
            padding: 16px;
            border-top: 1px solid #DBE2E5;
            .cancel-button {
                color: black;
                width: 100%;
                font-size: 14px;
                font-weight: 700;
            }
            .confirm-button {
                width: 100%;
                background-color: #212121;
                color: white;
                font-size: 14px;
                font-weight: 700;
            }
        }

    }
    .cancel-button {
        color: black;
        width: 100%;
        background-color: #F5F7F8;
        font-size: 14px;
        font-weight: 700;
        :hover {
            background-color: #F5F7F8 !important;
        }
    }
    .confirm-button {
        width: 100%;
        background-color: #212121;
        color: white;
        font-size: 14px;
        font-weight: 700;
    }
}

.user-container {
    display: block;
    height: fit-content;
    max-width: 640px;
    border-radius: 16px;
    padding: 0;
    .user-header-wrapper {
        font-size: 20px;
        font-weight: 700;
        padding: 16px;
        border-bottom: 1px solid #DBE2E5
    }
    .user-have-content-wrapper {
        display: flex;
        flex-direction: column;
        padding: 16px;
        height: 470px;
        overflow: scroll;
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-address-button {
            background: #FF4F00;
            width: fit-content;
        }
    }
    .user-content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 24px 0;
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-user-button {
            margin-top: 24px;
            background: #FF4F00;
        }
    }
    .user-add-wrapper {
        height: 470px;
        overflow: scroll;
        .form-user-custom-container {
            .text-head {
                font-size: 20px;
                font-weight: 700;
                margin-bottom: 24px;
                position: relative;
            }
            .cancel-button {
                color: black;
                width: 100%;
                background-color: #F5F7F8 !important;
                font-size: 14px;
                font-weight: 700;
            }
            .confirm-button {
                width: 100%;
                background-color: #212121;
                color: white;
                font-size: 14px;
                font-weight: 700;
            }
        }
    }
}

.tax-container {
    display: block;
    height: fit-content;
    max-width: 640px;
    border-radius: 16px;
    padding: 0;
    .tax-header-wrapper {
        font-size: 20px;
        font-weight: 700;
        padding: 16px;
        border-bottom: 1px solid #DBE2E5
    }
    .tax-have-content-wrapper {
        display: flex;
        flex-direction: column;
        padding: 16px;
        height: 470px;
        overflow: scroll;
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-tax-button {
            background: #FF4F00;
            width: fit-content;
        }
    }
    .tax-content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 24px 0;
        .content {
            margin-top: 8px;
            color: #BDBDBD;
            font-size: 14px;
            font-weight: 400;
        }
        .add-tax-button {
            margin-top: 24px;
            background: #FF4F00;
        }
    }
    .tax-add-wrapper {
        height: 670px;
        overflow: scroll;
        margin-bottom: 90px;
        .form-tax-custom-container {
            .text-head {
                font-size: 20px;
                font-weight: 700;
                margin-bottom: 24px;
                position: relative;
            }
            .cancel-button {
                color: black;
                width: 100%;
                background-color: #F5F7F8 !important;
                font-size: 14px;
                font-weight: 700;
            }
            .confirm-button {
                width: 100%;
                background-color: #212121;
                color: white;
                font-size: 14px;
                font-weight: 700;
            }
        }
    }
}

.modal-cancel-order {
    border-radius: 16px;
    padding: 0;
    .text-wrapper {
        .subtitle-bold {
            font-size: 20px;
            font-weight: 700;
        }
        .content {
            font-size: 14px;
            font-weight: 400;
        }
        .content-bold {
            font-size: 14px;
            font-weight: 700;
        }
    }
    form {
        padding: 24px;
    }
}

.modal-quotation-order {
    border-radius: 16px;
    padding: 0;
    .text-wrapper {
        .subtitle-bold {
            font-size: 20px;
            font-weight: 700;
        }
        .content {
            font-size: 14px;
            font-weight: 400;
        }
        .content-bold {
            font-size: 14px;
            font-weight: 700;
        }
    }
    form {
        padding: 24px;
    }
}

.claims-container {
    border-radius: 16px;
    padding: 0;
    .form-claims-container {
        .radio {
            justify-content: flex-start;
            width: 100%;
            position: relative !important;
            .radio-item {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 16px;
                button {
                    width: 24px !important;
                    height: 24px !important;
                }
                input {
                    width: 24px !important;
                    height: 24px !important;
                }
                &.selected {
                    background-color: #FAFAFF;
                    width: 100%;
                }
            }
        }
        .content-wrapper {
            display: flex;
            flex-direction: row;
            width: calc(100% - 60px);
            @media screen and (max-width: 430px) {
               flex-direction: column;
            }
            img {
                @media screen and (max-width: 430px) {
                    margin-bottom: 4px;
                }
            }
            .text-container {
                width: calc(100% - 60px);
                display: flex;
                /*justify-content: space-between;*/
                .text-wrapper {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    width: 100%;
                    .subtitle {
                        font-size: 14px;
                        font-weight: 400;
                    }
                    .subtitle-bold {
                        font-size: 14px;
                        font-weight: 700;
                    }
                }
                .price-container {
                    .price {
                        font-size: 14px;
                        font-weight: 700;
                    }
                    .unit {
                        margin-left: 8px;
                        font-size: 14px;
                        font-weight: 700;
                    }
                }
            }
        }
        .upload-content {
            .upload-button {
                border: 2px dotted #dbe2e5;
                width: 120px;
                height: 120px;
                background-color: #F5F7F8;
                color: #E0E0E0;
                display: flex;
                flex-direction: column;
            }
        }
    }
}
@media (max-width: 768px) {
    .claims-container {
        max-width: 100% !important;
        max-height: 100% !important;
        z-index: 999;
        border-radius: 0;
        border: none;
        .upload-content{
            grid-template-columns: repeat(2, minmax(0, 1fr));
            .upload-button{
                width: 100% !important;
            }
        }

    }
}
.slip-wrapper {
    padding: 0;
    border-radius: 16px;
    .image-wrapper {
        /*max-height: inherit;*/
    }
}
.container-new{
    max-width: 100%;
    padding: 0 1.4rem;
}
.row-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    @media (max-width: 575.98px) {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }
}
.label-field{
    font-size: 14px;
    color: #212121;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    gap: .3rem;
}
.main-field{
    &.search{
        .MuiInputBase-root{
            border-radius: 40px;
        }
    }
    .MuiInputBase-root{
        border-radius: 8px;
    }
    .Mui-focused{
        fieldset{
            border-color: #0050ff !important;
        }
    }
    fieldset{
        border-color: #EEEEEE !important;
    }
}
.modal-app-custom{
    .MuiPaper-root{
        border-radius: 24px;
    }
}
.box-menu-more{
    min-width: 180px;
    svg{
        font-size: 18px;
    }
}
@layer base {
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
}

*{
    -webkit-box-sizing:border-box;
    -moz-box-sizing:border-box;
    box-sizing:border-box;
}
@media (max-width: 768.98px) {
    .container-new{
        padding: 0 1rem;
    }
    .modal-app-custom{
        .MuiPaper-root{
            margin: unset;
            border-radius: 0;
            width: 100%;
            height: 100%;
            max-height: 100% !important;
            max-width: 100% !important;
        }
    }
}
@media (max-width: 425px) {
    .container-new{
        padding: 0 .7rem;
    }
}