import styled, { keyframes } from 'styled-components';

export const slideInRight = keyframes`    
  from {
    transform: translateX(10%);
    opacity: 0;
  }
  to {
    transform: translateX(0%);
    opacity: 1;
  }
`;
export const slideInTop = keyframes`
  from {
    transform: translateY(5%);
    opacity: 0;
  }
  to {
    transform: translateY(0%);
    opacity: 1;
  }
`;

export const slideInBottom = keyframes`
  from {
    transform: translateY(-5%);
    opacity: 0;
  }
  to {
    transform: translateY(0%);
    opacity: 1;
  }
`;
export const emailShake = keyframes`
  0% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(5deg);
  }
`;
export const srFadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
`;

export const changePopup = keyframes`
  0% {
    background-color: transparent;
    z-index: 1;
  }
  50% {
    background-color: #ffffff1f;
    z-index: 1;
  }
  100% {
    background-color: transparent;
    z-index: 1;
  }
`;
export const Fadein = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

export const FadeInStyled = styled.div`
  animation: ${Fadein} 0.3s ease-in;
`;
