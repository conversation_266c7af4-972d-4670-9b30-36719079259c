import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import apiMaterial from '@/services/coating';
import { CoatingState } from '@/store/type/coating';

const initialState = {
  coatings: [],
  coatingLoading: true,
  pending: false,
  error: false,
} as CoatingState;

export const coatingSlice = createSlice({
  name: 'coating',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getCoating.pending, (state) => {
        state.coatingLoading = true;
        state.pending = true;
      })
      .addCase(getCoating.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.coatings = payload;
        state.coatingLoading = false;
      })
      .addCase(getCoating.rejected, (state) => {
        state.pending = false;
        state.coatingLoading = false;
        state.error = true;
      });
  },
});

export const getCoating = createAsyncThunk(
  'coating/getCoating',
  async (category?: number) => {
    try {
      const response = await apiMaterial.getCoating(category);
      if (response.status) {
        return response.data;
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const coatingSelector = (state: RootState) => state.coating;
export default coatingSlice.reducer;
