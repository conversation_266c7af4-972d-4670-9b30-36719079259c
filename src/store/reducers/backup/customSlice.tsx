import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import { CustomState } from '@/store/type/custom';

const initialState = {
  models: [],
  modelLoading: true,
  prices: [],
  priceLoading: true,
  error: false,
} as CustomState;

export const customSlice = createSlice({
  name: 'custom',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(setModels.pending, (state) => {
        state.modelLoading = true;
      })
      .addCase(setModels.fulfilled, (state, { payload }) => {
        state.modelLoading = false;
        state.models = payload;
      })
      .addCase(setPrices.pending, (state) => {
        state.priceLoading = true;
      })
      .addCase(setPrices.fulfilled, (state, { payload }) => {
        state.priceLoading = false;
        state.prices = payload;
      });
  },
});

export const setModels = createAsyncThunk(
  'custom/setModels',
  async (models: any) => {
    try {
      return models;
    } catch (error) {
      console.log(error);
    }
  },
);

export const setPrices = createAsyncThunk(
  'custom/setPrices',
  async (prices: any) => {
    try {
      return prices;
    } catch (error) {
      console.log(error);
    }
  },
);

export const customSelector = (state: RootState) => state.custom;
export default customSlice.reducer;
