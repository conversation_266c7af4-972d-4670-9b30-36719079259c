import { combineReducers } from '@reduxjs/toolkit';
import userReducer from '@/store/reducers/backup/userSlice';
import productReducer from '@/store/reducers/backup/productSlice';
import materialReducer from '@/store/reducers/backup/materialSlice';
import coatingReducer from '@/store/reducers/backup/coatingSlice';
import specialTechnicReducer from '@/store/reducers/backup/specialTechnicSlice';
import customReducer from '@/store/reducers/backup/customSlice';
import cartReducer from '@/store/reducers/backup/cartSlice';
import orderReducer from '@/store/reducers/backup/orderSlice';
import reQuotationReducer from '@/store/reducers/backup/reQuotationSlice';
import couponReducer from '@/store/reducers/backup/couponSlice';

const rootReducer = combineReducers({
  user: userReducer,
  product: productReducer,
  material: materialReducer,
  coating: coatingReducer,
  specialTechnic: specialTechnicReducer,
  custom: customReducer,
  cart: cartReducer,
  order: orderReducer,
  reQuotation: reQuotationReducer,
  coupon: couponReducer,
});

export default rootReducer;
