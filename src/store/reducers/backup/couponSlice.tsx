import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';

const initialState: any = [];
const couponSlice = createSlice({
  name: 'coupon',
  initialState: initialState,
  reducers: {
    setCoupon: (state, action) => {
      return action.payload;
    },
  },
});

export const { setCoupon } = couponSlice.actions;
export const couponSelector = (state: RootState) => state.coupon;
export default couponSlice.reducer;
