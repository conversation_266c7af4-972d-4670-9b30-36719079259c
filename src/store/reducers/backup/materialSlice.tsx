import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import { MaterialState } from '@/store/type/material';
import apiMaterial from '@/services/material';

const initialState = {
  materials: [],
  materialLoading: true,
  pending: false,
  error: false,
} as MaterialState;

export const materialSlice = createSlice({
  name: 'material',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getMaterial.pending, (state) => {
        state.materialLoading = true;
        state.pending = true;
      })
      .addCase(getMaterial.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.materials = payload;
        state.materialLoading = false;
      })
      .addCase(getMaterial.rejected, (state) => {
        state.pending = false;
        state.materialLoading = false;
        state.error = true;
      });
  },
});

export const getMaterial = createAsyncThunk(
  'material/getMaterial',
  async (category?: number) => {
    try {
      const response = await apiMaterial.getMaterial(category);
      if (response.status) {
        return response.data;
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const materialSelector = (state: RootState) => state.material;
export default materialSlice.reducer;
