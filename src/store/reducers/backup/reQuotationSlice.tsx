import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import apiRevision from '@/services/revision';
import {
  ReQuotationItemType,
  ReQuotationState,
} from '@/store/type/reQuotation';

const initialState = {
  reQuotations: {},
  pending: false,
  error: false,
} as ReQuotationState;

export const reQuotationSlice = createSlice({
  name: 'reQuotation',
  initialState,
  reducers: {
    setReQuotationShipping: (state, { payload }) => {
      state.reQuotations = payload;
    },
    setReQuotationAddressSelected: (state, { payload }) => {
      state.reQuotations = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getReQuotation.pending, (state) => {
        state.pending = true;
      })
      .addCase(getReQuotation.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.reQuotations = payload;
      })
      .addCase(getReQuotation.rejected, (state) => {
        state.pending = false;
        state.error = true;
      });
  },
});

// ACTIONS
export const getReQuotation = createAsyncThunk(
  'reQuotation/getReQuotation',
  async (orderId: string) => {
    try {
      const response = await apiRevision.getRevisionById(orderId);
      if (response.status) {
        const total = response.data.reduce(function (
          sum: number,
          obj: ReQuotationItemType,
        ) {
          return sum + obj.totalPrice;
        }, 0);
        return {
          items: response.data
            .reverse()
            .map((cartData: ReQuotationItemType) => {
              return {
                ...cartData,
                itemId: cartData.id,
                shipping: 1,
                shippingCost: 0,
                recipientName: null,
                phoneNumber: null,
                address: null,
                district: null,
                subDistrict: null,
                province: null,
                zipCode: null,
                status: 0,
              };
            }),
          totalPrice: total,
          vat: total * 0.17,
          shippingCost: 0,
          isTax: false,
          taxPayerType: 0,
          taxId: null,
          taxPayerName: null,
          phoneNumber: null,
          address: null,
          district: null,
          subDistrict: null,
          province: null,
          zipCode: null,
          email: null,
          status: 0,
          orderId: orderId,
        };
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const setReQuotationShipping = createAction(
  'reQuotation/setReQuotationShipping',
);

// export const setAddressSelected = createAction(
//   'reQuotation/setReQuotationAddressSelected',
// );

export const reQuotationSelector = (state: RootState) => state.reQuotation;
export default reQuotationSlice.reducer;
