import {
  createAction,
  createAsyncThunk,
  createSlice,
  Draft,
} from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import apiCart from '@/services/cart';
import { OrderItemType, OrderState } from '@/store/type/order';

const initialState = {
  orders: {},
  pending: false,
  error: false,
} as OrderState;

export const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setShippingCode: (state, { payload }) => {
      state.orders = payload;
    },
    setAddressSelected: (state, { payload }) => {
      state.orders = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCartOrders.pending, (state) => {
        state.pending = true;
      })
      .addCase(
        getCartOrders.fulfilled,
        (state: Draft<typeof initialState>, { payload }) => {
          state.pending = false;
          state.orders = payload;
        },
      )
      .addCase(getCartOrders.rejected, (state) => {
        state.pending = false;
        state.error = true;
      });
  },
});

// ACTIONS
export const getCartOrders = createAsyncThunk(
  'order/getCartOrders',
  async () => {
    try {
      const response = await apiCart.getCart();
      if (response.status) {
        const total = response.data.reduce(function (
          sum: number,
          obj: OrderItemType,
        ) {
          return sum + (obj.totalPrice || 0);
        }, 0);
        return {
          items: response.data.reverse().map((cartData: OrderItemType) => {
            return {
              ...cartData,
              itemId: cartData.id,
              shipping: 1,
              shippingCost: 0,
              recipientName: null,
              phoneNumber: null,
              address: null,
              district: null,
              subDistrict: null,
              province: null,
              zipCode: null,
              status: 0,
            };
          }),
          totalPrice: total,
          vat: total * 0.17,
          shippingCost: 0,
          isTax: false,
          taxPayerType: 0,
          taxId: null,
          taxPayerName: null,
          phoneNumber: null,
          address: null,
          district: null,
          subDistrict: null,
          province: null,
          zipCode: null,
          email: null,
          status: 0,
        };
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const setShippingCode = createAction('order/setShippingCode');

// export const setAddressSelected = createAction('order/setAddressSelected');

export const orderSelector = (state: RootState) => state.order;
export default orderSlice.reducer;
