import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import { SpecialTechnicState } from '@/store/type/specialTechnic';
import apiSpecialTechnic from '@/services/specialTechnic';

const initialState = {
  specialTechnics: [],
  pending: false,
  error: false,
} as SpecialTechnicState;

export const specialTechnicSlice = createSlice({
  name: 'specialTechnic',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getSpecialTechnic.pending, (state) => {
        state.pending = true;
      })
      .addCase(getSpecialTechnic.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.specialTechnics = payload;
      })
      .addCase(getSpecialTechnic.rejected, (state) => {
        state.pending = false;
        state.error = true;
      });
  },
});

export const getSpecialTechnic = createAsyncThunk(
  'specialTechnic/getSpecialTechnic',
  async (category?: number) => {
    try {
      const response = await apiSpecialTechnic.getSpecialTechnic(category);
      if (response.status) {
        return response.data;
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const specialTechnicSelector = (state: RootState) =>
  state.specialTechnic;
export default specialTechnicSlice.reducer;
