import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import apiCart from '@/services/cart';
import { CartState } from '@/store/type/cart';
import { RootState } from '@/store/index';

const initialState = {
  cart: [],
  pending: false,
  error: false,
} as CartState;

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getCart.pending, (state) => {
        state.pending = true;
        state.cart = [];
      })
      .addCase(getCart.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.cart = payload;
      })
      .addCase(getCart.rejected, (state) => {
        state.pending = false;
        state.error = true;
      });
  },
});

// ACTIONS
export const getCart = createAsyncThunk('cart/getCart', async () => {
  try {
    const response = await apiCart.getCart();
    if (response.status) {
      response.data.reverse();
      return response.data;
    }
    return response.error;
  } catch (error) {
    console.log(error);
  }
});
export const setShippingCode = createAction('cartOrders/setShippingCode');

export const cartSelector = (state: RootState) => state.cart;
export default cartSlice.reducer;
