import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import { UserState } from '@/store/type/user';
import apiUser from '@/services/user';

const initialState = {
  user: {},
  pending: false,
  error: false,
} as UserState;

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getUserProfile.pending, (state) => {
        state.pending = true;
      })
      .addCase(getUserProfile.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.user = payload;
      })
      .addCase(getUserProfile.rejected, (state) => {
        state.pending = false;
        state.error = true;
      })
      .addCase(resetUser, (state) => {
        state.user = initialState.user;
      });
  },
});

// ACTIONS
export const getUserProfile = createAsyncThunk(
  'user/getUserProfile',
  async () => {
    try {
      const response = await apiUser.getProfile();
      if (response.status) {
        return response.data;
      }
      return response.error;
    } catch (error) {
      console.log(error);
    }
  },
);

export const resetUser = createAction('user/reset');

export const userSelector = (state: RootState) => state.user;
export default userSlice.reducer;
