import { ProductState } from '@/store/type/product';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import apiProducts from '@/services/product';
import { RootState } from '@/store/index';

const initialState = {
  products: [],
  productLoading: false,
  pending: false,
  error: false,
} as ProductState;

export const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getProduct.pending, (state) => {
        state.productLoading = true;
        state.pending = true;
      })
      .addCase(getProduct.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.products = payload;
        state.productLoading = false;
      })
      .addCase(getProduct.rejected, (state) => {
        state.productLoading = false;
        state.pending = false;
        state.error = true;
      });
  },
});

export const getProduct = createAsyncThunk('product/getProduct', async () => {
  try {
    const response = await apiProducts.getProducts();
    if (response.status) {
      response.data.reverse();
      return response.data;
    }
    return response.error;
  } catch (error) {
    console.log(error);
  }
});

export const productSelector = (state: RootState) => state.product;
export default productSlice.reducer;
