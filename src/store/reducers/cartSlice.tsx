import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import apiCart from '@/services/cart';
import { IMyCartState } from '@/store/type/cart';
import {
  calculateSpecialTechniquesTotal,
  calculateTotalSummaryPrice,
} from '@/utils/calculator';

const initialState: IMyCartState = {
  cart: [],
  pending: false,
  error: false,
};

export const fetchGetMyCart = createAsyncThunk(
  'cart/fetchGetMyCart',
  async () => {
    try {
      const res = await apiCart.getMyCart();
      if (!res.isError) {
        const cartTotalPrice = res.data.map((item: any) => {
          const summaryPrice = {
            periodPrice: item.productPeriodPrice,
            specialTechnicPrice: calculateSpecialTechniquesTotal(
              item.specialTechnics,
            ),
            amountPrice: item.amountUnitPrice * item.amount,
          };
          return {
            ...item,
            totalPrice: calculateTotalSummaryPrice(summaryPrice),
          };
        });
        return cartTotalPrice;
      }
      return res.error;
    } catch (error) {
      console.log(error);
      throw error;
    }
  },
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchGetMyCart.pending, (state) => {
        state.pending = true;
        state.cart = [];
      })
      .addCase(fetchGetMyCart.fulfilled, (state, { payload }) => {
        state.pending = false;
        state.cart = payload;
      })
      .addCase(fetchGetMyCart.rejected, (state) => {
        state.pending = false;
        state.error = true;
      });
  },
});

export const dataAddCartSelector = (state: RootState) => state.cart;

const cartReducer = cartSlice.reducer;
export default cartReducer;
