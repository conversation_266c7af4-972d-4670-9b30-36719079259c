import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '@/store/index';
import { TypeDataAreaSize } from '@/store/type/specialTechnic';

type TInitialState = {
  order: {
    product: {
      id: string;
      name: string;
      imageUrl: string;
      category: string;
    };
    model: {
      id: string;
      name: string;
      imageUrl?: string;
      modelCode?: string;
      modelSize?: any[];
    };
    modelSize: {
      id?: number;
      isThreeD?: boolean;
      modelId?: number;
      width?: number;
      height?: number;
      length?: number;
      unfoldedWidth?: number;
      unfoldedHeight?: number;
      modelSizeId?: number;
      unfoldedSizeId?: number;
      unfoldedSizeName?: string;
      modelSizeConfig?: any[];
    };
    material: {
      id?: number;
      materialConfig?: {
        gramsId: number;
        gsm: number;
        id: number;
        materialsId: number;
        materialsName: string;
        mm: number;
      };
      modelSize?: {
        height: number;
        id: number;
        length: number;
        unfoldedSizeId: number;
        unfoldedSizeName: string;
        width: number;
      };
    };
    printing?: {
      printingId?: number;
      modelSizeConfigDetail?: {
        amount?: number;
        period?: number;
        price?: number;
      }[];
      modelSizeConfigCoating?: {
        coatingId: number;
      }[];
    };
    amountConfigCoating?: {
      amount?: number;
      id?: number;
      modelSizeConfigCoating?: {
        id?: number;
        coating?: {
          id: number;
          imageUrl: string;
          isActive: boolean;
          name: string;
          price: number;
        };
      }[];
    };
    coating?: {
      id?: number;
      coating?: {
        id: number;
        imageUrl: string;
        isActive: boolean;
        name: string;
        price: number;
      };
    };
    specialTechniques: TypeDataAreaSize[];
    specialTechnicSelectConfig?: number;
    period?: {
      id: number;
      imageUrl: string;
      isActive: boolean;
      maxPeriod: number;
      minPeriod: number;
      name: string;
      price: number;
      completionDate?: string;
    };
    artwork?: {
      id?: number;
      name?: string;
      description?: string;
    };
    sampleProduct?: {
      id: number;
      imageUrl: string;
      name: string;
      description: string;
      price: number;
    };
    zipcodeData?: {
      id: number;
      zipcode: string;
      value: string;
      errorMessage?: string;
    };
    summaryPrice?: {
      periodPrice: number;
      specialTechnicPrice: number;
      amountPrice: number;
      examplePrice: number;
    };
  };
};
const initialState: TInitialState = {
  order: {
    product: {
      id: '',
      name: '',
      imageUrl: '',
      category: '',
    },
    model: {
      id: '',
      name: '',
    },
    modelSize: {},
    material: {},
    printing: {},
    sampleProduct: {
      id: 1,
      imageUrl: '/images/image-product-sample-1.svg',
      name: 'Soft and Online Proof',
      description:
        'ส่งลิงก์ไฟล์ PDF และ คลิปVDO ตัวอย่างสินค้า ให้ตรวจสอบก่อนพิมพ์',
      price: 0,
    },
    specialTechnicSelectConfig: 1,
    specialTechniques: [],
  },
};
const orderSlice = createSlice({
  name: 'order',
  initialState: initialState,
  reducers: {
    setOrder: (state, { payload }) => {
      state.order = payload;
    },
  },
});

export const { setOrder } = orderSlice.actions;
export const dataAddOrderSelector = (state: RootState) => state.order;
export default orderSlice.reducer;
