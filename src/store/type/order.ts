export type OrderState = {
  orders: OrderType[];
  pending: boolean;
  error: boolean;
};

export type OrderType = {
  items: OrderItemType[];
  totalPrice: number;
  vat: number;
  shippingCost: number;
  isTax: boolean;
  taxPayerType: number;
  taxId: string;
  taxPayerName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  email: string;
  status: number;
};

export type OrderItemType = {
  id: number;
  width: number;
  length: number;
  height: number;
  printing: {
    id: number;
    price: number;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
    price: number;
  };
  specialTechnics: OrderItemSpecialTechnicsType[];
  material: {
    id: number;
    name: string;
    gram: number;
    imageUrl: string;
    amount: number;
    price: number;
  };
  model: {
    name: string;
    imageUrl: string;
  };
  artwork: {
    status: boolean;
    url: string;
    price: number;
  };
  productDemo: {
    id: number;
    price: number;
  };
  description: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
  itemId: number;
  shipping: number;
  shippingCost: number;
  name?: string;
  recipientName: string;
  phoneNumber: string;
  email: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  status: number;
};

export type OrderItemSpecialTechnicsType = {
  name: string;
  imageUrl: string;
  width: number;
  height: number;
};
