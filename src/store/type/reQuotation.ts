export type ReQuotationState = {
  reQuotations: ReQuotationType;
  pending: boolean;
  error: boolean;
};

export type ReQuotationType = {
  items: ReQuotationItemType[];
  orderId: number;
  step?: boolean;
  totalPrice: number;
  vat: number;
  shippingCost: number;
  isTax: boolean;
  taxPayerType: number;
  taxId: string;
  taxPayerName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  email: string;
  status: number;
};

export type ReQuotationItemType = {
  id: number;
  width: number;
  length: number;
  height: number;
  printing: {
    id: number;
    price: number;
  };
  model: {
    id: number;
    name: string;
    imageUrl: string;
    productId: number;
  };
  material: {
    id: number;
    amount: number;
    name: string;
    gram: number;
    imageUrl: string;
    price: number;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
    price: number;
  };
  specialTechnics?: ReQuotationSpecialTechnicType[];
  artwork: {
    status: boolean;
    url: string;
    price: number;
  };
  productDemo: {
    id: number;
    price: number;
  };
  description?: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
  itemId: number;
  shipping: number;
  shippingCost: number;
  recipientName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  status: number;
};

export type ReQuotationSpecialTechnicType = {
  id: number;
  name: string;
  imageUrl: string;
  price: number;
  width: number;
  height: number;
};
