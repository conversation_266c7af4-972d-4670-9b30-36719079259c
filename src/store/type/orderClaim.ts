export type OrderClaimTypeState = {
  id: number;
  orderNumber: string;
  amount: number;
  images: ClaimImageType[];
  description: string;
  claimProblem: string;
  claimCause: string;
  auditDetail: string;
  auditDescription: null;
  status: number;
  createdDate: string;
  modifiedDate: string;
  item: ClaimItemType;
  shippingDetail: ClaimShippingDetailType;
};

export type ClaimImageType = {
  id: number;
  url: string;
};

export type ClaimItemType = {
  id: number;
  width: number;
  length: number;
  height: number;
  printing: number;
  model: {
    id: number;
    name: string;
    imageUrl: string;
    productId: number;
  };
  material: {
    id: number;
    name: string;
    gram: number;
    imageUrl: string;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
  };
  specialTechnics: ClaimItemSpecialTechnicType[];
  isArtwork: false;
  artworkUrl: string;
  productDemo: number;
  description: string;
  amount: number;
  unitPrice: number;
  totalPrice: number;
};

export type ClaimItemSpecialTechnicType = {
  id: number;
  name: string;
  imageUrl: string;
  width: number;
  height: number;
};

export type ClaimShippingDetailType = {
  shipping: number;
  shippingCost: number;
  trackingNumber: string;
  recipientName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  email: string;
  isConfirmReceipt: true;
  deliveryDate: string;
  receivedDate: string;
  shippingName: string;
  receivedDescription: string;
};
