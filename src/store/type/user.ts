export type UserState = {
  user: UserTypeState;
  pending: boolean;
  error: boolean;
};

export type UserTypeState = {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  imageUrl: string;
  emailVerified: boolean;
  isAdmin: boolean;
  isAnonymous: boolean;
  isOwner: boolean;
  isSuperAdmin: boolean;
  isUser: boolean;
  shippingAddress: UserShippingAddressType[];
  taxAddress: UserTaxAddressType[];
  provider: string;
};

export type UserShippingAddressType = {
  id: number;
  name: string;
  phoneNumber: string;
  address: string;
  zipCode: string;
  province: string;
  district: string;
  subDistrict: string;
  email: string;
  isDefault: boolean;
  taxPayerType?: number;
  taxId?: string;
  isTax?: boolean;
};

export type UserTaxAddressType = {
  id: number;
  name: string;
  phoneNumber: string;
  address: string;
  zipCode: string;
  province: string;
  district: string;
  subDistrict: string;
  email: string;
  isDefault: boolean;
  taxPayerType: number;
  taxId: string;
  isTax?: boolean;
};

export type FormAddressType = {
  taxPayerName: string;
  firstname: string;
  lastname: string;
  phoneNumber: string;
  address: string;
  zipCode: string;
  province: string;
  district: string;
  subDistrict: string;
  email: string;
  isDefault: boolean;
  taxPayerType: string;
  taxId: string;
  isTax: boolean;
};
