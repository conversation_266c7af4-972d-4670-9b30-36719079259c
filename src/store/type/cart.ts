import { CartItemType } from '@/types/cart';

export type CartState = {
  cart: CartItemType[];
  pending: boolean;
  error: boolean;
};

export type DataFormDetailType = {
  modelId: number | null;
  modelSizeId: number | null;
  unfoldedSizeId: number | null;
  width: number | null;
  height: number | null;
  length: number | null;
  materialConfigId: number | null;
  printingId: number | null;
  modelSizeConfigDetailId: number | null;
  customizeSpecialTechnic:
    | {
        specialTechnicConfigId: number;
      }[]
    | null;
  customizeCoating:
    | {
        modelSizeConfigCoatingId: number;
      }[]
    | null;
  sampleProduct: number;
  shippingType: number;
  zipcode?: string;
  productPeriodId: number | null;
};

export interface IMyCartState {
  cart: IMyCartItem[];
  pending: boolean;
  error: boolean;
}
export interface ICartValue {
  cartValue: IMyCartItem[];
}
export interface IMyCartItem {
  cartId: number;
  customId: number;
  productId: number;
  productName: string;
  modelId: number;
  modelImage: string;
  modelName: string;
  modelSizeId: number;
  unfoldedSizeId: number;
  unfoldedSizeName: string;
  width: number;
  height: number;
  length: number;
  materialConfigId: number;
  materialName: string;
  gramId: number;
  gramGsm: number;
  gramMM: number;
  printingId: number;
  printName: string;
  modelSizeConfigDetailId: number;
  amount: number;
  amountUnitPrice: number;
  period: number;
  productPeriodId: number;
  productPeriodName: string;
  productPeriodPrice: number;
  productPeriodImageUrl: string;
  productMaxPeriod: number;
  productMinPeriod: number;
  coatings: ICoatingItem[];
  specialTechnics: ISpecialTechnicItem[];
  sampleProductName: string;
  totalPrice: number;
  zipcode?: string;
}

export interface ISpecialTechnicItem {
  id: number;
  customId: number;
  price: number;
  specialTechnicId: number;
  specialTechnicName: string;
  areaSizePercentageId: number;
  areaSizePercentageName: string;
  areaSizePercentage: number;
}

export interface ICoatingItem {
  id: number;
  customId: number;
  coatingId: number;
  coatingName: string;
  price: number;
}
