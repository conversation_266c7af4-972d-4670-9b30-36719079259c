export type CustomizeType = {
  width: number;
  length: number;
  height: number;
  materialId: number;
  amount: number;
  printing: number;
  coatingId: number;
  specialTechnic: [];
  isArtwork: boolean;
  artworkUrl: string;
  productDemo: number;
  description: string;
  status: number;
  unitPrice: number;
  trackingNumber: string;
  totalPrice: number;
};

export type CustomizeFormType = {
  modelId: number;
  width: number;
  length: number;
  height: number;
  amount: number;
  unitPrice: number;
  materialId: number;
  printingId: number;
  printingPrice: number;
  coatingId: number;
  coatingPrice: number;
  isArtwork: boolean;
  artworkUrl: string;
  artworkPrice: number;
  productDemoId: number;
  productDemoPrice: number;
  description: string;
  specialTechnic: CartSpecialTechnicType[] | [];
};

export type CartSpecialTechnicType = {
  id: number;
  price: number;
  width: number;
  height: number;
};

export type DataSizeType = {
  width: number;
  height: number;
  length: number;
  isThreeD?: boolean;
  modelId?: number;
  unfoldedWidth?: number;
  unfoldedHeight?: number;
};
