export type ModelType = {
  id: number;
  name: string;
  imageUrl: string;
  modelCode: string;
  description: string;
  isPopular: boolean;
  isActive: boolean;
  productId: number;
  createdDate: string;
  modifiedDate: string;
  prices: PriceType[];
  thumbnails: ThumbnailType[];
};

export type PriceType = {
  id: number;
  modelId: number;
  amount: number;
  price: number;
  percentage: number;
};

export type ThumbnailType = {
  id: number;
  modelId: number;
  isCover: boolean;
  thumbnailUrl: string;
};
