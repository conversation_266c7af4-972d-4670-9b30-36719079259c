export type OrdersType = {
  items: OrderItemType[] | undefined;
  isTax: boolean;
  taxPayerType: number;
  taxId: string;
  taxPayerName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  email: string;
};

export type OrderItemType = {
  itemId: number;
  totalPrice: number;
  shipping: number;
  shippingCost: number;
  recipientName: string;
  phoneNumber: string;
  address?: string | undefined;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
};

export type OrderType = {
  id: number;
  userId: number;
  orderNumber: string;
  totalPrice: number;
  vat: number;
  shippingCost: number;
  isTax: boolean;
  taxPayerType: number;
  taxId: string;
  taxPayerName: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  email: string;
  paymentStatus: false;
  isConfirm: false;
  status: number;
  isDelete: false;
  description: string;
  createdDate: string;
  modifiedDate: string;
  orderItems: OrderItemType[];
  taxDetail: any;
  customer: any;
  amount: any;
  artworkUrl: string;
};
