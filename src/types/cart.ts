export type CartItemType = {
  id: number;
  modelId: number;
  materialId: number;
  width: number;
  length: number;
  height: number;
  amount: number;
  printing: {
    id: number;
    price: number;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
    price: number;
  };
  specialTechnics: CartSpecialTechnicsType[];
  material: {
    id: number;
    name: string;
    gram: number;
    imageUrl: string;
    amount: number;
    price: number;
  };
  model: {
    name: string;
    imageUrl: string;
  };
  artwork: {
    status: boolean;
    url: string;
    price: number;
  };
  productDemo: {
    id: number;
    price: number;
  };
  description: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
};

export type CartSpecialTechnicsType = {
  id: number;
  name: string;
  imageUrl: string;
  price: number;
  width: number;
  height: number;
};

export type GetCartType = {
  id: number;
  width: number;
  length: number;
  height: number;
  printing: {
    id: number;
    price: number;
  };
  model: {
    id: number;
    name: string;
    imageUrl: string;
    productId: number;
  };
  material: {
    id: number;
    name: string;
    gram: number;
    imageUrl: string;
    amount: number;
    price: number;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
    price: number;
  };
  specialTechnics: GetCartSpecialTechnics[];
  artwork: {
    status: true;
    url: string;
    price: number;
  };
  productDemo: {
    id: number;
    price: number;
  };
  description: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
};

export type GetCartSpecialTechnics = {
  id: number;
  name: string;
  imageUrl: string;
  price: number;
  width: number;
  height: number;
};
