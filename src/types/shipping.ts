import { AddressType } from '@/types/address';

export type ShippingType = {
  orderId: number;
  orderNumber: string;
  taxDetail: AddressType;
  orderItemId: number;
  item: any;
  shippingDetail: ShippingDetailType;
  createdDate: string;
  amount: number;
  claimProblem: string;
  claimCause: string;
  auditDetail: string;
  trackingNumber: string;
};

export type ShippingDetailType = {
  shipping: number;
  shippingCost: number;
  trackingNumber: number;
  recipientName: string;
  email: string;
  phoneNumber: string;
  address: string;
  district: string;
  subDistrict: string;
  province: string;
  zipCode: string;
  isConfirmReceipt: true;
  deliveryDate: string;
  receivedDate: number;
  shippingName: null;
  receivedDescription: string;
};
