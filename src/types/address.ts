export type AddressType = {
  address: string;
  createdDate: string;
  district: string;
  email: string;
  id: number;
  isTax: boolean;
  modifiedDate: string;
  name: string;
  phoneNumber: string;
  province: string;
  subDistrict: string;
  taxId: string;
  taxPayerType: number;
  taxPayerName: string;
  userId: number;
  zipCode: string;
};

export type ZipcodeType = {
  status: boolean;
  message: string;
  data: {
    id: number;
    zipcode: string;
    districtId: {
      id: number;
      geoId: number;
      districtCode: string;
      name: string;
    };
    provinceId: {
      id: number;
      geoId: number;
      provinceCode: string;
      name: string;
    };
  };
};

export type SubDistrictType = {
  status: boolean;
  message: string;
  data: SubDistrictDetailsType[];
};

export type SubDistrictDetailsType = {
  id: number;
  geoId: number;
  subDistrictCode: string;
  name: string;
};
