export type RevisionType = {
  customer: {
    id: number;
    fullName: string;
    phoneNumber: string;
    email: string;
  };
  id: number;
  orderNumber: string;
  vat: number;
  shippingCost: number;
  paymentStatus: boolean;
  isConfirm: boolean;
  status: number;
  isDelete: boolean;
  isRevision: boolean;
  taxDetail: {
    isTax: boolean;
    taxPayerType: number;
    taxId: string;
    taxPayerName: string;
    phoneNumber: string;
    address: string;
    district: string;
    subDistrict: string;
    province: string;
    zipCode: string;
    email: string;
  };
  orderItems: [
    {
      id: number;
      status: number;
      item: {
        id: number;
        width: number;
        length: number;
        height: number;
        printing: {
          id: number;
          price: number;
        };
        model: {
          id: number;
          name: string;
          imageUrl: string;
          productId: number;
        };
        material: {
          id: number;
          name: string;
          gram: number;
          imageUrl: string;
          amount: number;
          price: number;
        };
        coating: {
          id: number;
          name: string;
          imageUrl: string;
          price: number;
        };
        specialTechnics: specialTechnics[];
        artwork: {
          status: false;
          url: string;
          price: number;
        };
        productDemo: {
          id: number;
          price: number;
        };
      };
      shippingDetail: {
        shipping: number;
        shippingCost: number;
        trackingNumber: boolean;
        recipientName: string;
        phoneNumber: string;
        address: string;
        district: string;
        subDistrict: string;
        province: string;
        zipCode: string;
        email: string;
        isConfirmReceipt: boolean;
        deliveryDate: string;
        receivedDate: string;
        shippingName: string;
        receivedDescription: string;
      };
    },
  ];
  revisions: revisions[];
  description: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
};

export type specialTechnics = {
  id: number;
  price: number;
  width: number;
  height: number;
};

export type revisions = {
  id: number;
  revisionNumber: number;
  detail: string;
  timestamp: string;
};

export type RevisionItemType = {
  id: number;
  width: number;
  length: number;
  height: number;
  printing: {
    id: number;
    price: number;
  };
  model: {
    id: number;
    name: string;
    imageUrl: string;
    productId: number;
  };
  material: {
    id: number;
    name: string;
    gram: number;
    imageUrl: string;
    amount: number;
    price: number;
  };
  coating: {
    id: number;
    name: string;
    imageUrl: string;
    price: number;
  };
  specialTechnics: [
    {
      id: number;
      name: string;
      imageUrl: string;
      price: number;
      width: number;
      height: number;
    },
  ];
  artwork: {
    status: true;
    url: string;
    price: number;
  };
  productDemo: {
    id: number;
    price: number;
  };
  description: string;
  unitPrice: number;
  totalPrice: number;
  createdDate: string;
  modifiedDate: string;
};
