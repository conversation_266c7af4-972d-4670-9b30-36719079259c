import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

const CardSkeleton = () => {
  const arraySkeletonModel = new Array(4).fill(null);
  return (
    <>
      {arraySkeletonModel.map((_, index: number) => (
        <div className="flex flex-col justify-center items-center" key={index}>
          <Skeleton className="w-[140px] h-[140px]" />
          <Skeleton className="w-[100px] h-[20px] mt-3" />
        </div>
      ))}
    </>
  );
};

export default CardSkeleton;
