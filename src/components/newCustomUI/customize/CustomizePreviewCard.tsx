import React from 'react';
import styled from 'styled-components';
import CircularProgress from '@mui/material/CircularProgress';

const CustomizePreviewCardStyle = styled.div`
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px #0000000d;
  border-radius: 16px;
  width: 100%;
  padding: 1rem;
  display: flex;
  align-items: center;
  height: 58px;
  justify-content: space-between;
  font-size: 14px;
`;
type Props = {
  title: string;
  value?: React.ReactNode;
};
const CustomizePreviewCard = ({ title, value }: Props) => {
  return (
    <CustomizePreviewCardStyle>
      <div className={'font-bold'}>{title}</div>
      {value ? (
        <div>{value}</div>
      ) : (
        <CircularProgress size={'20px'} color={'inherit'} />
      )}
    </CustomizePreviewCardStyle>
  );
};

export default CustomizePreviewCard;
