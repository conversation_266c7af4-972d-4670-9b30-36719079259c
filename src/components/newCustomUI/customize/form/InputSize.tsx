import React from 'react';
import styled from 'styled-components';
import TextField from '@mui/material/TextField';
import { FormHelperText, InputAdornment } from '@mui/material';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';

const InputSizeStyle = styled.div`
  .MuiFormControl-root {
    > .MuiInputBase-root {
      //border: 1px solid #eeeeee;
      border-radius: 6px;
      padding-left: 0.8rem;
      &.Mui-focused {
        input {
          padding-left: 0.5rem;
          border-left: 2px solid #0050ff;
        }
        fieldset {
          border-color: #0050ff !important;
        }
      }

      > .MuiInputAdornment-root {
        margin-right: 0.8rem;
      }
    }
    fieldset {
      border-color: #eeeeee;
    }
  }

  input {
    font-size: 14px;
    padding-left: 0.5rem;
    border-left: 1px solid #eeeeee;
  }
  .label-start {
    font-weight: bold !important;
    color: #0344dc;
    &.w {
      color: #fe4902;
    }
    &.h {
      color: #00a914;
    }
  }
  p {
    margin-top: 0.1rem;
    font-size: 12px;
    color: #bdbdbd;
  }
  @media (max-width: 575.98px) {
    .MuiInputAdornment-root {
      min-width: 14px;
      justify-content: center;
    }
  }
`;
type Props = {
  name: string;
  description: string;
  formik: FormikProps<DataFormDetailType>;
  value?: string;
  error?: any;
  helperText?: any;
};
const InputSize = ({
  name,
  description,
  formik,
  error,
  helperText,
  value,
}: Props) => {
  const handleNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;

    if (value === '') {
      formik.handleChange(event);
      return;
    }
    const decimalRegex = /^\d*\.?\d{0,2}$/;

    if (decimalRegex.test(value)) {
      formik.handleChange(event);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      !/[0-9.]/.test(event.key) &&
      !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(
        event.key,
      )
    ) {
      event.preventDefault();
    }
  };
  return (
    <InputSizeStyle>
      <TextField
        autoComplete={'off'}
        id={`size.${name}`}
        name={`size.${name}`}
        value={value}
        onChange={handleNumberChange}
        onKeyDown={handleKeyPress}
        onBlur={formik.handleBlur}
        fullWidth
        size={'small'}
        inputProps={{
          inputMode: 'decimal',
          pattern: '[0-9]*[.]?[0-9]{0,2}',
        }}
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment position="start">
                <span className={`label-start ${name}`}>
                  {name.toUpperCase()}
                </span>
              </InputAdornment>
            ),
          },
        }}
        error={error}
      />
      <p>{description}</p>
      {error && <FormHelperText error>{helperText}</FormHelperText>}
    </InputSizeStyle>
  );
};

export default InputSize;
