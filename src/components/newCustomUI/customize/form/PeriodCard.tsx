import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';

const PeriodCardStyle = styled.div`
  width: 100%;
  padding: 0.5rem;
  border-radius: 16px;
  border: 1px solid #eeeeee;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.7rem;
  cursor: pointer;
  height: 183px;
  &:hover,
  &.active {
    border: 2px solid #0050ff;
    background: #eef3ff;
  }
  .top-section {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 8px;
    .name {
      font-weight: bold;
      font-size: 14px;
    }
  }
  .description {
    text-align: center;
    font-size: 12px;
  }
  .endProcess {
    text-align: center;
    font-weight: 700;
    font-size: 18px;
  }
  .price {
    border-radius: 6px;
    padding: 0.4rem;
    text-align: center;
    font-size: 14px;
    background: #eef3ff;
    color: #0050ff;
    font-weight: bold;
  }
  @media (min-width: 1201px) {
    .endProcess {
      br {
        display: none;
      }
    }
  }
  @media (max-width: 1300px) {
    justify-content: space-between;
    min-height: 190px;
    height: auto;
    .endProcess {
      font-size: 16px;
    }
  }
  @media (max-width: 991.98px) {
    .endProcess {
      br {
        display: none !important;
      }
    }
  }
  @media (max-width: 575.98px) {
    .endProcess {
      font-size: 14px;
      br {
        display: unset !important;
      }
    }
  }
  @media (max-width: 425px) {
    .top-section {
      img {
        width: 20px;
      }
      .name {
        font-size: 12px;
      }
    }
    .endProcess {
      font-size: 12px;
      br {
        display: unset !important;
      }
    }
  }
`;
type Props = {
  imageUrl: string;
  name: string;
  color: string;
  bgColor: string;
  description?: string;
  price: number;
  isActive?: boolean;
  onSelectValue: () => void;
  completionDate: string;
};
const PeriodCard = ({
  imageUrl,
  name,
  color,
  bgColor,
  description,
  price,
  onSelectValue,
  isActive,
  completionDate,
}: Props) => {
  return (
    <PeriodCardStyle
      onClick={onSelectValue}
      className={isActive ? 'active' : ''}
    >
      <div className={'top-section'} style={{ backgroundColor: bgColor }}>
        <Image src={imageUrl} alt={'icon'} width={25} height={25} />
        <div className={'name'} style={{ color: color }}>
          {name}
        </div>
      </div>
      <div>
        {description && <div className={'description'}>{description}</div>}
        {completionDate && (
          <div className={'endProcess'}>ผลิตเสร็จ {completionDate}</div>
        )}
      </div>
      <div className={'price'}>฿{numberWithCommas(price, 2)}</div>
    </PeriodCardStyle>
  );
};

export default PeriodCard;
