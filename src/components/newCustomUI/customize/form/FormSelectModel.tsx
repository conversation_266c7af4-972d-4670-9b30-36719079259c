import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FreeMode } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import SelectValueCard from '@/components/newCustomUI/customize/form/SelectValueCard';
import ModalAppCustom from '@/components/common/ModalAppCustom';
import ProductsListsCard from '@/components/newCustomUI/customize/form/ProductsListsCard';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';
import apiModel from '@/services/model';
import { isEmpty } from 'lodash';
import { Skeleton } from '@/components/ui/skeleton';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import apiProducts from '@/services/product';

const FormSelectModelStyle = styled.div`
  .model {
    margin-top: 1rem;
    .mySwiper {
      cursor: grab;
      &:active {
        cursor: grabbing;
      }
    }
    .card-select-model {
      //cursor: pointer;
      &.active {
        .img-model {
          border: 2px solid #0050ff;
        }
        .name {
          color: #0050ff;
        }
      }
      img {
        border-radius: 20px;
        width: 100%;
      }
      .img-model {
        width: 100%;
        height: 100%;
        aspect-ratio: 1 / 1;
        background-position: center !important;
        background-size: cover !important;
        border-radius: 16px;
        overflow: hidden;
      }
      .name {
        text-align: center;
        margin-top: 0.5rem;
        font-size: 14px;
      }
    }
  }
  @media (max-width: 1200px) {
    .model {
      .card-select-model {
        img {
          border-radius: 12px;
        }
        .name {
          font-size: 12px;
        }
      }
    }
  }
  @media (max-width: 991.98px) {
    .model {
      .card-select-model {
        img {
          border-radius: 30px;
        }
      }
    }
  }
  @media (max-width: 767.98px) {
    .model {
      .card-select-model {
        img {
          border-radius: 20px;
        }
      }
    }
  }
  @media (max-width: 575.98px) {
    .model {
      .card-select-model {
        img {
          border-radius: 12px;
        }
        .name {
          font-size: 12px;
          flex-wrap: wrap;
        }
      }
    }
  }
`;
const FormSelectModel = () => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingModel, setIsLoadingModel] = useState(false);
  const [models, setModels] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [tabValue, setTabValue] = useState('0');
  const [filters] = useState<any>({
    size: 10,
    page: 0,
    ascending: true,
  });
  const fetchDataProductCategory = async () => {
    const res = await apiProducts.getProductCategory();
    if (!res.isError) {
      setCategories(res.data.content.filter((item: any) => item.isActive));
    }
  };
  const fetchDataProducts = async (params: any, categoryId?: string) => {
    setIsLoading(true);
    const res = await apiProducts.getProductList(params);
    if (!res.isError) {
      const productsData = res.data.content.filter(
        (item: any) => item.isActive,
      );
      if (!categoryId) {
        dispatch(
          setOrder({
            ...order,
            product: {
              id: productsData[0].id,
              name: productsData[0].name,
              imageUrl: productsData[0].gallery[0].imageUrl,
              category: productsData[0].category[0].productCategoryName,
            },
          }),
        );
        setProducts(productsData);
      } else {
        const newFilter = productsData.filter((item: any) => {
          return String(item.category[0].productCategoryId) === categoryId;
        });
        setProducts(newFilter);
      }
    }
    setIsLoading(false);
  };
  const onActionCategory = (categoryId: string) => {
    setTabValue(categoryId);
    if (categoryId === '0') {
      fetchDataProducts(filters);
    } else {
      fetchDataProducts(filters, categoryId);
    }
  };
  const fetchDataModelByProductId = async (productId: string) => {
    setIsLoadingModel(true);
    const res = await apiModel.getModelByProductId(productId);
    if (!res.isError) {
      const modelList = res.data.filter((item: any) => item.isActive);
      if (!isEmpty(modelList)) {
        if (!order.model?.id) {
          dispatch(
            setOrder({
              ...order,
              model: {
                id: modelList[0].id,
                name: modelList[0].name,
                imageUrl: modelList[0].imageUrl,
                modelCode: modelList[0].modelCode,
                modelSize: modelList[0].modelSize,
              },
              modelSize: {
                ...order.modelSize,
                modelId: modelList[0].id,
              },
            }),
          );
        }
      } else {
        dispatch(
          setOrder({
            product: {
              ...order.product,
            },
            sampleProduct: {
              ...order.sampleProduct,
            },
          }),
        );
      }
      setModels(modelList);
    }
    setIsLoadingModel(false);
  };
  const tabCategories = [
    { value: 0, label: 'ทั้งหมด' },
    ...(categories?.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || []),
  ];

  useEffect(() => {
    if (filters) {
      fetchDataProducts(filters);
    }
  }, [filters]);
  useEffect(() => {
    fetchDataProductCategory();
  }, []);
  useEffect(() => {
    if (order.product?.id) fetchDataModelByProductId(order.product.id);
    else setModels([]);
  }, [order.product?.id]);
  return (
    <FormSelectModelStyle>
      <ModalAppCustom
        open={open}
        handleClose={() => setOpen(false)}
        title={'เลือกสินค้า'}
      >
        <ProductsListsCard
          handleClose={() => setOpen(false)}
          isLoading={isLoading}
          products={products}
          setTabValue={(categoryId: string) => onActionCategory(categoryId)}
          tabValue={tabValue}
          tabCategories={tabCategories}
        />
      </ModalAppCustom>
      <SelectValueCard
        imageUrl={order.product?.imageUrl}
        name={order.product?.name}
        subName={order.product?.category}
        onOpenModal={() => {
          setOpen(true);
          // onActionCategory('0');
          // fetchDataProducts(filters);
        }}
      />
      <div className="model">
        {!isEmpty(models) && !isLoadingModel ? (
          <Swiper
            slidesPerView={2.4}
            spaceBetween={10}
            breakpoints={{
              425: {
                slidesPerView: 3.2,
                spaceBetween: 10,
              },
              575: {
                slidesPerView: 4.2,
                spaceBetween: 10,
              },
              768: {
                slidesPerView: 5.2,
                spaceBetween: 10,
              },
              992: {
                slidesPerView: 4,
                spaceBetween: 10,
              },
              1100: {
                slidesPerView: 3.6,
                spaceBetween: 10,
              },
            }}
            freeMode={true}
            modules={[FreeMode]}
            className="mySwiper"
          >
            {models?.map((item: any, index: number) => {
              return (
                <SwiperSlide key={index}>
                  <div
                    className={`card-select-model ${item.id === order.model?.id ? 'active' : ''}`}
                    onClick={() => {
                      if (!isEmpty(item.modelSize)) {
                        dispatch(
                          setOrder({
                            ...order,
                            model: {
                              id: item.id,
                              name: item.name,
                              imageUrl: item.imageUrl,
                              modelCode: item.modelCode,
                              modelSize: item.modelSize,
                            },
                            modelSize: {
                              ...order.modelSize,
                              modelId: item.id,
                            },
                          }),
                        );
                      } else {
                        dispatch(
                          setOrder({
                            ...order,
                            model: {
                              id: item.id,
                              name: item.name,
                              imageUrl: item.imageUrl,
                              modelCode: item.modelCode,
                              modelSize: item.modelSize,
                            },
                            modelSize: {},
                            coating: {},
                            printing: {},
                            material: {},
                            amountConfigCoating: {},
                          }),
                        );
                      }
                    }}
                  >
                    <div
                      className={'img-model'}
                      style={{ background: `url(${item.imageUrl})` }}
                    />
                    <div className={'name'}>{item.name}</div>
                  </div>
                </SwiperSlide>
              );
            })}
          </Swiper>
        ) : isEmpty(models) && isLoadingModel ? (
          <div className={'grid grid-cols-3 gap-2'}>{SkeletonModels()}</div>
        ) : (
          <EmptyDataMessage message={'ไม่พบข้อมูลโมลเดล'} height={'190px'} />
        )}
      </div>
    </FormSelectModelStyle>
  );
};
const SkeletonModels = () => {
  return [...Array(3)].map((_, index) => (
    <div key={index} className={'min-w-[180px] min-h-[180px]'}>
      <Skeleton className={'w-full h-full'} />
    </div>
  ));
};
export default FormSelectModel;
