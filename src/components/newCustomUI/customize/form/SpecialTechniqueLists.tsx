import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ButtonCustom from '@/components/common/ButtonCustom';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';
import apiSpecialTechnic from '@/services/specialTechnic';
import SpecialTechnicValue from '@/components/newCustomUI/customize/form/SpecialTechnicValue';

const SpecialTechniqueListsStyle = styled.div`
  .row-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1rem;
  }
  .btn-actions {
    width: 100%;
    margin-top: 2rem;
    button {
      width: 100%;
    }
  }
  @media (max-width: 768.98px) {
    .btn-actions {
      background: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      margin-top: 0;
      padding: 1rem;
      box-shadow: 0px 0px 25px 5px #0000001a;
    }
  }
  @media (max-width: 575.98px) {
    .row-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      padding-bottom: 6rem;
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
  handleClose: () => void;
  specialTechniques: any[];
};
const SpecialTechniqueLists = ({
  // formik,
  handleClose,
  specialTechniques,
}: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [dataSpecialTechniques, setDataSpecialTechniques] = useState<any>([]);
  const fetchGetSpecialTechnicById = async (data: any[]) => {
    const newData = await Promise.all(
      data.map(async (item: any) => {
        const res = await apiSpecialTechnic.getSpecialTechConfigBySpecialTechId(
          item.specialTechnic.id,
        );
        if (res?.status) {
          return res.data[0];
        }
        return null;
      }),
    );
    const validData = newData.filter((item) => item !== null);
    dispatch(
      setOrder({
        ...order,
        specialTechniques: validData,
      }),
    );
  };
  const onAddSpecialTechnic = async () => {
    handleClose();
    fetchGetSpecialTechnicById(dataSpecialTechniques);
  };
  useEffect(() => {
    if (!isEmpty(order.specialTechniques))
      setDataSpecialTechniques(order.specialTechniques);
  }, [order]);
  return (
    <SpecialTechniqueListsStyle>
      <div className={'row-list'}>
        {!isEmpty(specialTechniques) &&
          specialTechniques?.map((data, index) => {
            const isSelected = dataSpecialTechniques.some((item: any) => {
              return item.specialTechnic.id === data.id;
            });
            return (
              <SpecialTechnicValue
                key={index}
                isSelected={isSelected}
                setDataSpecialTechniques={setDataSpecialTechniques}
                id={data.id}
                imageUrl={data.imageUrl}
                name={data.name}
                description={data.description}
              />
            );
          })}
      </div>
      <div className={'btn-actions'}>
        <ButtonCustom
          btnType={'primary'}
          name={'เพิ่ม'}
          widthFull
          onAction={onAddSpecialTechnic}
        />
      </div>
    </SpecialTechniqueListsStyle>
  );
};

export default SpecialTechniqueLists;
