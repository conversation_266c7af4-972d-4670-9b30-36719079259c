import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import { numberWithCommas } from '@/utils/numberFormat';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useSelector } from 'react-redux';
import { useAppDispatch } from '@/store/index';

const ProductSamplesSelectCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;
const ProductSamplesSelectCard = () => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [isActive, setIsActive] = useState<number>(0);
  const productSamplesOptions = [
    {
      id: 1,
      imageUrl: '/images/image-product-sample-1.svg',
      name: 'Soft and Online Proof',
      description:
        'ส่งลิงก์ไฟล์ PDF และ คลิปVDO ตัวอย่างสินค้า ให้ตรวจสอบก่อนพิมพ์',
      price: 0,
    },
    {
      id: 2,
      imageUrl: '/images/image-product-sample-2.svg',
      name: 'Mockup Proof',
      description:
        'ส่งตัวอย่างสินค้า, ลิงก์ไฟล์ PDF และ คลิปVDO ให้ตรวจสอบก่อนพิมพ์ ระยะเวลาขึ้น Mockup 1 - 2 วัน (รวมค่าจัดส่ง)',
      price: 300,
    },
  ];
  useEffect(() => {
    setIsActive(productSamplesOptions[0].id);
  }, []);

  return (
    <ProductSamplesSelectCardStyle>
      {productSamplesOptions.map((item, index) => (
        <FormValueCard
          key={index}
          imageUrl={item.imageUrl}
          id={item.id}
          name={item.name}
          description={item.description}
          endValue={`฿${numberWithCommas(item.price, 2)}`}
          isActive={isActive === item.id}
          onAction={(id: number) => {
            setIsActive(id);
            dispatch(
              setOrder({
                ...order,
                sampleProduct: item,
                summaryPrice: {
                  ...order.summaryPrice,
                  examplePrice: item.price,
                },
              }),
            );
          }}
        />
      ))}
    </ProductSamplesSelectCardStyle>
  );
};

export default ProductSamplesSelectCard;
