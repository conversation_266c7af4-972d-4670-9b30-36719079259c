import { Info } from 'lucide-react';
import React from 'react';
import styled from 'styled-components';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

const SpecialTechnicValueStyle = styled.div`
  width: 100%;
  cursor: pointer;
  &:hover,
  &.active {
    .img {
      border-radius: 18px;
      overflow: hidden;
      border: 2px solid #0050ff;
    }
  }
  .img {
    flex: 1;
    background-size: cover !important;
    background-position: center !important;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 16px;
  }
  .detail {
    margin-top: 0.3rem;
    text-align: center;
    .name {
      font-size: 14px;
      font-weight: bold;
    }
    .tooltip-description {
      position: relative;
      .MuiButtonBase-root {
        position: absolute;
        right: 0;
        top: -7px;
      }
      .description {
        text-align: center;
        font-size: 12px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
`;
type Props = {
  id: number;
  isSelected: boolean;
  setDataSpecialTechniques: any;
  imageUrl: string;
  name: string;
  description: string;
};
const SpecialTechnicValue = ({
  id,
  isSelected,
  setDataSpecialTechniques,
  imageUrl,
  name,
  description,
}: Props) => {
  return (
    <SpecialTechnicValueStyle
      className={`${isSelected ? 'active' : ''}`}
      onClick={() => {
        setDataSpecialTechniques((prevState: any) => {
          if (isSelected) {
            return prevState.filter(
              (item: any) => item.specialTechnic.id !== id,
            );
          }
          return [...prevState, { specialTechnic: { id: id } }];
        });
      }}
    >
      <div className={'img'} style={{ background: `url(${imageUrl})` }}></div>
      <div className={'detail'}>
        <div className={'name'}>{name}</div>
        <div className={'tooltip-description'}>
          <div className={'description m-auto'}>{description}</div>
          {description.length > 14 && (
            <Tooltip title={description} placement="bottom-end">
              <IconButton>
                <Info size={'14px'} />
              </IconButton>
            </Tooltip>
          )}
        </div>
      </div>
    </SpecialTechnicValueStyle>
  );
};

export default SpecialTechnicValue;
