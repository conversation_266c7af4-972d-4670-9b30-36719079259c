import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';

const SelectValueCardStyle = styled.div`
  padding: 0.5rem;
  background: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &:hover {
    box-shadow: 0px 0px 15px 3px #0000000a;
  }
  &.active {
    border: 2px solid #0050ff;
  }
  .img-container {
    width: 60px;
    height: 60px;
    background-size: cover !important;
    background-position: center !important;
    background-color: #eeeeee !important;
    border-radius: 8px;
  }
  > div {
    &:first-child {
      display: flex;
      align-items: center;
      gap: 1rem;
      .name {
        font-size: 22px;
        font-weight: 600;
        &.font-16px {
          font-size: 16px;
        }
      }
      .sub-name {
        font-size: 12px;
      }
    }
    &:last-child {
      padding-right: 0.5rem;
    }
  }
  @media (max-width: 575.98px) {
    > div {
      &:first-child {
        gap: 0.5rem;
        img {
          width: 50px;
        }
        .name {
          font-size: 18px;
        }
      }
    }
  }
  @media (max-width: 425px) {
    > div {
      &:first-child {
        gap: 0.4rem;
        img {
          width: 50px;
        }
        .name {
          max-width: 180px;
          font-size: 16px;
          white-space: nowrap;
          overflow: hidden !important;
          text-overflow: ellipsis;
        }
      }
    }
  }
  @media (max-width: 375px) {
    > div {
      &:first-child {
        .name {
          max-width: 150px;
        }
      }
    }
  }
`;
type Props = {
  imageUrl?: string;
  name: string;
  sizeName?: string;
  subName: string;
  onOpenModal?: () => void;
  onSelectValue?: () => void;
  isActive?: boolean;
};
const SelectValueCard = ({
  imageUrl,
  name,
  subName,
  onOpenModal,
  sizeName,
  onSelectValue,
  isActive,
}: Props) => {
  return (
    <>
      <SelectValueCardStyle
        className={`${isActive ? 'active' : ''}`}
        {...(onOpenModal ? { onClick: onOpenModal } : {})}
        {...(onSelectValue ? { onClick: onSelectValue } : {})}
      >
        <div>
          {imageUrl ? (
            <div
              style={{ background: `url(${imageUrl})` }}
              className={'img-container'}
            />
          ) : (
            <Skeleton className={'w-[60px] h-[60px]'} />
          )}
          <div>
            <div className={`name ${sizeName || ''}`}>{name}</div>
            <div className={'sub-name'}>{subName}</div>
          </div>
        </div>
        {onOpenModal && (
          <div>
            <Image
              src={'/images/icon_grid.svg'}
              alt={'img product'}
              width={27}
              height={27}
            />
          </div>
        )}
      </SelectValueCardStyle>
    </>
  );
};

export default SelectValueCard;
