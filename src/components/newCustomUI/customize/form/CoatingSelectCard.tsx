import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import CollapsedSpring from '@/components/common/CollapsedSpring';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { isEmpty } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useAppDispatch } from '@/store/index';

const CoatingSelectCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  @media (max-width: 1200px) {
    .row-card {
      grid-template-columns: 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 991.98px) {
    .row-card {
      grid-template-columns: 1fr 1fr;
      gap: 0.7rem;
    }
  }
`;
const CoatingSelectCard = () => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [selectedCoating, setSelectedCoating] = useState<number>(1);
  useEffect(() => {
    if (order?.coating?.id) {
      setSelectedCoating(2);
    }
  }, [order?.coating?.id]);
  useEffect(() => {
    if (
      selectedCoating === 2 &&
      !isEmpty(order.amountConfigCoating?.modelSizeConfigCoating)
    ) {
      if (!order?.coating?.id) {
        dispatch(
          setOrder({
            ...order,
            coating: order.amountConfigCoating?.modelSizeConfigCoating[0],
          }),
        );
      }
    } else {
      dispatch(
        setOrder({
          ...order,
          coating: {},
        }),
      );
    }
  }, [selectedCoating, order.amountConfigCoating?.modelSizeConfigCoating]);

  return (
    <CoatingSelectCardStyle>
      <div className={'row-card'}>
        {coatingOptions.map((item: any, index: number) => (
          <FormValueCard
            key={index}
            imageUrl={item.imageUrl}
            id={item.id}
            name={item.name}
            isActive={selectedCoating === item.id}
            onSelectValue={() => {
              setSelectedCoating(item.id);
            }}
          />
        ))}
      </div>

      <div className={'box-display-upload-artwork'}>
        <CollapsedSpring isToggled={selectedCoating === 2}>
          <>
            <div className={'label-field'}>การเคลือบ</div>
            {!isEmpty(order.amountConfigCoating?.modelSizeConfigCoating) ? (
              <div className={'row-card'}>
                {order.amountConfigCoating?.modelSizeConfigCoating.map(
                  (item: any, index: number) => {
                    return (
                      <FormValueCard
                        key={index}
                        imageUrl={item.coating.imageUrl}
                        id={item.id}
                        name={item.coating.name}
                        isActive={item.id === order.coating?.id}
                        onSelectValue={() =>
                          dispatch(
                            setOrder({
                              ...order,
                              coating: item,
                            }),
                          )
                        }
                      />
                    );
                  },
                )}
              </div>
            ) : (
              <EmptyDataMessage
                message={'ไม่พบข้อมูล กรุณาเลือกข้อมูลจำนวนก่อน'}
                height={'100px'}
              />
            )}
          </>
        </CollapsedSpring>
      </div>
    </CoatingSelectCardStyle>
  );
};
const coatingOptions = [
  {
    id: 1,
    imageUrl: '/images/image_non_coating.svg',
    name: 'ไม่เคลือบ',
    description: 'บรรจุภัณฑ์ไมมีการเคลือบ',
  },
  {
    id: 2,
    imageUrl: '/images/image_coating.svg',
    name: 'เคลือบ',
    description: 'บรรจุภัณฑ์เคลือบด้านนอกเท่านั้น',
  },
];
export default CoatingSelectCard;
