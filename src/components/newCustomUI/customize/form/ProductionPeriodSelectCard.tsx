import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import PeriodCard from '@/components/newCustomUI/customize/form/PeriodCard';
import apiProductPeriod from '@/services/product-period';
import { isEmpty } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';
import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import { calculateCompletionDate } from '@/utils/calculator';

dayjs.extend(buddhistEra);
dayjs.locale('th');

const ProductionPeriodSelectCardStyle = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  @media (max-width: 1300px) {
    gap: 0.7rem;
  }
  @media (max-width: 575.98px) {
    gap: 0.5rem;
  }
  @media (max-width: 425px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const ProductionPeriodSelectCard = () => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const periodDetail = (id: number, date?: string, maxPeriod?: number) => {
    const completionTimestamp = maxPeriod
      ? calculateCompletionDate(maxPeriod)
      : null;
    const periodDateThaiFormat = dayjs(completionTimestamp).format('D MMM');
    const completionDate = completionTimestamp ? periodDateThaiFormat : '';

    switch (id) {
      case 1:
        return {
          imageUrl: '/icons/icon_period_1.svg',
          color: '#8BC34A',
          bgColor: '#E6F8CF',
          description: `ใช้เวลาผลิต ${date} วันทำการ`,
          completionDate: completionDate,
        };
      case 2:
        return {
          imageUrl: '/icons/icon_period_2.svg',
          color: '#F9A925',
          bgColor: '#FFF5D3',
          description: `เร่งผลิตใช้เวลาเพียง ${date} วันทำการ`,
          completionDate: completionDate,
        };
      case 3:
        return {
          imageUrl: '/icons/icon_period_3.svg',
          color: '#D32F2F',
          bgColor: '#FDE8EF',
          description: `ใช้เวลาผลิตเพียง ${date} วันทำการ`,
          completionDate: completionDate,
        };
      default:
        return {
          imageUrl: '/icons/icon_period_3.svg',
          color: '#D32F2F',
          bgColor: '#FDE8EF',
          description: `ใช้เวลาผลิตเพียง ${date} วันทำการ`,
          completionDate: completionDate,
        };
    }
  };
  const [periodOption, setPeriodOption] = useState<any[]>([]);
  const fetchGetAllProductPeriod = async () => {
    const res = await apiProductPeriod.getAllProductPeriod();
    if (!res.isError) {
      setPeriodOption(res.data);
    }
  };
  useEffect(() => {
    fetchGetAllProductPeriod();
  }, []);
  return !isEmpty(periodOption) ? (
    <ProductionPeriodSelectCardStyle>
      {periodOption.map((item, index) => {
        const periodInfo = periodDetail(
          item.id,
          `${item.minPeriod === item.maxPeriod ? item.maxPeriod : `${item.minPeriod} - ${item.maxPeriod}`}`,
          item.maxPeriod,
        );
        return (
          <PeriodCard
            key={index}
            isActive={order.period?.id === item.id}
            name={item.name}
            imageUrl={periodInfo.imageUrl}
            color={periodInfo.color}
            bgColor={periodInfo.bgColor}
            description={periodInfo.description}
            completionDate={periodInfo.completionDate}
            price={item.price}
            onSelectValue={() =>
              dispatch(
                setOrder({
                  ...order,
                  period: {
                    ...item,
                    completionDate: periodInfo.completionDate,
                  },
                  summaryPrice: {
                    ...order.summaryPrice,
                    periodPrice: item.price,
                  },
                }),
              )
            }
          />
        );
      })}
    </ProductionPeriodSelectCardStyle>
  ) : (
    <EmptyDataMessage message={'ไม่พบข้อมูล'} />
  );
};

export default ProductionPeriodSelectCard;
