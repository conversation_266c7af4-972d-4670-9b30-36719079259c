import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import TabCategory from '@/components/common/TabCategory';
import SelectValueCard from '@/components/newCustomUI/customize/form/SelectValueCard';
import { isEmpty } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';

const SizeListsCardStyle = styled.div``;
type Props = {
  data?: any[];
  category?: any[];
  onSelectValue?: (value: any) => void;
  idActive?: number;
  onSetCategoryId: (id: number) => void;
};
const SizeListsCard = ({
  data,
  onSelectValue,
  idActive,
  category,
  onSetCategoryId,
}: Props) => {
  const [tabValue, setTabValue] = useState('0');
  const tabCategories = [
    { value: 0, label: 'ทั้งหมด' },
    ...(category?.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || []),
  ];
  useEffect(() => {
    onSetCategoryId(Number(tabValue));
  }, [category, tabValue]);
  return (
    <SizeListsCardStyle>
      <TabCategory
        tabs={tabCategories}
        tabValue={tabValue}
        setTabValue={(value) => setTabValue(value)}
        layoutIdKey={'itemSizeCategory'}
      />
      {!isEmpty(data) ? (
        <div className={'row-card'}>
          {data?.map((item, index) => {
            return (
              <SelectValueCard
                key={index}
                isActive={item.id === idActive}
                imageUrl={'/images/mock_size.png'}
                name={`${item.width} x ${item.height} x ${item.length} mm`}
                sizeName={'font-16px'}
                subName={`ขนาดกางออก ${item.unfoldedSizeName}`}
                onSelectValue={() => {
                  if (onSelectValue) onSelectValue(item);
                }}
              />
            );
          })}
        </div>
      ) : (
        <EmptyDataMessage
          message={'ไม่พบข้อมูล'}
          isToggled={isEmpty(data)}
          height={'80px'}
        />
      )}
    </SizeListsCardStyle>
  );
};

export default SizeListsCard;
