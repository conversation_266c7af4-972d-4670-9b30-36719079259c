import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import AmountChip from '@/components/newCustomUI/customize/form/AmountChip';
import apiModel from '@/services/model';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { isEmpty } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useAppDispatch } from '@/store/index';

const AmountSelectCardStyle = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 10px;
  max-width: 100%;
  @media (max-width: 1440px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
  @media (max-width: 991.98px) {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  }
  @media (max-width: 767.98px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;
type Props = {
  printingId?: number;
};
const AmountSelectCard = ({ printingId }: Props) => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [amountOptions, setAmountOptions] = useState<any[]>([]);
  const fetchGetPrintingConfigById = async (id: number) => {
    const res = await apiModel.getPrintingConfigById(id);
    if (!res.isError) {
      setAmountOptions(res.data.modelSizeConfigDetail);
      dispatch(
        setOrder({
          ...order,
          amountConfigCoating: res.data.modelSizeConfigDetail[0],
          summaryPrice: {
            ...order.summaryPrice,
            amountPrice:
              res.data.modelSizeConfigDetail[0].price *
              res.data.modelSizeConfigDetail[0].amount,
          },
        }),
      );
    }
  };
  useEffect(() => {
    if (order?.printing?.id) {
      fetchGetPrintingConfigById(order?.printing?.id);
    } else {
      setAmountOptions([]);
    }
  }, [order?.printing?.id]);
  useEffect(() => {
    // 13
    if (printingId) fetchGetPrintingConfigById(printingId);
  }, [printingId]);
  return !isEmpty(amountOptions) ? (
    <AmountSelectCardStyle>
      {amountOptions.map((item, index) => (
        <AmountChip
          key={index}
          amount={item.amount}
          price={item.price}
          isActive={order?.amountConfigCoating?.id === item.id}
          onSelectValue={() =>
            dispatch(
              setOrder({
                ...order,
                amountConfigCoating: item,
                summaryPrice: {
                  ...order.summaryPrice,
                  amountPrice: item.price * item.amount,
                },
              }),
            )
          }
        />
      ))}
    </AmountSelectCardStyle>
  ) : (
    <EmptyDataMessage
      message={'ไม่พบข้อมูล กรุณาเลือกข้อมูลการพิมพ์ก่อน'}
      height={'100px'}
    />
  );
};

export default AmountSelectCard;
