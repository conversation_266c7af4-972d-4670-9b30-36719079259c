import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import CollapsedSpring from '@/components/common/CollapsedSpring';
import ButtonCustom from '@/components/common/ButtonCustom';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import { AnimatePresence, motion } from 'motion/react';
import TextField from '@mui/material/TextField';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import apiThaiAddress from '@/services/thaiAddress';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { useAppDispatch } from '@/store/index';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useSelector } from 'react-redux';

const HomeDeliverySelectCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  .box-display-zipcode {
    .box-zipcode {
      border-radius: 12px;
      padding: 0.8rem;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .detail {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 14px;
        .price {
          font-weight: 700;
          color: #0050ff;
        }
      }
      .box-field-zipcode {
        .MuiFormControl-root {
          max-width: 150px;
          > .MuiInputBase-root {
            border-radius: 6px;
            &.Mui-focused {
              fieldset {
                border-color: #0050ff !important;
              }
            }
          }
        }
        > div {
          button {
            color: #0050ff;
          }
        }
      }
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
};
const HomeDeliverySelectCard = ({ formik }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [dataZipcode, setDataZipcode] = useState<any>();
  const [value, setValue] = useState<string>('');
  const [openField, setOpenField] = useState<boolean>(false);
  const [isActive, setIsActive] = useState<number>(1);

  const handleZipChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (/^\d*$/.test(value) || value === '') {
      setValue(event.target.value);
      formik.setFieldValue('zipcode', event.target.value);
    }
  };

  const handleZipKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      !/[0-9]/.test(event.key) &&
      !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(
        event.key,
      )
    ) {
      event.preventDefault();
    }
  };

  const homeDeliveryOptions = [
    {
      id: 1,
      name: 'รับสินค้าด้วยตัวเอง',
      description: 'คุณต้องการมารับสินค้าด้วยตัวเองที่โรงงานของเรา',
      link: {
        name: 'ดูแผนที่ใน Google Map',
        path: '/',
      },
    },
    {
      id: 2,
      name: 'ส่งสินค้าถึงบ้าน',
      description:
        'จัดส่งสินค้ากรุงเทพฯ และปริมณฑลฟรี! หากจัดส่งนอกเขตค่าจัดส่งขึ้นอยู่กับระยะทางที่อยู่จัดส่ง',
    },
  ];
  const fetchGetZipcode = async (zipcode: number) => {
    const res = await apiThaiAddress.getZipcode(zipcode);
    if (!res.isError) {
      setDataZipcode(res.data);
      dispatch(
        setOrder({
          ...order,
          zipcodeData: {
            zipcode: zipcode,
            value: `${res.data.zipcode}, ${res.data.districtId.name} ${res.data.provinceId.name}`,
          },
        }),
      );
    } else {
      dispatch(
        setOrder({
          ...order,
          zipcodeData: {
            errorMessage: res.error,
          },
        }),
      );
      formik.errors.zipcode = res.error;
    }
  };
  useEffect(() => {
    if (value.length === 5) {
      fetchGetZipcode(Number(value));
    } else {
      setDataZipcode({});
      // dispatch(
      //   setOrder({
      //     ...order,
      //     zipcodeData: {},
      //   }),
      // );
    }
  }, [value]);
  useEffect(() => {
    if (isActive === 1) {
      formik.setFieldValue('zipcode', '');
      setValue('');
      setDataZipcode({});
      formik.setFieldError('zipcode', undefined);
      formik.setFieldTouched('zipcode', false);
      // dispatch(
      //   setOrder({
      //     ...order,
      //     zipcodeData: {
      //       ...order.zipcodeData,
      //       errorMessage: '',
      //     },
      //   }),
      // );
    }
  }, [isActive, formik.values.zipcode]);
  return (
    <HomeDeliverySelectCardStyle>
      <div className={'row-card'}>
        {homeDeliveryOptions.map((item, index) => (
          <FormValueCard
            key={index}
            id={item.id}
            name={item.name}
            description={item.description}
            isActive={isActive === item.id}
            height={'85px'}
            onAction={(id: number) => {
              setIsActive(id);
              dispatch(
                setOrder({
                  ...order,
                  zipcodeData: {
                    ...order.zipcodeData,
                    id: item.id,
                  },
                }),
              );
            }}
            link={item.link}
          />
        ))}
      </div>
      <CollapsedSpring isToggled={isActive === 2}>
        <div className={'box-display-zipcode'}>
          <div className={'label-field'}>สถานที่จัดส่ง</div>
          <div className={'box-zipcode'}>
            <div className={'detail'}>
              <Image
                src={'/icons/icon_distance.svg'}
                alt={'icon'}
                width={25}
                height={25}
              />
              <div>
                {dataZipcode?.zipcode && `${dataZipcode?.zipcode},`}{' '}
                {dataZipcode?.districtId?.name} {dataZipcode?.provinceId?.name}
              </div>
              <div className={'price'}>฿{numberWithCommas(0, 2)}</div>
            </div>
            <AnimatePresence mode="wait">
              <motion.div
                key={openField ? 'show' : 'empty'}
                initial={{ x: 10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -10, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                {!openField ? (
                  <ButtonCustom
                    name={'เปลี่ยนรหัสไปรษณีย์'}
                    btnType={'secondary'}
                    classList={'rounded-full'}
                    onAction={() => setOpenField(true)}
                  />
                ) : (
                  <div>
                    {' '}
                    <div
                      className={'box-field-zipcode flex items-center gap-1'}
                    >
                      <TextField
                        autoComplete={'off'}
                        fullWidth
                        size={'small'}
                        id={'zipcode'}
                        name={'zipcode'}
                        onChange={handleZipChange}
                        onKeyDown={handleZipKeyPress}
                        value={value}
                        inputProps={{
                          inputMode: 'numeric',
                          pattern: '[0-9]*',
                          maxLength: 5,
                        }}
                        // error={formik.touched.zip && Boolean(formik.errors.zip)}
                      />
                      <ButtonCustom
                        btnType={'text'}
                        name={'ยืนยัน'}
                        onAction={() => setOpenField(false)}
                      />
                      <ButtonCustom
                        btnType={'text'}
                        startIcon={
                          <CloseRoundedIcon style={{ color: '#e0e0e0' }} />
                        }
                        onAction={() => setOpenField(false)}
                      />
                    </div>
                    {/* {formik.touched.zip && Boolean(formik.errors.zip) && ( */}
                    {/*  <FormHelperText error> */}
                    {/*    {formik.touched.zip && */}
                    {/*      (formik.errors.zip as React.ReactNode)} */}
                    {/*  </FormHelperText> */}
                    {/* )} */}
                  </div>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </CollapsedSpring>
    </HomeDeliverySelectCardStyle>
  );
};

export default HomeDeliverySelectCard;
