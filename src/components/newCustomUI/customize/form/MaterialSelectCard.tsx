import React, { useEffect } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import HelpIcon from '@mui/icons-material/Help';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useAppDispatch } from '@/store/index';
import apiCustomize from '@/services/customize';

const SelectCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  .box-help {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem 0.5rem;
    width: 100%;
    border-radius: 6px;
    background: #f5f5f5;
    font-size: 12px;
    .more {
      cursor: pointer;
      color: #0055ff;
    }
  }
  @media (max-width: 1200px) {
    .row-card {
      grid-template-columns: 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 991.98px) {
    gap: 0.6rem;
    .box-help {
      border-radius: 8px;
      padding: 0.3rem 0.5rem;
    }
    .row-card {
      grid-template-columns: 1fr 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 425px) {
    .box-help {
      font-size: 10px;
    }
  }
`;
const MaterialSelectCard = () => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [materials, setMaterials] = React.useState<any[]>([]);
  const fetchFindModelSizeConfigMaterialsByModelSizeId = async (
    modelSizeId: number,
  ) => {
    const res =
      await apiCustomize.findModelSizeConfigMaterialsByModelSizeId(modelSizeId);
    if (!res.isError) {
      setMaterials(res.data);
      dispatch(
        setOrder({
          ...order,
          material: res.data[0],
        }),
      );
    }
  };
  useEffect(() => {
    if (!isUndefined(order.modelSize?.id) && !isNull(order.modelSize?.id)) {
      fetchFindModelSizeConfigMaterialsByModelSizeId(order.modelSize?.id);
    } else {
      setMaterials([]);
      dispatch(
        setOrder({
          ...order,
          material: {},
        }),
      );
    }
  }, [order.modelSize?.id]);
  return (
    <SelectCardStyle>
      {!isEmpty(materials) ? (
        <div className={'row-card'}>
          {materials?.map((item, index) => {
            return (
              <FormValueCard
                key={index}
                isActive={item.id === order.material?.id}
                imageUrl={'/images/mock-material.svg'}
                name={item.materialConfig.materialsName}
                description={`${item.materialConfig.gsm} gsm | ${item.materialConfig.mm} mm`}
                onSelectValue={() =>
                  dispatch(
                    setOrder({
                      ...order,
                      material: item,
                    }),
                  )
                }
              />
            );
          })}
        </div>
      ) : (
        <EmptyDataMessage message={'ไม่พบข้อมูลวัสดุ'} height={'100px'} />
      )}
      <div className={'box-help'}>
        <HelpIcon sx={{ fontSize: '15px' }} />
        <div>วิธีเลือกวัสดุให้เหมาะกับการใช้งาน</div>
        <div className={'more'}>เรียนรู้เพิ่มเติม</div>
      </div>
    </SelectCardStyle>
  );
};

export default MaterialSelectCard;
