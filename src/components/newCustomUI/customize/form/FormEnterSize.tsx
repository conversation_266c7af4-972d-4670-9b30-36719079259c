import React, { useEffect } from 'react';
import InputSize from '@/components/newCustomUI/customize/form/InputSize';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';
import { useSelector } from 'react-redux';
import { DataSizeType } from '@/types/customize';
import apiCustomize from '@/services/customize';
import { useDebounce } from '@uidotdev/usehooks';
import { toast } from 'sonner';

type Props = {
  formik: FormikProps<DataFormDetailType>;
};
const FormEnterSize = ({ formik }: Props) => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const fetchFindModelSizeId = async (dataSize: DataSizeType) => {
    const res = await apiCustomize.findModelSizeId(dataSize);
    if (!res.isError) {
      dispatch(
        setOrder({
          ...order,
          modelSize: {
            ...order.modelSize,
            id: res.data.modelSizeId,
            width: Number(dataSize?.width) || '',
            height: Number(dataSize?.height) || '',
            length: Number(dataSize?.length) || '',
            modelId: res.data.modelId,
            unfoldedSizeId: res.data.unfoldedSizeId,
          },
        }),
      );
    } else {
      toast.error(res.error);
      dispatch(
        setOrder({
          ...order,
          modelSize: {
            ...order.modelSize,
            id: null,
            width: Number(dataSize?.width) || '',
            height: Number(dataSize?.height) || '',
            length: Number(dataSize?.length) || '',
          },
          material: {},
        }),
      );
    }
  };
  const debouncedValueSize = useDebounce(formik.values.size, 1000);
  useEffect(() => {
    if (debouncedValueSize) {
      dispatch(
        setOrder({
          ...order,
          modelSize: {
            ...order.modelSize,
            width: Number(debouncedValueSize?.w) || '',
            height: Number(debouncedValueSize?.h) || '',
            length: Number(debouncedValueSize?.l) || '',
          },
        }),
      );
      if (
        Number(debouncedValueSize?.w) >= 20 &&
        Number(debouncedValueSize?.h) >= 20 &&
        Number(debouncedValueSize?.l) >= 20
      ) {
        fetchFindModelSizeId({
          width: Number(debouncedValueSize?.w),
          height: Number(debouncedValueSize?.h),
          length: Number(debouncedValueSize?.l),
          unfoldedWidth: order.modelSize.unfoldedWidth,
          unfoldedHeight: order.modelSize.unfoldedHeight,
          // unfoldedWidth: 146.5,
          // unfoldedHeight: 202.4,
          modelId: order.model.id,
        });
      }
    }
  }, [debouncedValueSize]);
  // useEffect(() => {
  //   formik.setFieldValue('size', { w: '', h: '', l: '' });
  //   dispatch(
  //     setOrder({
  //       ...order,
  //       modelSize: {},
  //       material: {},
  //       printing: {},
  //       coating: {},
  //       amountConfigCoating: {},
  //     }),
  //   );
  // }, []);
  return (
    <div className={'row-size-field'}>
      <InputSize
        formik={formik}
        name={'w'}
        description={'Width: Min 20 mm '}
        value={formik.values.size?.w}
        error={formik.touched.size?.w && Boolean(formik.errors.size?.w)}
        helperText={
          formik.touched.size?.w && (formik.errors.size?.w as React.ReactNode)
        }
      />
      <InputSize
        formik={formik}
        name={'l'}
        description={'Length: Min 20 mm '}
        value={formik.values.size?.l}
        error={formik.touched.size?.l && Boolean(formik.errors.size?.l)}
        helperText={
          formik.touched.size?.l && (formik.errors.size?.l as React.ReactNode)
        }
      />
      <InputSize
        formik={formik}
        name={'h'}
        description={'Height: Min 20 mm '}
        value={formik.values.size?.h}
        error={formik.touched.size?.h && Boolean(formik.errors.size?.h)}
        helperText={
          formik.touched.size?.h && (formik.errors.size?.h as React.ReactNode)
        }
      />
    </div>
  );
};

export default FormEnterSize;
