import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import { numberWithCommas } from '@/utils/numberFormat';
import ButtonCustom from '@/components/common/ButtonCustom';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';

const SpecialTechniqueValueStyle = styled.div`
  padding: 0.7rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &:hover {
    background: #eef3ff;
  }
  .name {
    font-size: 14px;
    font-weight: bold;
  }
  .description {
    font-size: 10px;
  }
  .box-field-select {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    .MuiInputBase-root {
      .MuiSelect-select {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 2rem 0 0.7rem;
        font-size: 14px;
      }
      fieldset {
        border-radius: 12px;
        border-color: #eeeeee;
      }
    }
    .field-price {
      display: flex;
      align-items: center;
      justify-content: end;
      height: 40px;
      min-width: 100px;
      text-align: right;
      border-radius: 12px;
      padding: 0.7rem;
      background: #eef3ff;
      color: #0050ff;
    }
  }
  @media (max-width: 575.98px) {
    flex-direction: column;
    align-items: start;
    gap: 0.7rem;
    .box-field-select {
      width: 100%;
      display: grid;
      grid-template-columns: 2fr 2fr 0.5fr;
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
  id: number;
  imageUrl: string;
  name: string;
  description: string;
};
const SpecialTechniqueValue = ({
  id,
  // formik,
  imageUrl,
  name,
  description,
}: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [value, setValue] = useState('10');

  const handleChange = (event: SelectChangeEvent) => {
    setValue(event.target.value as string);
  };
  return (
    <SpecialTechniqueValueStyle>
      <div className={'flex items-center gap-2'}>
        <Image src={imageUrl} alt={'img'} width={35} height={35} />
        <div>
          <div className={'name'}>{name}</div>
          <div className={'description'}>{description}</div>
        </div>
      </div>
      <div className={'box-field-select'}>
        <Select value={value} onChange={handleChange}>
          <MenuItem value={10}>พื้นที่ 10%</MenuItem>
          <MenuItem value={20}>พื้นที่ 20%</MenuItem>
          <MenuItem value={30}>พื้นที่ 30%</MenuItem>
        </Select>
        <div className={'field-price'}>{numberWithCommas(0, 2)}</div>
        <ButtonCustom
          btnType={'secondary'}
          startIcon={
            <Image
              src={'/icons/icon-delete-red.svg'}
              alt={'delete'}
              width={25}
              height={25}
            />
          }
          onAction={() => {
            const removeItem =
              !isEmpty(order.specialTechniques) &&
              order.specialTechniques.filter((item: any) => item.id !== id);
            dispatch(
              setOrder({
                ...order,
                specialTechniques: removeItem,
              }),
            );
            // formik.setFieldValue('specialTechniquesOptions', removeItem);
          }}
        />
      </div>
    </SpecialTechniqueValueStyle>
  );
};

export default SpecialTechniqueValue;
