import ButtonCustom from '@/components/common/ButtonCustom';
import React from 'react';
import styled from 'styled-components';
import { numberWithCommas } from '@/utils/numberFormat';

const AmountChipStyle = styled.div`
  //width: calc(100% / 6);
  width: 100%;
  padding: 1rem;
  text-align: center;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  height: 100px;
  border-radius: 8px;
  &.active,
  &:hover {
    border: 1px solid #0050ff;
    background: #eef3ff;
    .amount {
      color: #0050ff;
    }
    button {
      border: 1px solid #0050ff !important;
      color: #fff;
      background: #0050ff;
    }
  }

  .amount {
    font-size: 14px;
  }

  @media (min-width: 576px) {
    .amount {
      br {
        display: none;
      }
    }
  }

  @media (max-width: 1100px) {
    padding: 0.5rem;
  }
  @media (max-width: 991.98px) {
  }
  @media (max-width: 767.98px) {
  }
  @media (max-width: 575.98px) {
    gap: 0.2rem;
  }
  @media (max-width: 425px) {
    .amount {
      font-size: 12px;
    }
  }
`;
type Props = {
  amount: number;
  price: number;
  isActive: boolean;
  onSelectValue: () => void;
};
const AmountChip = ({ amount, price, isActive, onSelectValue }: Props) => {
  return (
    <AmountChipStyle
      className={`${isActive ? 'active' : ''}`}
      {...(onSelectValue ? { onClick: onSelectValue } : {})}
    >
      <div className={'amount'}>
        {numberWithCommas(amount, 0)} <br /> ชิ้น
      </div>
      <ButtonCustom
        btnType={'tertiary'}
        classList={'font-bold'}
        name={`฿${numberWithCommas(price, 2)}`}
      />
    </AmountChipStyle>
  );
};

export default AmountChip;
