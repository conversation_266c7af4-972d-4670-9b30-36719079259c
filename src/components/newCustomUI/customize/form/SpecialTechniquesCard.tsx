import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import CollapsedSpring from '@/components/common/CollapsedSpring';
import ButtonCustom from '@/components/common/ButtonCustom';
import AddIcon from '@mui/icons-material/Add';
import ModalAppCustom from '@/components/common/ModalAppCustom';
import SpecialTechniqueLists from '@/components/newCustomUI/customize/form/SpecialTechniqueLists';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { isEmpty } from 'lodash';
import SpecialTechniqueValue from '@/components/newCustomUI/customize/form/SpecialTechniqueValue';
import { AnimatePresence, motion } from 'motion/react';
import apiSpecialTechnic from '@/services/specialTechnic';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useAppDispatch } from '@/store/index';
import { calculateSpecialTechniquesTotal } from '@/utils/calculator';

const SpecialTechniquesCardStyle = styled.div`
  .box-has-special-techniques {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    .empty-list {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
      width: 100%;
      background: #fafafa;
      color: #bdbdbd;
      height: 6rem;
      font-size: 14px;
    }
    .box-div-card {
      border-top: 1px solid #eeeeee;
      &:last-child {
        border-bottom: 1px solid #eeeeee !important;
      }
    }
  }
  @media (max-width: 1200px) {
    .row-card {
      grid-template-columns: 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 991.98px) {
    .row-card {
      grid-template-columns: 1fr 1fr;
      gap: 0.7rem;
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
};
const SpecialTechniquesCard = ({ formik }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [isActive, setIsActive] = useState<number>(1);
  const [open, setOpen] = useState<boolean>(false);
  const specialTechniquesOptions = [
    {
      id: 1,
      imageUrl: '/images/image_non_special_techniques.svg',
      name: 'ไม่มี',
      description: 'บรรจุภัณฑ์ไมมีเทคนิคพิเศษ',
    },
    {
      id: 2,
      imageUrl: '/images/image_special_techniques.svg',
      name: 'มีเทคนิคพิเศษ',
      description: 'สร้างความรู้สึกให้กับบรรจุภัณฑ์ของคุณ',
    },
  ];
  const [specialTechniques, setSpecialTechniques] = useState<any[]>([]);
  const fetchDataSpecialTechnic = async (productId: number) => {
    const res = await apiSpecialTechnic.getDataSpecialTechnic(productId);
    if (res?.status) {
      const data = res.data.content.filter((item: any) => item.isActive);
      setSpecialTechniques(data);
    }
  };
  useEffect(() => {
    if (order?.product?.id) fetchDataSpecialTechnic(order?.product.id);
  }, [order?.product?.id]);

  useEffect(() => {
    if (!isEmpty(order.specialTechniques)) {
      dispatch(
        setOrder({
          ...order,
          summaryPrice: {
            ...order.summaryPrice,
            specialTechnicPrice: calculateSpecialTechniquesTotal(
              order.specialTechniques,
            ),
          },
        }),
      );
    } else {
      dispatch(
        setOrder({
          ...order,
          summaryPrice: {
            ...order.summaryPrice,
            specialTechnicPrice: 0,
          },
        }),
      );
    }
  }, [order.specialTechniques]);

  return (
    <SpecialTechniquesCardStyle>
      <div className={'row-card'}>
        {specialTechniquesOptions.map((item, index) => (
          <FormValueCard
            key={index}
            imageUrl={item.imageUrl}
            id={item.id}
            name={item.name}
            description={item.description}
            isActive={isActive === item.id}
            onAction={(id: number) => {
              if (id === 1) {
                dispatch(
                  setOrder({
                    ...order,
                    specialTechniques: [],
                  }),
                );
              }
              dispatch(
                setOrder({
                  ...order,
                  specialTechnicSelectConfig: item.id,
                }),
              );
              setIsActive(id);
            }}
          />
        ))}
      </div>
      <CollapsedSpring isToggled={isActive === 2}>
        <div className={'box-has-special-techniques'}>
          <div className={'w-full'}>
            {isEmpty(order.specialTechniques) ? (
              <EmptyDataMessage message={'ไม่มีรายการ'} />
            ) : (
              <AnimatePresence mode="popLayout">
                {order.specialTechniques?.map((item: any) => {
                  return (
                    <motion.div
                      key={
                        !isEmpty(order.specialTechniques)
                          ? `show-${item.id}`
                          : 'empty'
                      }
                      layout
                      initial={{ x: 10, opacity: 0, filter: 'blur(1px)' }}
                      animate={{ x: 0, opacity: 1, filter: 'blur(0px)' }}
                      exit={{ x: -10, opacity: 0, filter: 'blur(1px)' }}
                      transition={{ duration: 0.2 }}
                      className={'box-div-card'}
                    >
                      <SpecialTechniqueValue
                        formik={formik}
                        id={item.id}
                        specialTechnicId={item.specialTechnic.id}
                        imageUrl={item.specialTechnic.imageUrl}
                        name={item.specialTechnic.name}
                        description={item.specialTechnic?.description}
                        price={item.price}
                      />
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
          </div>
          {/* {formik.errors.specialTechniquesOptions && ( */}
          {/*  <FormHelperText error style={{ textAlign: 'center' }}> */}
          {/*    {formik.errors.specialTechniquesOptions} */}
          {/*  </FormHelperText> */}
          {/* )} */}
          <>
            <ButtonCustom
              type={'button'}
              btnType={'secondary'}
              name={'เพิ่มเทคนิคพิเศษ'}
              classList={'rounded-full'}
              startIcon={<AddIcon sx={{ color: '#0050FF' }} />}
              theme={'#0050FF'}
              onAction={() => setOpen(true)}
            />
            <ModalAppCustom
              open={open}
              handleClose={() => setOpen(false)}
              title={'เทคนิคพิเศษ'}
            >
              <SpecialTechniqueLists
                formik={formik}
                handleClose={() => setOpen(false)}
                specialTechniques={specialTechniques}
              />
            </ModalAppCustom>
          </>
        </div>
      </CollapsedSpring>
    </SpecialTechniquesCardStyle>
  );
};

export default SpecialTechniquesCard;
