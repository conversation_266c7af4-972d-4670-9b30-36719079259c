import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Link from 'next/link';

const FormValueCardStyle = styled.div`
  padding: 0.8rem 0.6rem;
  border: 1px solid #eeeeee;
  border-radius: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  flex: 1;
  cursor: pointer;
  height: 69px;
  .start-col {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  &.add-px {
    padding: 0.8rem 1.2rem;
  }
  &:hover,
  &.active {
    border: 2px solid #0050ff;
    background: #eef3ff;
    .end-col {
      font-weight: 700;
      color: #0050ff;
    }
  }
  img {
    border-radius: 6px;
  }
  .detail {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .name {
      font-size: 14px;
    }
    p {
      max-width: 320px;
      padding-right: 0.2rem;
      text-wrap: wrap;
      font-size: 12px;
      color: #9e9e9e;
    }
    a {
      width: fit-content;
      color: #0050ff;
      font-size: 12px;
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;
type Props = {
  imageUrl?: string;
  name: string;
  description?: string;
  height?: string;
  id?: number;
  isActive?: boolean;
  onAction?: (id: number) => void;
  endValue?: string;
  link?: {
    path: string;
    name: string;
  };
  onSelectValue?: () => void;
};
const FormValueCard = ({
  imageUrl,
  name,
  description,
  height,
  isActive,
  onAction,
  endValue,
  id,
  link,
  onSelectValue,
}: Props) => {
  return (
    <FormValueCardStyle
      className={`${!imageUrl ? 'add-px' : ''} ${isActive ? 'active' : ''}`}
      style={height ? { height: height } : {}}
      {...(onAction && id ? { onClick: () => onAction(id) } : {})}
      {...(onSelectValue ? { onClick: onSelectValue } : {})}
    >
      <div className={'start-col'}>
        {imageUrl && (
          <Image src={imageUrl} alt={'img material'} width={40} height={40} />
        )}
        <div className={'detail'}>
          <div className={'name'}>
            {name} <InfoOutlinedIcon sx={{ fontSize: '13px' }} />
          </div>
          {description && <p>{description}</p>}
          {link && <Link href={link.path}>{link.name}</Link>}
        </div>
      </div>
      {endValue && <div className={'end-col'}>{endValue}</div>}
    </FormValueCardStyle>
  );
};

export default FormValueCard;
