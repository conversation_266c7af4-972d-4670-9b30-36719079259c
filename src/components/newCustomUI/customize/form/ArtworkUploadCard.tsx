import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import ButtonCustom from '@/components/common/ButtonCustom';
import Image from 'next/image';
import { TextField } from '@mui/material';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';

const ArtworkUploadCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  .box-display-upload-artwork {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    .upload-file-artwork {
      padding: 1rem;
      border-radius: 16px;
      background: #fafafa;
      border: 1px dashed #e0e0e0;
      display: flex;
      align-items: center;
      gap: 1rem;
      p {
        font-size: 12px;
        color: #bdbdbd;
      }
    }
  }
  .box-dieline-template {
    p {
      font-size: 12px;
      margin-bottom: 0.3rem;
    }
    .upload-model {
      padding: 0.7rem;
      background: #f5f5f5;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .upload-model-input {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        .details {
          display: flex;
          flex-direction: column;
          .name {
            font-size: 14px;
            font-weight: 600;
          }
          .file-name {
            font-size: 12px;
          }
        }
      }
    }
  }
  @media (max-width: 1300px) {
    .row-card {
      gap: 0.7rem;
      .start-col {
        .detail {
          p {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
    .box-display-upload-artwork {
      .upload-file-artwork {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        p {
          text-align: center;
          br {
            display: none;
          }
        }
      }
    }
  }
  @media (max-width: 1200px) {
    .row-card {
      grid-template-columns: 1fr !important;
    }
  }
  @media (max-width: 991.98px) {
    .box-display-upload-artwork {
      .upload-file-artwork {
        display: flex;
        flex-direction: unset;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        p {
          text-align: center;
          br {
            display: unset;
          }
        }
      }
    }
  }
  @media (max-width: 575.98px) {
    .box-display-upload-artwork {
      .upload-file-artwork {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        p {
          text-align: center;
        }
      }
    }
  }
  @media (max-width: 425px) {
    .box-display-upload-artwork {
      .upload-file-artwork {
        padding: 0.7rem;
        p {
          br {
            display: none;
          }
        }
      }
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
};
const ArtworkUploadCard = ({ formik }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const dispatch = useAppDispatch();
  const [isActive, setIsActive] = useState<number>(0);
  const uploadOptions = [
    {
      id: 1,
      name: 'มีไฟล์อาร์ตเวิร์ก',
      description:
        'สินค้าของคุณได้ออกแบบและมีไฟล์อาร์ตเวิร์กสำหรับส่งพิมพ์แล้ว และจะต้องส่งไฟล์ให้ทีมงานหลังจากการชำระเงิน',
    },
    {
      id: 2,
      name: 'ไม่ใช้ไฟล์อาร์ตเวิร์ก',
      description:
        'สินค้าของคุณไม่มีการพิมพ์ใดๆ และใช้เพียงไฟล์ Dieline Template สำหรับการผลิต',
    },
    // {
    //   id: 1,
    //   name: 'อัปโหลดไฟล์ภายหลัง',
    //   description:
    //     'ไฟล์อาร์ตเวิร์กของคุณยังไม่พร้อมใช่ไหม? คุณต้องการ เพิ่มรายการก่อนแล้วคุณจะส่งให้ภายหลัง',
    // },
    // {
    //   id: 2,
    //   name: 'อัปโหลดไฟล์',
    //   description:
    //     'คุณมีไฟล์งานอาร์ตเวิร์ตที่เป็น “ไฟล์ .ai” พร้อมผลิต และพร้อมอัปโหลดตอนนี้',
    // },
  ];
  useEffect(() => {
    console.log('isActive: ', isActive);
    const artworkData = uploadOptions.find((item) => item.id === isActive);
    if (artworkData) {
      console.log('artworkData: ', artworkData);
      dispatch(setOrder({ ...order, artwork: artworkData }));
    }
  }, [isActive]);
  useEffect(() => {
    if (isActive === 1) {
      delete formik.errors.artwork;
    }
  }, [formik]);
  return (
    <ArtworkUploadCardStyle>
      <div className="row-card">
        {uploadOptions.map((item, index) => (
          <FormValueCard
            key={index}
            id={item.id}
            height={'84px'}
            name={item.name}
            description={item.description}
            isActive={isActive === item.id}
            onAction={(id: number) => setIsActive(id)}
          />
        ))}
      </div>
      {/* <CollapsedSpring isToggled={isActive === 2}> */}
      {/*  <div className={'box-display-upload-artwork'}> */}
      {/*    <div> */}
      {/*      <div className={'label-field'}> */}
      {/*        ไฟล์อาร์ตเวิร์ก{' '} */}
      {/*        <HelpRoundedIcon sx={{ fontSize: '14px', color: '#E0E0E0' }} /> */}
      {/*      </div> */}
      {/*      <div className={'upload-file-artwork'}> */}
      {/*        {isNull(formik.values.artwork.file) ? ( */}
      {/*          <> */}
      {/*            <ButtonCustom */}
      {/*              formik={formik} */}
      {/*              typeFileUpload */}
      {/*              name={'อัพโหลดไฟล์'} */}
      {/*              btnType={'secondary'} */}
      {/*              classList={'rounded-full'} */}
      {/*              startIcon={ */}
      {/*                <Image */}
      {/*                  src={'/icons/icon_upload_file.svg'} */}
      {/*                  alt={'icon upload'} */}
      {/*                  width={25} */}
      {/*                  height={25} */}
      {/*                /> */}
      {/*              } */}
      {/*            /> */}
      {/*            <p> */}
      {/*              File types supported: JPG, PNG, SVG, PSD, AI, PDF, TTF, ZIP,{' '} */}
      {/*              <br /> ABR, PAT and more. Max size 500MB. */}
      {/*            </p> */}
      {/*          </> */}
      {/*        ) : ( */}
      {/*          <> */}
      {/*            <ButtonCustom */}
      {/*              formik={formik} */}
      {/*              typeFileUpload */}
      {/*              name={ */}
      {/*                !isNull(formik.values.artwork.file) */}
      {/*                  ? 'เปลี่ยนไฟล์' */}
      {/*                  : 'อัพโหลดไฟล์' */}
      {/*              } */}
      {/*              btnType={'secondary'} */}
      {/*              classList={'rounded-full'} */}
      {/*              startIcon={ */}
      {/*                <Image */}
      {/*                  src={'/icons/icon_upload_file.svg'} */}
      {/*                  alt={'icon upload'} */}
      {/*                  width={25} */}
      {/*                  height={25} */}
      {/*                /> */}
      {/*              } */}
      {/*            /> */}
      {/*            <div className={'max-w-[400px] text-[14px] truncate'}> */}
      {/*              {formik.values.artwork.file?.name} */}
      {/*            </div> */}
      {/*          </> */}
      {/*        )} */}
      {/*      </div> */}
      {/*      {formik.touched.artwork?.file && */}
      {/*        Boolean(formik.errors.artwork?.file) && ( */}
      {/*          <FormHelperText error> */}
      {/*            {formik.errors.artwork?.file as React.ReactNode} */}
      {/*          </FormHelperText> */}
      {/*        )} */}
      {/*    </div> */}
      {/*    <div> */}
      {/*      <div className={'label-field'}>ลิงก์ไฟล์</div> */}
      {/*      <TextField */}
      {/*        size={'small'} */}
      {/*        className={'main-field'} */}
      {/*        placeholder={'https://'} */}
      {/*        id={'artwork.linkFile'} */}
      {/*        name={'artwork.linkFile'} */}
      {/*        hiddenLabel */}
      {/*        onChange={formik.handleChange} */}
      {/*        fullWidth */}
      {/*        error={ */}
      {/*          formik.touched.artwork?.linkFile && */}
      {/*          Boolean(formik.errors.artwork?.linkFile) */}
      {/*        } */}
      {/*      /> */}
      {/*      {formik.touched.artwork?.linkFile && */}
      {/*        Boolean(formik.errors.artwork?.linkFile) && ( */}
      {/*          <FormHelperText error> */}
      {/*            {formik.errors.artwork?.linkFile as React.ReactNode} */}
      {/*          </FormHelperText> */}
      {/*        )} */}
      {/*    </div> */}
      {/*  </div> */}
      {/* </CollapsedSpring> */}
      <div>
        <div className={'box-dieline-template'}>
          <p>Dieline Template</p>
          <div className={'upload-model'}>
            <div className={'upload-model-input'}>
              <Image
                src={'/images/mock-image-upload.svg'}
                alt={'upload template'}
                width={40}
                height={40}
              />
              <div className={'details'}>
                <div className={'name'}>Model: HPM-PK01-001A0</div>
                <div className={'file-name'}>PDF</div>
              </div>
            </div>
            <ButtonCustom
              name={'Download'}
              btnType={'primary'}
              startIcon={
                <Image
                  src={'/icons/icon_download.svg'}
                  alt={'icons download'}
                  width={25}
                  height={25}
                />
              }
            />
          </div>
        </div>
      </div>
      <div>
        <div className={'label-field'}>หมายเหตุ</div>
        <TextField
          className={'main-field'}
          placeholder={'พิมพ์ข้อความ...'}
          id={'artwork.note'}
          name={'artwork.note'}
          hiddenLabel
          fullWidth
          multiline
          rows={5}
          onChange={formik.handleChange}
        />
      </div>
    </ArtworkUploadCardStyle>
  );
};
export default ArtworkUploadCard;
