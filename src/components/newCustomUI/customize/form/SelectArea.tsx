import React, { useEffect, useState } from 'react';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { TypeDataAreaSize } from '@/store/type/specialTechnic';
import { useAppDispatch } from '@/store/index';
import styled from 'styled-components';

const SelectAreaStyle = styled.div`
  .MuiInputBase-root {
    &.disabled {
      pointer-events: none;
      svg {
        display: none;
      }
      .MuiSelect-select {
        padding: 0 1rem !important;
      }
    }
  }
`;
type Props = {
  id: number;
  menuItems: any[];
};
const SelectArea = ({ id, menuItems }: Props) => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [value, setValue] = useState<string>('0');
  const handleChange = (event: SelectChangeEvent) => {
    setValue(event.target.value as string);
  };
  const newSpecialTechniques = (item: TypeDataAreaSize) => {
    const newData = order.specialTechniques.map((data: any) => {
      if (data.specialTechnic.id === item.specialTechnic.id) {
        return {
          ...item,
        };
      }
      return data;
    });
    dispatch(setOrder({ ...order, specialTechniques: newData }));
  };

  useEffect(() => {
    setValue(String(id));
  }, [id]);

  return (
    <SelectAreaStyle>
      <Select
        value={value}
        onChange={handleChange}
        className={`${menuItems.length < 2 ? 'disabled' : ''}`}
      >
        {menuItems.map((item: any, i: number) => {
          return (
            <MenuItem
              value={item.id}
              key={i}
              onClick={() => {
                newSpecialTechniques(item);
              }}
            >
              พื้นที่ {item.areaSize.name}
            </MenuItem>
          );
        })}
      </Select>
    </SelectAreaStyle>
  );
};

export default SelectArea;
