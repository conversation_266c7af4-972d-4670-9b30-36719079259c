import React, { useEffect, useState } from 'react';
import FormValueCard from '@/components/newCustomUI/customize/form/FormValueCard';
import styled from 'styled-components';
import apiModel from '@/services/model';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { isEmpty, isUndefined } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useAppDispatch } from '@/store/index';

const PrintSelectCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  @media (max-width: 1200px) {
    .row-card {
      grid-template-columns: 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 991.98px) {
    .row-card {
      grid-template-columns: 1fr 1fr;
    }
  }
`;
const PrintSelectCard = () => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [printOptions, setPrintOptions] = useState<any[]>([]);
  const fetchGetPrintingByModelSizeConfigId = async (
    modelSizeConfigId: number,
  ) => {
    const res =
      await apiModel.getPrintingByModelSizeConfigId(modelSizeConfigId);
    if (!res.isError) {
      setPrintOptions(res.data);
      if (!isEmpty(res.data)) {
        if (!order?.printing?.id) {
          dispatch(
            setOrder({
              ...order,
              printing: res.data[0],
            }),
          );
        }
      } else {
        dispatch(
          setOrder({
            ...order,
            printing: {},
            coating: {},
            amountConfigCoating: {},
          }),
        );
      }
    }
  };
  useEffect(() => {
    if (!isUndefined(order.material?.id)) {
      fetchGetPrintingByModelSizeConfigId(order.material?.id);
    } else {
      setPrintOptions([]);
      dispatch(
        setOrder({
          ...order,
          printing: {},
          coating: {},
          amountConfigCoating: {},
        }),
      );
    }
  }, [order.material?.id]);
  return (
    <PrintSelectCardStyle>
      {!isEmpty(printOptions) ? (
        <div className={'row-card'}>
          {printOptions.map((item, index) => (
            <FormValueCard
              key={index}
              isActive={order.printing?.id === item.id}
              imageUrl={item.printing.imageUrl}
              name={item.printing.name}
              onSelectValue={() =>
                dispatch(
                  setOrder({
                    ...order,
                    printing: item,
                  }),
                )
              }
            />
          ))}
        </div>
      ) : (
        <EmptyDataMessage
          message={'ไม่พบรายการพิมพ์'}
          isToggled={isEmpty(printOptions)}
        />
      )}
    </PrintSelectCardStyle>
  );
};

export default PrintSelectCard;
