import React, { useEffect, useState } from 'react';
import SelectValueCard from '@/components/newCustomUI/customize/form/SelectValueCard';
import TabValue from '@/components/common/TabValue';
import { motion, AnimatePresence } from 'motion/react';
import ModalAppCustom from '@/components/common/ModalAppCustom';
import SizeListsCard from '@/components/newCustomUI/customize/form/SizeListsCard';
import styled from 'styled-components';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { isEmpty, isUndefined } from 'lodash';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useAppDispatch } from '@/store/index';
import apiModel from '@/services/model';
import FormEnterSize from '@/components/newCustomUI/customize/form/FormEnterSize';

const FormSelectSizeStyle = styled.div`
  .row-size-field {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
  }
  @media (max-width: 1200px) {
    .row-size-field {
      grid-template-columns: 1fr 1fr;
      gap: 0.7rem;
    }
  }
  @media (max-width: 991.98px) {
    .row-size-field {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
  @media (max-width: 575.98px) {
    .row-size-field {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
};
const FormSelectSize = ({ formik }: Props) => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState('0');
  const [categoryId, setCategoryId] = useState<number>(0);
  const [modelSizeStandard, setModelSizeStandard] = useState<any>([]);
  const [categoryUnfoldedSize, setCategoryUnfoldedSize] = useState<any>([]);
  const fetchGetModelSizeByModelId = async (
    modelId: number,
    categoryId: number,
  ) => {
    const res = await apiModel.getModelSizeByModelId(modelId);
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        if (categoryId === 0) {
          setModelSizeStandard(res.data);
          const { width, height, length } = res.data[0];
          dispatch(
            setOrder({
              ...order,
              modelSize: {
                ...res.data[0],
              },
            }),
          );
          formik.setFieldValue('size', {
            w: width,
            h: height,
            l: length,
          });
        } else {
          const filteredData = res.data.filter(
            (item: any) => item.unfoldedSizeId === categoryId,
          );
          setModelSizeStandard(filteredData);
        }
      }
    }
  };
  const fetchGetUnfoldedSizePage = async () => {
    const res = await apiModel.getUnfoldedSizePage();
    if (!res.isError) {
      setCategoryUnfoldedSize(
        res.data.content.filter((item: any) => item.isActive),
      );
    }
  };
  useEffect(() => {
    fetchGetUnfoldedSizePage();
  }, []);
  useEffect(() => {
    if (order.model?.id && tabValue === '0') {
      fetchGetModelSizeByModelId(order.model?.id, categoryId);
    } else {
      setModelSizeStandard([]);
    }
  }, [order.model?.id, tabValue, categoryId]);
  useEffect(() => {
    if (tabValue === '0') {
      delete formik.errors.size;
    }
  }, [formik]);

  return (
    <FormSelectSizeStyle>
      <TabValue
        tabs={[
          { value: 0, label: 'มาตรฐาน' },
          {
            value: 1,
            label: 'กำหนดเอง',
            disabled: isUndefined(order.model?.id),
          },
        ]}
        tabValue={tabValue}
        setTabValue={(value) => setTabValue(value)}
        layoutIdKey={'item'}
      />
      <AnimatePresence mode="wait">
        <motion.div
          key={tabValue || 'empty'}
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {tabValue === '0' && (
            <>
              <ModalAppCustom
                open={open}
                handleClose={() => setOpen(false)}
                title={'ขนาดมาตรฐาน'}
              >
                <SizeListsCard
                  idActive={order.modelSize?.id}
                  category={categoryUnfoldedSize}
                  data={modelSizeStandard}
                  // onFetchDataByCategory={(categoryId: number) => {}
                  onSetCategoryId={(id: number) => setCategoryId(id)}
                  onSelectValue={(value: any) => {
                    console.log(value);
                    const { width, height, length } = value;
                    formik.setFieldValue('size', {
                      w: width,
                      h: height,
                      l: length,
                    });
                    setOpen(false);
                    dispatch(
                      setOrder({
                        ...order,
                        modelSize: {
                          ...order.modelSize,
                          ...value,
                        },
                      }),
                    );
                  }}
                />
              </ModalAppCustom>
              {!isUndefined(order.modelSize?.id) ? (
                <SelectValueCard
                  name={`${order.modelSize?.width || 0} x ${order.modelSize?.length || 0} x ${order.modelSize?.height || 0} mm`}
                  subName={`ขนาดกางออก ${order.modelSize?.unfoldedSizeName}`}
                  onOpenModal={() => setOpen(true)}
                />
              ) : (
                <EmptyDataMessage
                  message={'ไม่พบรายการขนาดมาตราฐาน'}
                  isToggled={isEmpty(modelSizeStandard)}
                />
              )}
            </>
          )}
          {tabValue === '1' && <FormEnterSize formik={formik} />}
        </motion.div>
      </AnimatePresence>
    </FormSelectSizeStyle>
  );
};

export default FormSelectSize;
