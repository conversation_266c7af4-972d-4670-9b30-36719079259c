import React from 'react';
import styled from 'styled-components';
import TabCategory from '@/components/common/TabCategory';
import { InputAdornment, TextField } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { isEmpty } from 'lodash';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppDispatch } from '@/store/index';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { useSelector } from 'react-redux';
import NotFoundData from '@/components/common/NotFoundData';

const ProductsListsStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 480px;
  .content-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1rem;
    > div {
      cursor: pointer;
      .img-product {
        width: 100%;
        aspect-ratio: 1 / 1;
        background-color: #f5f5f5 !important;
        background-size: cover !important;
        background-position: center !important;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        //img {
        //  margin: auto;
        //  width: calc(100% - 75px) !important;
        //}
      }
      span {
        font-size: 12px;
      }
      .name {
        font-size: 16px;
        font-weight: bold;
        white-space: wrap;
      }
      &:hover,
      &.active {
        .img-product {
          background: #eef3ff;
          border: 2px solid #0050ff;
        }
      }
    }
  }
  @media (max-width: 768.98px) {
    .content-list {
      grid-template-columns: 1fr 1fr 1fr;
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 120px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 600px) {
    .content-list {
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 100px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 575.98px) {
    .content-list {
      grid-template-columns: 1fr 1fr;
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 140px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 475px) {
    .content-list {
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 120px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 425px) {
    .content-list {
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 110px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 375px) {
    .content-list {
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 90px) !important;
        //  }
        //}
      }
    }
  }
  @media (max-width: 320px) {
    .content-list {
      > div {
        //.img-product {
        //  img {
        //    width: calc(100% - 80px) !important;
        //  }
        //}
      }
    }
  }
`;
type Props = {
  handleClose: () => void;
  isLoading: boolean;
  tabValue: string;
  setTabValue: (value: string) => void;
  tabCategories: any;
  products?: any[];
};
const ProductsListsCard = ({
  handleClose,
  isLoading,
  tabCategories,
  setTabValue,
  tabValue,
  products,
}: Props) => {
  const dispatch = useAppDispatch();
  const { order } = useSelector(dataAddOrderSelector);
  return (
    <ProductsListsStyle>
      {!isEmpty(tabCategories) && (
        <div>
          <TabCategory
            tabs={tabCategories || []}
            tabValue={tabValue}
            setTabValue={(value) => setTabValue(value)}
            layoutIdKey={'itemCategory'}
          />
          <TextField
            autoComplete={'off'}
            size={'small'}
            className={'main-field search'}
            placeholder={'ค้นหา'}
            hiddenLabel
            fullWidth
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#BDBDBD' }} />
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>
      )}
      {!isEmpty(products) && !isLoading ? (
        <div className={'content-list'}>
          {products?.map((item: any, index: number) => {
            return (
              <div
                className={`${item.id === order.product.id ? 'active' : ''}`}
                key={index}
                onClick={() => {
                  handleClose();
                  dispatch(
                    setOrder({
                      ...order,
                      product: {
                        id: item.id,
                        name: item.name,
                        imageUrl: item.gallery[0].imageUrl,
                        category: item.category[0].productCategoryName,
                      },
                    }),
                  );
                }}
              >
                <div
                  className={`img-product `}
                  style={{ background: `url(${item.gallery[0].imageUrl})` }}
                >
                  {/* <Image */}
                  {/*  src={item.gallery[0].imageUrl} */}
                  {/*  alt={'img product'} */}
                  {/*  width={500} */}
                  {/*  height={122} */}
                  {/* /> */}
                </div>
                <span>{item.category[0].productCategoryName}</span>
                <div className={'name'}>{item.name}</div>
              </div>
            );
          })}
        </div>
      ) : isEmpty(products) && isLoading ? (
        <div className={'content-list'}>{SkeletonProduct()}</div>
      ) : (
        <NotFoundData message={'ไม่พบข้อมูลสินค้า'} heightSize={'300px'} />
      )}
    </ProductsListsStyle>
  );
};
const SkeletonProduct = () => {
  return [...Array(8)].map((_, index) => (
    <div key={index} className={'min-w-[130px] min-h-[130px]'}>
      <Skeleton className={'w-full h-full'} />
    </div>
  ));
};
export default ProductsListsCard;
