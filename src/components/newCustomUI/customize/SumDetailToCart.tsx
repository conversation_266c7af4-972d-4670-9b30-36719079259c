import ButtonCustom from '@/components/common/ButtonCustom';
import React from 'react';
import styled from 'styled-components';
import ShoppingCartOutlinedIcon from '@mui/icons-material/ShoppingCartOutlined';
import { numberWithCommas } from '@/utils/numberFormat';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector } from '@/store/reducers/orderSlice';

const SumDetailToCartStyle = styled.div`
  position: sticky;
  bottom: 30px;
  right: 0;
  width: 95%;
  margin: auto;
  padding: 1rem 2rem;
  border-radius: 24px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 0px 25px 5px #0000001a;
  z-index: 100;
  &.min-992-none {
    @media (min-width: 992px) {
      display: none;
    }
  }
  &.max-991-none {
    @media (max-width: 991.98px) {
      display: none;
    }
  }
  .price-detail {
    color: #fff;
    .price {
      font-weight: bold;
      font-size: 26px;
    }
    .detail {
      font-size: 14px;
    }
  }
  @media (max-width: 1200px) {
    padding: 1rem 1.5rem;
    border-radius: 18px;
    .price-detail {
      .price {
        font-size: 22px;
      }
      .detail {
        font-size: 12px;
      }
    }
    button {
      width: unset !important;
    }
  }
  @media (max-width: 991.98px) {
    border-radius: 0;
    position: fixed;
    width: 100%;
    margin: unset;
    bottom: 0;
  }
  @media (max-width: 575.98px) {
    padding: 1rem 0.7rem;
    .price-detail {
      .price {
        font-size: 20px;
      }
      .detail {
        font-size: 14px;
      }
    }
    button {
      width: unset !important;
    }
  }
  @media (max-width: 425px) {
    padding: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
    .price-detail {
      .price {
        font-size: 16px;
      }
      .detail {
        font-size: 12px;
      }
    }
  }
`;
type Props = {
  className?: string;
};
const SumDetailToCart = ({ className }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  return (
    <SumDetailToCartStyle className={className}>
      <div className={'price-detail'}>
        <div className={'price'}>฿{numberWithCommas(order.summaryPrice)}</div>
        {order?.amountConfigCoating ? (
          <div className={'detail'}>
            จำนวน {numberWithCommas(order?.amountConfigCoating.amount, 0)} ชิ้น
            ({numberWithCommas(order?.amountConfigCoating.price, 2)} บาท/ชิ้น)
          </div>
        ) : (
          <div className={'text-[#9e9e9e] text-[14px]'}>
            ระบุจำนวนผลิตสินค้า
          </div>
        )}
      </div>
      <ButtonCustom
        type={'submit'}
        classList={'btn-add-to-cart'}
        btnType={'primary'}
        name={'เพิ่มไปยังรถเข็น'}
        endIcon={<ShoppingCartOutlinedIcon sx={{ color: '#fff' }} />}
      />
    </SumDetailToCartStyle>
  );
};

export default SumDetailToCart;
