import React, { createContext, useEffect, useState } from 'react';
import CustomizeTitle from '@/components/newCustomUI/customize/CustomizeTitle';
import styled from 'styled-components';
import FormDataDetail from '@/components/newCustomUI/customize/FormDataDetail';
import TabValue from '@/components/common/TabValue';
import { useWindowSize } from 'usehooks-ts';
import { AnimatePresence, motion } from 'motion/react';
import SumDetailToCart from '@/components/newCustomUI/customize/SumDetailToCart';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { fetchGetMyCart } from '@/store/reducers/cartSlice';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/index';
import { useSelector } from 'react-redux';
import { DataFormDetailType, IMyCartItem } from '@/store/type/cart';
import { dataAddOrderSelector, setOrder } from '@/store/reducers/orderSlice';
import { isEmpty } from 'lodash';
import apiCustomize from '@/services/customize';
import apiCart from '@/services/cart';
import PreviewCustomize from '@/components/newCustomUI/customize/PreviewCustomize';
import IframePreview from '@/components/iframe-preview/IframePreview';
// const TabValue = dynamic(() => import('@/components/common/TabValue'), {
//   ssr: false,
// });
const CustomizeFormStyle = styled.div`
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 6rem;
  .container-new {
    margin-top: 6rem;
  }
  .row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    .model-preview {
      position: sticky;
      top: 180px;
      @media (max-width: 991.98px) {
        position: unset !important;
      }
    }
    .model-container {
      //position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60%;
      height: calc(100vh - 300px);
      margin: 0 auto 40px auto;
      @media only screen and (max-width: 430px) {
        width: 100%;
        display: none;
      }
      .name-label {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 0;
        z-index: 2;
        .product {
          font-size: 18px;
          font-weight: 700;
        }
        .model {
          font-size: 12px;
          font-weight: 500;
        }
      }
      .display-mode {
        position: absolute;
        top: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        z-index: 2;
      }
      .action-panel {
        position: absolute;
        bottom: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        z-index: 2;
      }
    }
  }
  @media (min-width: 992px) {
    .box-tab-select {
      display: none;
    }
  }
  @media (max-width: 1100px) {
    .row {
      .model-container {
        .action-panel {
          > div {
            column-gap: 10px;
          }
        }
      }
    }
  }
  @media (max-width: 991.98px) {
    padding-bottom: 8rem;
    .row {
      grid-template-columns: unset;
    }
  }
`;
// type CustomizeComponentProps = {
//   submit: (values: any) => void;
//   editData: GetCartType | RevisionItemType | null;
//   editMode: string;
//   custom?: any;
// };
const validationSchema = yup.object({
  modelId: yup.number().required('กรุณาเลือกโมเดล'),
  materialConfigId: yup.number().required('กรุณาเลือกวัสดุ'),
  printingId: yup.number().required('กรุณาเลือกการพิมพ์'),
  productPeriodId: yup.number().required('กรุณาเลือกระยะเวลา'),
  width: yup.number().required('กรุณากรอกความกว้าง'),
  height: yup.number().required('กรุณากรอกความสูง'),
  length: yup
    .number()
    .required('กรุณากรอกความยาว')
    .test(
      'length-not-greater-than-width',
      'ความยาวต้องน้อยกว่าหรือเท่ากับความกว้าง',
      function (value) {
        const { width } = this.parent;
        if (!value || !width) return true;
        return Number(value) <= Number(width);
      },
    ),
  // zipcode: yup.string().required('กรุณากรอกรหัสไปรษณีย์'),
});
type FormTo3dType = {
  product: string | null;
  modelName: string | null;
  modelCode: string | null;
  isSupport: boolean;
  width: number;
  minWidth: number;
  length: number;
  minLength: number;
  height: number;
  minHeight: number;
};
export const FormTo3dContext = createContext<FormTo3dType | any | null>(null);
type Props = {
  cartId?: number;
  isPreview?: boolean;
};
const CustomizeForm = ({ cartId, isPreview }: Props) =>
  // props: CustomizeComponentProps
  {
    const dispatch = useAppDispatch();
    const { order } = useSelector(dataAddOrderSelector);
    const windowSize = useWindowSize();
    const [tabValue, setTabValue] = useState<string>(
      windowSize.width <= 991 ? '0' : '991',
    );
    const [value3d, setValue3d] = useState<FormTo3dType | null>({
      product: '',
      modelName: '',
      modelCode: '',
      isSupport: true,
      width: 20,
      minWidth: 20,
      length: 20,
      minLength: 20,
      height: 20,
      minHeight: 20,
    });
    const formik = useFormik<DataFormDetailType>({
      initialValues: {
        modelId: null,
        modelSizeId: null,
        unfoldedSizeId: null,
        width: null,
        height: null,
        length: null,
        materialConfigId: null,
        printingId: null,
        modelSizeConfigDetailId: null,
        customizeSpecialTechnic: [],
        customizeCoating: [],
        sampleProduct: 1,
        productPeriodId: null,
        shippingType: 1,
        zipcode: '',
      },
      validationSchema,
      enableReinitialize: true,
      validate: (values: DataFormDetailType) => {
        const errors: any = {};
        if (Number(values.width) < 20) {
          errors.width = 'ความกว้างต้องมากกว่า 20 mm';
        }
        if (Number(values.length) < 20) {
          errors.length = 'ความยาวต้องมากกว่า 20 mm';
        }
        if (Number(values.height) < 20) {
          errors.height = 'ความสูงต้องมากกว่า 20 mm';
        }
        if (order?.zipcodeData?.errorMessage) {
          errors.zipcode = order?.zipcodeData?.errorMessage;
        }
        if (order?.zipcodeData?.id === 2 && !values.zipcode) {
          errors.zipcode = 'กรุณากรอกรหัสไปรษณีย์';
        }
        if (order?.zipcodeData?.id === 2 && values.zipcode?.length !== 5) {
          errors.zipcode = 'กรุณากรอกรหัสไปรษณีย์ 5 หลัก';
        }
        if (
          order?.specialTechnicSelectConfig === 2 &&
          isEmpty(values.customizeSpecialTechnic)
        ) {
          errors.customizeSpecialTechnic = 'กรุณาเลือกเทคนิคพิเศษ';
        }
        return errors;
      },
      onSubmit: async (values: DataFormDetailType) => {
        // console.log('values: ', values);
        onCreateCustomByModelSizeId(values);
      },
    });
    const onCreateCustomByModelSizeId = async (data: DataFormDetailType) => {
      const res = await apiCustomize.createCustomByModelSizeId(data);
      if (!res.isError) {
        await onAddCustomCart({ customizeId: res.data.id });
      }
    };
    const onAddCustomCart = async (params: {
      uuid?: string;
      customizeId: number;
    }) => {
      const res = await apiCart.addCustomCart(params);
      if (!res.isError) {
        toast.success('เพิ่มสินค้าลงในรถเข็นเรียบร้อยแล้ว');
        formik.resetForm({});
        dispatch(fetchGetMyCart());
        dispatch(
          setOrder({
            product: order.product,
          }),
        );
      } else {
        toast.error('เพิ่มสินค้าลงในรถเข็นไม่สำเร็จ');
      }
    };

    useEffect(() => {
      // modelId
      if (order?.model?.id) {
        formik.setFieldValue('modelId', order.model.id);
        formik.setFieldTouched('modelId', false);
        formik.setFieldError('modelId', '');
      } else {
        formik.setFieldValue('modelId', '');
      }

      // modelSizeId
      if (order?.modelSize?.id) {
        formik.setFieldValue('modelSizeId', order.modelSize.id);
      } else {
        formik.setFieldValue('modelSizeId', '');
      }

      // unfoldedSizeId
      if (order?.modelSize?.unfoldedSizeId) {
        formik.setFieldValue('unfoldedSizeId', order.modelSize.unfoldedSizeId);
      } else {
        formik.setFieldValue('unfoldedSizeId', '');
      }

      // materialConfigId
      if (order?.material?.materialConfig?.id) {
        formik.setFieldValue(
          'materialConfigId',
          order.material.materialConfig.id,
        );
      } else {
        formik.setFieldValue('materialConfigId', '');
      }

      // printingId
      if (order?.printing?.printing?.id) {
        formik.setFieldValue('printingId', order.printing.printing.id);
      } else {
        formik.setFieldValue('printingId', '');
      }

      // modelSizeConfigDetailId
      if (order?.amountConfigCoating?.id) {
        formik.setFieldValue(
          'modelSizeConfigDetailId',
          order.amountConfigCoating.id,
        );
      } else {
        formik.setFieldValue('modelSizeConfigDetailId', '');
      }

      // customizeSpecialTechnic
      if (!isEmpty(order?.specialTechniques)) {
        const specialTechnicConfig = order.specialTechniques.map(
          (item: any) => {
            return {
              specialTechnicConfigId: item.id,
            };
          },
        );
        formik.setFieldValue('customizeSpecialTechnic', specialTechnicConfig);
      } else {
        formik.setFieldValue('customizeSpecialTechnic', []);
      }

      // customizeCoating
      if (!isEmpty(order?.coating)) {
        const customizeCoating = order.coating.map((item: any) => {
          return {
            modelSizeConfigCoatingId: item.id,
          };
        });
        formik.setFieldValue('customizeCoating', customizeCoating);
      } else {
        formik.setFieldValue('customizeCoating', []);
      }

      // sampleProduct
      if (order?.sampleProduct) {
        formik.setFieldValue('sampleProduct', order.sampleProduct.id);
      }

      // shippingType
      if (order?.zipcodeData?.id) {
        formik.setFieldValue('shippingType', order.zipcodeData.id);
      }

      // productPeriodId
      if (order?.period?.id) {
        formik.setFieldValue('productPeriodId', order.period.id);
      } else {
        formik.setFieldValue('productPeriodId', '');
      }
    }, [order]);
    const [dataCartById, setDataCartById] = useState<IMyCartItem>();
    const fetchGetCartById = async (cartId: number) => {
      const res = await apiCart.getCartById(cartId);
      if (!res.isError) {
        setDataCartById(res.data);
      }
    };
    useEffect(() => {
      if (cartId) fetchGetCartById(cartId);
    }, [cartId]);
    return (
      <>
        <FormTo3dContext.Provider value={{ value3d, setValue3d }}>
          <CustomizeFormStyle>
            <CustomizeTitle
              productName={dataCartById?.productName || order?.product?.name}
              modelName={dataCartById?.modelName || order?.model?.name}
              isPreview={isPreview}
            />
            <div className={'container-new'}>
              <TabValue
                className={'min-992-d-none'}
                tabs={[
                  { value: 0, label: 'สเปคสินค้า' },
                  { value: 1, label: 'ตัวอย่าง' },
                ]}
                tabValue={tabValue}
                setTabValue={(value) => setTabValue(value)}
                maxWidth={'200px'}
                layoutIdKey={'underline'}
              />
              <form onSubmit={formik.handleSubmit}>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={tabValue || 'empty'}
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -10, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className={'row'}>
                      {(tabValue === '1' || tabValue === '991') && (
                        <div className="model-container model-preview">
                          {/* <IframePreview */}
                          {/*  modelId={100010} */}
                          {/*  modelType={'3d'} */}
                          {/*  width={ */}
                          {/*    order.modelSize?.width || dataCartById?.width */}
                          {/*  } */}
                          {/*  height={ */}
                          {/*    order.modelSize?.height || dataCartById?.height */}
                          {/*  } */}
                          {/*  length={ */}
                          {/*    order.modelSize?.length || dataCartById?.length */}
                          {/*  } */}
                          {/*  material={{ inside: 'white', outside: 'kraft' }} */}
                          {/*  onInfo={(info: any) => { */}
                          {/*    const { widthOfArea, heightOfArea } = info.data; */}
                          {/*    if (!isPreview) */}
                          {/*      dispatch( */}
                          {/*        setOrder({ */}
                          {/*          ...order, */}
                          {/*          modelSize: { */}
                          {/*            ...order.modelSize, */}
                          {/*            unfoldedWidth: widthOfArea, */}
                          {/*            unfoldedHeight: heightOfArea, */}
                          {/*          }, */}
                          {/*        }), */}
                          {/*      ); */}
                          {/*  }} */}
                          {/* /> */}
                          <IframePreview
                            modelId={100010}
                            modelType={'3d'}
                            width={50}
                            height={100}
                            length={20}
                            material={{ inside: 'white', outside: 'kraft' }}
                            onInfo={(info: any) => {
                              const { widthOfArea, heightOfArea } = info.data;
                              console.log(widthOfArea, heightOfArea);
                            }}
                          />
                        </div>
                      )}
                      {isPreview && tabValue !== '1' ? (
                        <PreviewCustomize dataCartById={dataCartById} />
                      ) : (
                        (tabValue === '0' || tabValue === '991') && (
                          <FormDataDetail formik={formik} cartId={cartId} />
                        )
                      )}
                    </div>
                  </motion.div>
                </AnimatePresence>
                {!isPreview && <SumDetailToCart className={'min-992-none'} />}
              </form>
            </div>
          </CustomizeFormStyle>
        </FormTo3dContext.Provider>
      </>
    );
  };

export default CustomizeForm;
