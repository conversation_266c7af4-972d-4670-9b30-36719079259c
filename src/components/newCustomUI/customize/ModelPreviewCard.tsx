import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';

const ModelPreviewCardStyle = styled.div`
  padding: 1rem;
  display: grid;
  grid-template-columns: 120px 1fr;
  align-items: center;
  gap: 1rem;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px #0000000d;
  border-radius: 16px;
`;
type Props = {
  imageUrl?: string;
  productName?: string;
  modelName?: string;
};
const ModelPreviewCard = ({ imageUrl, productName, modelName }: Props) => {
  return (
    <ModelPreviewCardStyle>
      <div>
        <Image
          src={imageUrl || ''}
          alt={'img model'}
          width={120}
          height={120}
        />
      </div>
      <div>
        <div className={'font-bold text-[20px]'}>{productName}</div>
        <div className={'font-bold text-[20px]'}>{modelName}</div>
      </div>
    </ModelPreviewCardStyle>
  );
};

export default ModelPreviewCard;
