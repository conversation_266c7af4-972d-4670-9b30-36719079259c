import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';

const InfoCardStyle = styled.div`
  padding: 1rem;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0px 0px 10px 0px #21212129;
  ul {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1rem;
    li {
      text-align: center;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      gap: 0.3rem;
      align-items: center;
      img {
        margin: auto;
      }
    }
  }
  @media (max-width: 1200px) {
    ul {
      gap: 0.7rem;
      li {
        font-size: 12px;
      }
    }
  }
  @media (max-width: 1024px) {
    padding: 1rem 0.5rem;
    ul {
      gap: 0.5rem;
      li {
        font-size: 12px;
      }
    }
  }
  @media (max-width: 425px) {
    ul {
      gap: 0.5rem;
      li {
        font-size: 10px;
        img {
          width: 28px;
        }
      }
    }
  }
  @media (max-width: 375px) {
    ul {
      gap: 0;
    }
  }
`;
const InfoCard = () => {
  const infoOptions = [
    'ปรึกษากับทีมงาน ผู้เชี่ยวชาญ',
    'วัสดุคุณภาพผลิต ด้วยผู้เชี่ยวชาญ',
    'เห็นตัวอย่างก่อน ก่อนสั่งผลิต',
    'ปรับแต่งสเปคเพื่อ ได้ราคาที่ต้องการ',
  ];
  return (
    <InfoCardStyle>
      <ul>
        {infoOptions.map((item, index) => (
          <li key={index}>
            <Image
              src={`/icons/icon_info_${index + 1}.svg`}
              alt={''}
              width={35}
              height={35}
            />
            <div
              dangerouslySetInnerHTML={{ __html: item.replace(' ', '<br />') }}
            />
          </li>
        ))}
      </ul>
    </InfoCardStyle>
  );
};

export default InfoCard;
