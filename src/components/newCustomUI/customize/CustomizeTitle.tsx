import React, { useState } from 'react';
import styled from 'styled-components';
import ButtonCustom from '@/components/common/ButtonCustom';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import Image from 'next/image';
import FormControl from '@mui/material/FormControl';
import NativeSelect from '@mui/material/NativeSelect';

const CustomizeTitleStyle = styled.div`
  background: #fff;
  padding: 1rem;
  color: #000;
  width: 100%;
  position: sticky;
  top: 74.86px;
  z-index: 300;
  border-bottom: 1px solid #eeeeee;
  .btn-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  .btn-size {
    text-align: right;
    .MuiInputBase-root {
      border: 1px solid #dbe2e5;
      border-radius: 30px;
      padding-left: 1rem;
      &:before {
        content: unset !important;
      }
      &:after,
      &:before {
        border: none;
      }
    }
  }
  .btn-group,
  .btn-size {
    min-width: 230px;
  }
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-selected {
    h2 {
      font-size: 26px;
      font-weight: 600;
    }
  }
  @media (min-width: 992px) {
    .btn-group {
      > div {
        &:last-child {
          display: none;
        }
      }
    }
  }
  @media (max-width: 991.98px) {
    .btn-group {
      > div {
        display: none;
        &:last-child {
          display: unset !important;
        }
      }
    }
    .btn-group,
    .btn-size {
      min-width: 82px;
    }
  }
  @media (max-width: 575.98px) {
    padding: 0.8rem 0.5rem;
    .title-selected {
      h2 {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 250px;
        font-size: 20px;
      }
    }
    .btn-size {
      .MuiFormControl-root {
        min-width: 55px !important;
        .MuiInputBase-root {
          padding-left: 5px;
        }
        select {
          padding-right: 8px !important;
          font-size: 12px;
          padding-bottom: 8px;
        }
        svg {
          right: -3px !important;
        }
      }
    }
  }
  @media (max-width: 425px) {
    top: 69.86px;
  }
  @media (max-width: 375px) {
    top: 61.86px;
  }
`;
type Props = {
  productName?: string;
  modelName?: string;
  isPreview?: boolean;
};
const CustomizeTitle = ({ productName, modelName, isPreview }: Props) => {
  const [value, setValue] = useState('10');
  const handleChange = (event: { target: { value: string } }) => {
    setValue(event.target.value);
  };

  return (
    <CustomizeTitleStyle>
      {!isPreview && (
        <div className={'btn-group'}>
          <ButtonCustom
            name={'กลับ'}
            startIcon={<KeyboardBackspaceRoundedIcon />}
            btnType={'secondary'}
          />
          <ButtonCustom
            name={'สินค้าทั้งหมด'}
            startIcon={
              <Image
                src={'/icons/icon_package.svg'}
                alt={'icon'}
                width={20}
                height={20}
              />
            }
            btnType={'secondary'}
          />
          <ButtonCustom
            startIcon={
              <Image
                src={'/icons/deployed_code.svg'}
                alt={'icon'}
                width={20}
                height={20}
              />
            }
            btnType={'secondary'}
          />
        </div>
      )}
      <div className={'title-selected'}>
        <h2>
          {productName || ''} {modelName ? `(${modelName})` : ''}
        </h2>
      </div>
      <div className={'btn-size'}>
        <FormControl sx={{ minWidth: 65 }} size="small">
          <NativeSelect value={value} onChange={handleChange}>
            <option value={10}>mm</option>
            <option value={20}>cm</option>
          </NativeSelect>
        </FormControl>
      </div>
    </CustomizeTitleStyle>
  );
};

export default CustomizeTitle;
