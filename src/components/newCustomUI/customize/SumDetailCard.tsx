import React, { useState } from 'react';
import styled from 'styled-components';
import TabValue from '@/components/common/TabValue';
import { numberWithCommas } from '@/utils/numberFormat';
import { AnimatePresence, motion } from 'motion/react';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector } from '@/store/reducers/orderSlice';
import { calculateTotalSummaryPrice } from '@/utils/calculator';

const SumDetailCardStyle = styled.div`
  padding: 1rem;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0px 0px 10px 0px #21212129;
  .box-tab-value {
    margin: 1rem 0;
    .box-price-sum {
      text-align: center;
      .price-sum {
        font-size: 40px;
        font-weight: bold;
      }
      .price-per-psc {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
  @media (max-width: 575.98px) {
    .box-tab-value {
      .box-price-sum {
        .price-sum {
          font-size: 36px;
        }

        .price-per-psc {
          font-size: 14px;
        }
      }
    }
  }
  @media (max-width: 375px) {
    .box-tab-value {
      .box-price-sum {
        .price-sum {
          font-size: 30px;
        }
        .price-per-psc {
          font-size: 14px;
        }
      }
    }
  }
`;
type Props = {
  amount?: number;
  amountUnitPrice?: number;
  summaryPrice?: any;
};
const SumDetailCard = ({ amount, amountUnitPrice, summaryPrice }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const [tabValue, setTabValue] = useState('0');
  const vatAmount =
    calculateTotalSummaryPrice(summaryPrice || order?.summaryPrice) * 0.07;
  const displayPriceAndAmount = (price: number, amount: number) => {
    if (price && amount) {
      return `฿${numberWithCommas(price, 2)} x ${numberWithCommas(amount, 0)} psc`;
    }
    return 'ระบุจำนวนผลิตสินค้า';
  };
  return (
    <SumDetailCardStyle>
      <TabValue
        tabs={[
          { value: 0, label: 'ไม่รวมภาษีมูลค่าเพิ่ม' },
          { value: 1, label: 'รวมภาษีมูลค่าเพิ่ม' },
        ]}
        tabValue={tabValue}
        setTabValue={(value) => setTabValue(value)}
        maxWidth={'350px'}
        layoutIdKey={'tabItem'}
      />
      <AnimatePresence mode="wait">
        <motion.div
          className={'box-tab-value'}
          key={tabValue || 'empty'}
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <>
            {tabValue === '0' && (
              <div className={'box-price-sum'}>
                <div className={'price-sum'}>
                  ฿
                  {numberWithCommas(
                    calculateTotalSummaryPrice(
                      summaryPrice || order.summaryPrice,
                    ),
                    2,
                  )}
                </div>
                <div className={'price-per-psc'}>
                  {displayPriceAndAmount(
                    amountUnitPrice || order?.amountConfigCoating?.price,
                    amount || order?.amountConfigCoating?.amount,
                  )}
                </div>
              </div>
            )}
            {tabValue === '1' && (
              <div className={'box-price-sum'}>
                <div className={'price-sum'}>
                  ฿
                  {numberWithCommas(
                    calculateTotalSummaryPrice(
                      summaryPrice || order.summaryPrice,
                    ) + vatAmount,
                    2,
                  )}
                </div>
                <div className={'price-per-psc'}>
                  {displayPriceAndAmount(
                    amountUnitPrice || order?.amountConfigCoating?.price,
                    amount || order?.amountConfigCoating?.amount,
                  )}
                </div>
              </div>
            )}
          </>
        </motion.div>
      </AnimatePresence>
    </SumDetailCardStyle>
  );
};

export default SumDetailCard;
