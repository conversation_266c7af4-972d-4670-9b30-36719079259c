import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'motion/react';
import HelpRoundedIcon from '@mui/icons-material/HelpRounded';

const FormValueStyle = styled.div`
  box-shadow: 0px 0px 20px 0px #0000000d;
  background: #ffffff80;
  border-radius: 16px;
  width: 100%;
  &.collapsed,
  &:hover {
    background: #fff;
    box-shadow: 0px 0px 20px 5px #0000000d;
  }
  .box-selected-value {
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      padding: 1.1rem 1rem;
      font-size: 14px;
      .title {
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        &:hover {
          svg {
            color: #000;
          }
        }
      }
      .value {
        font-weight: 400;
        font-size: 14px;
      }
    }
  }
  .box-value {
    overflow: hidden;
    .content {
      padding: 0 1rem 1rem 1rem;
    }
  }
  @media (max-width: 575.98px) {
    .box-selected-value {
      header {
        .title {
          font-size: 12px;
        }
        .value {
          font-size: 12px;
        }
      }
    }
  }
  @media (max-width: 425px) {
    .box-selected-value {
      header {
        padding: 1rem 0.7rem;
        .value {
          max-width: 210px;
          white-space: nowrap;
          overflow: hidden !important;
          text-overflow: ellipsis;
        }
      }
      .box-value {
        .content {
          padding: 0 0.7rem 0.7rem 0.7rem;
        }
      }
    }
  }
`;
type Props = {
  value?: string;
  title: string;
  children?: React.ReactNode;
  isOpen?: boolean;
  info?: string;
};
const AccordionFormValue = ({
  value,
  title,
  children,
  isOpen,
  info,
}: Props) => {
  const [isCollapsed, setIsCollapsed] = useState(isOpen || false);
  return (
    <FormValueStyle className={isCollapsed ? 'collapsed' : ''}>
      <motion.div className={`box-selected-value `}>
        <header onClick={() => setIsCollapsed(!isCollapsed)}>
          <div className={'title'}>
            {title}
            {info && (
              <HelpRoundedIcon sx={{ fontSize: '14px', color: '#E0E0E0' }} />
            )}
          </div>
          <div className={'value'}>
            {value || <span style={{ color: '#E0E0E0' }}>ไม่ระบุ</span>}
          </div>
        </header>
        <div>
          <AnimatePresence initial={false}>
            {isCollapsed && (
              <motion.div
                className={'box-value'}
                key={title}
                initial="collapsed"
                animate="open"
                exit="collapsed"
                variants={{
                  open: {
                    opacity: 1,
                    height: 'auto',
                  },
                  collapsed: {
                    opacity: 0,
                    height: 0,
                    transition: {
                      damping: 20,
                    },
                  },
                }}
                transition={{
                  type: 'spring',
                  damping: 12,
                  duration: 0.4,
                  ease: 'easeInOut',
                }}
              >
                <div className={'content'}>{children && children}</div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </FormValueStyle>
  );
};

export default AccordionFormValue;
