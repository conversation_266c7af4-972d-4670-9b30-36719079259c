import React from 'react';
import { IMyCartItem, ISpecialTechnicItem } from '@/store/type/cart';
import CustomizePreviewCard from '@/components/newCustomUI/customize/CustomizePreviewCard';
import { displayValueArr } from '@/utils/display';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/numberFormat';
import dayjs from 'dayjs';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import {
  calculateCompletionDate,
  calculateSpecialTechniquesTotal,
} from '@/utils/calculator';
import InfoCard from '@/components/newCustomUI/customize/InfoCard';
import SumDetailCard from '@/components/newCustomUI/customize/SumDetailCard';
import ModelPreviewCard from '@/components/newCustomUI/customize/ModelPreviewCard';

const format = 'DD MMM';
dayjs.extend(buddhistEra);
dayjs.locale('th');
type Props = {
  dataCartById?: IMyCartItem;
};
const PreviewCustomize = ({ dataCartById }: Props) => {
  return (
    <div className={'flex flex-col gap-[1rem]'}>
      <ModelPreviewCard
        imageUrl={dataCartById?.modelImage}
        productName={dataCartById?.productName}
        modelName={dataCartById?.modelName}
      />
      <CustomizePreviewCard
        title={'ขนาด'}
        value={
          dataCartById &&
          `${dataCartById?.width} x ${dataCartById?.length} x ${dataCartById?.height} mm`
        }
      />
      <CustomizePreviewCard
        title={'วัสดุ'}
        value={
          dataCartById &&
          `${dataCartById?.materialName} ${dataCartById.gramGsm} แกรม`
        }
      />
      <CustomizePreviewCard
        title={'การพิมพ์'}
        value={dataCartById && `${dataCartById?.printName}`}
      />
      <CustomizePreviewCard
        title={'เคลือบ'}
        value={
          dataCartById &&
          `${displayValueArr(dataCartById?.coatings, 'เคลือบ', 'coatingName')}`
        }
      />
      {!isEmpty(dataCartById?.specialTechnics) &&
        dataCartById?.specialTechnics.map(
          (item: ISpecialTechnicItem, key: number) => {
            return (
              <CustomizePreviewCard
                key={key}
                title={'เทคนิคพิเศษ'}
                value={
                  <div className="flex flex-col items-end">
                    <div>{item.specialTechnicName}</div>
                    <div className={'text-[12px]'}>
                      พื้นที่ {item.areaSizePercentage}%
                    </div>
                  </div>
                }
              />
            );
          },
        )}
      <CustomizePreviewCard
        title={'จำนวน'}
        value={
          dataCartById &&
          `${numberWithCommas(dataCartById.amount, 0)} ชิ้น (฿${numberWithCommas(dataCartById.amountUnitPrice, 2)}/ชิ้น)`
        }
      />
      <CustomizePreviewCard
        title={'ตัวอย่างสินค้า'}
        value={dataCartById && `${dataCartById.sampleProductName}`}
      />
      <CustomizePreviewCard
        title={'การรับสินค้า'}
        value={
          dataCartById &&
          `${dataCartById?.zipcode ? `จัดส่ง ${dataCartById?.zipcode}` : 'รับสินค้าด้วยตัวเอง'}`
        }
      />
      <CustomizePreviewCard
        title={'ระยะเวลาการผลิต'}
        value={
          dataCartById &&
          `${dataCartById.productPeriodName} (จัดส่ง ${dayjs(
            calculateCompletionDate(dataCartById.productMaxPeriod + 1),
          ).format(format)})`
        }
      />
      <InfoCard />
      <SumDetailCard
        amount={dataCartById?.amount}
        amountUnitPrice={dataCartById?.amountUnitPrice}
        summaryPrice={{
          amountPrice:
            (dataCartById?.amount || 0) * (dataCartById?.amountUnitPrice || 0),
          productPeriodPrice: dataCartById?.productPeriodPrice || 0,
          specialTechnicPrice: calculateSpecialTechniquesTotal(
            dataCartById?.specialTechnics || [],
          ),
        }}
      />
    </div>
  );
};

export default PreviewCustomize;
