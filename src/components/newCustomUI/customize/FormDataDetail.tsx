import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import AccordionFormValue from '@/components/newCustomUI/customize/AccordionFormValue';
import FormSelectModel from '@/components/newCustomUI/customize/form/FormSelectModel';
import FormSelectSize from '@/components/newCustomUI/customize/form/FormSelectSize';
import MaterialSelectCard from '@/components/newCustomUI/customize/form/MaterialSelectCard';
import PrintSelectCard from '@/components/newCustomUI/customize/form/PrintSelectCard';
import ArtworkUploadCard from '@/components/newCustomUI/customize/form/ArtworkUploadCard';
import CoatingSelectCard from '@/components/newCustomUI/customize/form/CoatingSelectCard';
import SpecialTechniquesCard from '@/components/newCustomUI/customize/form/SpecialTechniquesCard';
import AmountSelectCard from '@/components/newCustomUI/customize/form/AmountSelectCard';
import ProductSamplesSelectCard from '@/components/newCustomUI/customize/form/ProductSamplesSelectCard';
import HomeDeliverySelectCard from '@/components/newCustomUI/customize/form/HomeDeliverySelectCard';
import ProductionPeriodSelectCard from '@/components/newCustomUI/customize/form/ProductionPeriodSelectCard';
import InfoCard from '@/components/newCustomUI/customize/InfoCard';
import SumDetailCard from '@/components/newCustomUI/customize/SumDetailCard';
import SumDetailToCart from '@/components/newCustomUI/customize/SumDetailToCart';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';
import { useSelector } from 'react-redux';
import { dataAddOrderSelector } from '@/store/reducers/orderSlice';
import { numberWithCommas } from '@/utils/numberFormat';
import { isEmpty } from 'lodash';

const FormDataDetailStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 640px;
  min-width: 640px;
  @media (max-width: 1440px) {
    max-width: 580px;
    min-width: 580px;
  }
  @media (max-width: 1300px) {
    max-width: 500px;
    min-width: 500px;
  }
  @media (max-width: 1200px) {
    max-width: 450px;
    min-width: 450px;
  }
  @media (max-width: 1100px) {
    max-width: 420px;
    min-width: 420px;
  }
  @media (max-width: 1024px) {
    max-width: 390px;
    min-width: 390px;
  }
  @media (max-width: 991.98px) {
    max-width: 100%;
    min-width: 100%;
  }
`;
type Props = {
  formik: FormikProps<DataFormDetailType>;
  cartId?: number;
};
const FormDataDetail = ({ formik }: Props) => {
  const { order } = useSelector(dataAddOrderSelector);
  const valueProductModel = `${order.product.name} ${order.model?.name ? `(${order.model?.name})` : ''}`;
  const valueSize = `${order.modelSize?.width || '0'} x ${order.modelSize?.length || '0'} x ${order.modelSize?.height || '0'} mm`;
  const valueMaterial =
    order.material?.materialConfig &&
    `${order.material?.materialConfig.materialsName} ${order.material?.materialConfig.gsm} แกรม`;
  const valuePrinting = order.printing?.printing?.name;
  const valueAmount =
    order?.amountConfigCoating?.amount &&
    `${numberWithCommas(Number(order?.amountConfigCoating?.amount))} ชิ้น (฿${numberWithCommas(Number(order?.amountConfigCoating?.price), 2)}/ชิ้น)`;
  const valueArtwork = order?.artwork?.name;
  const valueZipcode = order?.zipcodeData
    ? order?.zipcodeData?.value
    : 'รับสินค้าด้วยตัวเอง';
  const valuePeriod =
    order?.period?.completionDate &&
    `${order?.period?.name} (จัดส่ง ${order?.period?.completionDate})`;
  const valueCoating = !isEmpty(order.coating)
    ? order.coating.map((item: any, index: number) => {
        if (index === order.coating.length - 1) {
          return `${item.coating.name}`;
        }
        return `${item.coating.name} • `;
      })
    : 'ไม่เคลือบ';
  const valueSpecialTechnics = !isEmpty(order?.specialTechniques)
    ? order?.specialTechniques.map((item: any, index: number) => {
        if (index === order.specialTechniques.length - 1) {
          return `${item.specialTechnic.name}`;
        }
        return `${item.specialTechnic.name} • `;
      })
    : 'ไม่มีเทคนิคพิเศษ';
  const valueSampleProduct = order?.sampleProduct
    ? order?.sampleProduct.name
    : '';
  const accordionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const scrollToFirstError = () => {
    const errorFields = [
      { key: 'modelId' as keyof DataFormDetailType, ref: 'product' },
      { key: 'materialConfigId' as keyof DataFormDetailType, ref: 'material' },
      { key: 'printingId' as keyof DataFormDetailType, ref: 'printing' },
      { key: 'zipcode' as keyof DataFormDetailType, ref: 'zipcode' },
      { key: 'productPeriodId' as keyof DataFormDetailType, ref: 'period' },
    ];

    for (const field of errorFields) {
      if (formik.errors[field.key] && formik.touched[field.key]) {
        const element = accordionRefs.current[field.ref];
        if (field.key === 'modelId') {
          const elementTop = element?.getBoundingClientRect().top;
          window.scrollTo({
            top: elementTop,
            behavior: 'smooth',
          });
          break;
        }
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
          break;
        }
      }
    }
  };
  useEffect(() => {
    if (formik.submitCount > 0 && Object.keys(formik.errors).length > 0) {
      scrollToFirstError();
    }
  }, [formik.isSubmitting]);

  return (
    <FormDataDetailStyle>
      <AccordionFormValue
        title={'สินค้า'}
        value={valueProductModel}
        isOpen={true}
        isError={formik.touched.modelId && Boolean(formik.errors.modelId)}
        helperText={
          formik.touched.modelId && (formik.errors.modelId as React.ReactNode)
        }
        refEl={(el: any) => (accordionRefs.current.product = el)}
      >
        <FormSelectModel />
      </AccordionFormValue>
      <AccordionFormValue title={'ขนาด'} value={valueSize} isOpen={true}>
        <FormSelectSize formik={formik} />
      </AccordionFormValue>
      <AccordionFormValue
        title={'วัสดุ'}
        value={valueMaterial}
        isOpen={true}
        isError={
          formik.touched.materialConfigId &&
          Boolean(formik.errors.materialConfigId)
        }
        helperText={
          formik.touched.materialConfigId &&
          (formik.errors.materialConfigId as React.ReactNode)
        }
        refEl={(el: any) => (accordionRefs.current.material = el)}
      >
        <MaterialSelectCard />
      </AccordionFormValue>
      <AccordionFormValue
        title={'การพิมพ์'}
        value={valuePrinting}
        isOpen={true}
        isError={formik.touched.printingId && Boolean(formik.errors.printingId)}
        helperText={
          formik.touched.printingId &&
          (formik.errors.printingId as React.ReactNode)
        }
        refEl={(el: any) => (accordionRefs.current.printing = el)}
      >
        <PrintSelectCard />
      </AccordionFormValue>
      <AccordionFormValue
        title={'Artwork'}
        value={valueArtwork}
        isOpen={true}
        info={'Artwork is not required for this product'}
      >
        <ArtworkUploadCard />
      </AccordionFormValue>
      <AccordionFormValue title={'จำนวน'} value={valueAmount} isOpen={true}>
        <AmountSelectCard />
      </AccordionFormValue>
      <AccordionFormValue title={'เคลือบ'} value={valueCoating} isOpen={true}>
        <CoatingSelectCard />
      </AccordionFormValue>
      <AccordionFormValue
        title={'เทคนิคพิเศษ'}
        value={valueSpecialTechnics}
        isOpen={true}
        isError={
          formik.touched.customizeSpecialTechnic &&
          Boolean(formik.errors.customizeSpecialTechnic)
        }
        helperText={
          formik.touched.customizeSpecialTechnic &&
          (formik.errors.customizeSpecialTechnic as React.ReactNode)
        }
      >
        <SpecialTechniquesCard formik={formik} />
      </AccordionFormValue>
      <AccordionFormValue
        title={'ตัวอย่างสินค้า'}
        value={valueSampleProduct}
        isOpen={true}
      >
        <ProductSamplesSelectCard />
      </AccordionFormValue>
      <AccordionFormValue
        title={'การรับสินค้า'}
        value={valueZipcode}
        isOpen={true}
        isError={formik.touched.zipcode && Boolean(formik.errors.zipcode)}
        helperText={
          formik.touched.zipcode && (formik.errors.zipcode as React.ReactNode)
        }
        refEl={(el: any) => (accordionRefs.current.zipcode = el)}
      >
        <HomeDeliverySelectCard formik={formik} />
      </AccordionFormValue>
      <AccordionFormValue
        title={'ระยะเวลาการผลิต'}
        value={valuePeriod}
        isOpen={true}
        isError={
          formik.touched.productPeriodId &&
          Boolean(formik.errors.productPeriodId)
        }
        helperText={
          formik.touched.productPeriodId &&
          (formik.errors.productPeriodId as React.ReactNode)
        }
        refEl={(el: any) => (accordionRefs.current.period = el)}
      >
        <ProductionPeriodSelectCard />
      </AccordionFormValue>
      <InfoCard />
      <SumDetailCard />
      <SumDetailToCart className={'max-991-none'} />
    </FormDataDetailStyle>
  );
};

export default FormDataDetail;
