import React, { useEffect, useState } from 'react';
import Checkbox from '@mui/material/Checkbox';
import styled from 'styled-components';
import CartDataCard from '@/components/newCustomUI/cart/CartDataCard';
import { ICartValue, IMyCartItem } from '@/store/type/cart';
import { FormikProps } from 'formik';

const CartSelectFormStyle = styled.div`
  display: flex;
  align-items: start;
  .MuiButtonBase-root {
    &.MuiCheckbox-root {
      svg {
        font-size: 2rem;
      }
    }
  }
  .MuiTypography-root {
    width: 100%;
  }
`;
type Props = { dataModel: IMyCartItem; formik: FormikProps<ICartValue> };
const CartSelectForm = ({ dataModel, formik }: Props) => {
  const [checked, setChecked] = useState<boolean>(true);
  useEffect(() => {
    setChecked(
      formik.values.cartValue.some(
        (item: IMyCartItem) => item.cartId === dataModel.cartId,
      ),
    );
  }, [formik.values.cartValue]);
  return (
    <CartSelectFormStyle>
      <Checkbox
        checked={checked}
        onChange={(event) => {
          const isChecked = event.target.checked;
          setChecked(isChecked);
          if (isChecked) {
            formik.setFieldValue('cartValue', [
              ...formik.values.cartValue,
              dataModel,
            ]);
          } else {
            const filteredItems = formik.values.cartValue.filter(
              (item: IMyCartItem) => item.cartId !== dataModel.cartId,
            );
            formik.setFieldValue('cartValue', filteredItems);
          }
        }}
        sx={{
          color: '#eee',
          '&.Mui-checked': {
            color: '#0050ff',
          },
        }}
      />
      <CartDataCard dataModel={dataModel} />
    </CartSelectFormStyle>
  );
};

export default CartSelectForm;
