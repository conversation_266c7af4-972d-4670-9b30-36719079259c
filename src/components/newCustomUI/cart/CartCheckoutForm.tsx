import React, { useState } from 'react';
import styled from 'styled-components';
import ButtonCustom from '@/components/common/ButtonCustom';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import { IMyCartItem } from '@/store/type/cart';
import ModalAppCustom from '@/components/common/ModalAppCustom';
import DiscountList from '@/components/newCustomUI/cart/DiscountList';

const CartCheckoutFormStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  hr {
    background: #eeeeee;
  }
  ul {
    font-size: 14px;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        span {
          color: #0050ff;
        }
      }
      .value {
        font-weight: bold;
      }
    }
  }
  .detail-coupon {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    .list {
      border-radius: 6px;
      font-size: 12px;
      padding: 0.5rem;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .box-price-sum {
    padding: 1rem;
    border-radius: 12px;
    background: #f5f5f5;
    text-align: center;
    .price-sum {
      font-size: 30px;
      font-weight: bold;
    }
  }
`;
type Props = {
  totalCartPrice: number;
  cartValue: IMyCartItem[];
};
const CartCheckoutForm = ({ totalCartPrice, cartValue }: Props) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const vatAmount = totalCartPrice * 0.07;
  const shippingCost = 100;
  const totalCheckoutPrice = totalCartPrice + vatAmount + shippingCost;
  return (
    <CartCheckoutFormStyle>
      <div className={'flex items-center justify-between'}>
        <div className={'font-bold text-[22px]'}>Checkout</div>
        <ButtonCustom
          classList={'font-bold text-0050ff'}
          name={'ดาวน์โหลดใบเสนอราคา'}
          btnType={'secondary'}
          startIcon={
            <Image
              src={'/icons/icon_checkout_download.svg'}
              alt={'icon'}
              width={25}
              height={25}
            />
          }
        />
      </div>
      <ul>
        <li>
          <div className={'name'}>
            การสั่งซื้อ <span>{cartValue.length} รายการ</span>
          </div>
          <div className={'value'}>{numberWithCommas(totalCartPrice, 2)}</div>
        </li>
        <li>
          <div className={'name'}>รวมค่าจัดส่ง</div>
          <div className={'value'}>{numberWithCommas(shippingCost, 0)}</div>
        </li>
        <li>
          <div className={'name'}>ภาษีมูลค่าเพิ่ม 7%</div>
          <div className={'value'}>{numberWithCommas(vatAmount, 2)}</div>
        </li>
        <li>
          <div className={'name'}>รวมทั้งหมด</div>
          <div className={'value'}>
            {numberWithCommas(totalCartPrice + vatAmount + shippingCost, 2)}
          </div>
        </li>
      </ul>
      <hr />
      <div className={'flex items-center justify-between'}>
        <div className={'flex items-center gap-2'}>
          <Image
            src={'/icons/icon_confirmation_number.svg'}
            alt={'icon'}
            width={20}
            height={20}
          />
          <span className={'text-[14px] font-bold'}>
            โค้ดส่วนลดของ Digiboxs
          </span>
        </div>
        <ButtonCustom
          classList={'font-bold text-0050ff'}
          name={'กดใช้โค้ด'}
          btnType={'text'}
          type={'button'}
          onAction={() => setOpenModal(true)}
        />
        <ModalAppCustom
          title={'เลือกโค้ดส่วนลดของ Digiboxs'}
          handleClose={() => setOpenModal(false)}
          open={openModal}
        >
          <DiscountList />
        </ModalAppCustom>
      </div>
      {/* <div className={'detail-coupon'}> */}
      {/*  <div className={'list'}> */}
      {/*    <div>คูปองลดสินค้า • ส่วนลด 10% ลดสูงสุด 300฿</div> */}
      {/*    <div className={'value text-[#D32F2F]'}> */}
      {/*      -{numberWithCommas(300.05, 2)} */}
      {/*    </div> */}
      {/*  </div> */}
      {/*  <div className={'list'}> */}
      {/*    <div>คูปองลดค่าบริการ • ส่งฟรี</div> */}
      {/*    <div className={'value text-[#D32F2F]'}> */}
      {/*      -{numberWithCommas(100.05, 2)} */}
      {/*    </div> */}
      {/*  </div> */}
      {/* </div> */}
      <div className={'box-price-sum'}>
        <div className={'text-[14px]'}>ยอดชำระเงินทั้งหมด</div>
        <div className={'price-sum'}>
          ฿{numberWithCommas(totalCheckoutPrice, 2)}
        </div>
      </div>
      <div>
        <ButtonCustom
          name={`Checkout ${cartValue.length > 0 ? `(${cartValue.length})` : ''}`}
          btnType={'primary'}
          widthFull
          type={'submit'}
        />
      </div>
      <div
        className={
          'flex items-center justify-center gap-2 text-[12px] text-[#212121] my-2'
        }
      >
        <div>เงื่อนไขการบริการ</div>
        <div>|</div>
        <div>นโยบายความเป็นส่วนตัว</div>
        <div>|</div>
        <div>วิธีสั่งซื้อสินค้า</div>
      </div>
      <div className={'text-[14px] text-center'}>
        ชำระเงินสำหรับคำสั่งซื้อและอัพโหลดไฟล์อาร์ตเวิร์คของคุณก่อน <br />
        วันพุธที่ 18 มิถุนายน 2568 เวลา 12:30:00 การชำระเงินล่าช้า และ/หรือ
        <br /> อัพโหลดไฟล์อาร์ตเวิร์คล่าช้า อาจส่งผลให้การสั่งซื้อของคุณล่าช้า
      </div>
    </CartCheckoutFormStyle>
  );
};

export default CartCheckoutForm;
