import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import ButtonCustom from '@/components/common/ButtonCustom';
import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded';
import ModeEditOutlineRoundedIcon from '@mui/icons-material/ModeEditOutlineRounded';
import MoreHorizOutlinedIcon from '@mui/icons-material/MoreHorizOutlined';
import { numberWithCommas } from '@/utils/numberFormat';
import ShoppingCartOutlinedIcon from '@mui/icons-material/ShoppingCartOutlined';
import BoltOutlinedIcon from '@mui/icons-material/BoltOutlined';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import { calculateCompletionDate } from '@/utils/calculator';
import dayjs from 'dayjs';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import { IMyCartItem } from '@/store/type/cart';
import Avatar from '@mui/material/Avatar';
import apiCart from '@/services/cart';
import { AnimatePresence, motion } from 'motion/react';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import { fetchGetMyCart } from '@/store/reducers/cartSlice';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/index';
import MenuItemMore from '@/components/common/MenuItemMore';
import ShareIcon from '@mui/icons-material/Share';
import { useRouter } from 'next/router';
import { displayValueArr } from '@/utils/display';

dayjs.extend(buddhistEra);
dayjs.locale('th');

const CartDataCardStyle = styled.div`
  width: 100%;
  border: 1px solid #eee;
  border-radius: 16px;
  box-shadow: 0px 0px 10px 0px #0000000d;
  overflow: hidden;
  .footer-detail {
    font-size: 12px;
    padding: 0.7rem 1rem;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.2rem;
    svg {
      font-size: 18px;
    }
    .box-detail {
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }
  }
  .price {
    font-weight: bold;
    font-size: 20px;
    color: #0050ff;
  }
  .detail {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
    //max-width: 430px;
    .name {
      font-size: 22px;
      font-weight: bold;
    }
    .description {
      font-size: 14px;
    }
    .btn-action {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      .amount {
        height: 38.78px;
        padding: 0.3rem 1rem;
        border-radius: 6px;
        color: #0050ff;
        background: #eef3ff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
    }
  }
`;
type Props = { dataModel: IMyCartItem };
const format = 'dd DD/MM/YYYY';
const CartDataCard = ({ dataModel }: Props) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  // const [openModal, setOpenModal] = useState(false);
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const valueCoating = displayValueArr(
    dataModel?.coatings,
    'เคลือบ',
    'coatingName',
    'ไม่เคลือบ',
  );
  const valueSpecialTechnics = displayValueArr(
    dataModel?.specialTechnics,
    'เทคนิคพิเศษ',
    'specialTechnicName',
    'ไม่เทคนิคพิเศษ',
  );
  const periodDetail = (id: number) => {
    switch (id) {
      case 1:
        return {
          color: '#8BC34A',
        };
      case 2:
        return {
          color: '#F9A925',
        };
      case 3:
        return {
          color: '#D32F2F',
        };
      default:
        return {
          color: '#D32F2F',
        };
    }
  };
  const onDeleteItemCart = async (cartId: number) => {
    const res = await apiCart.deleteCartById(cartId);
    if (!res.isError) {
      toast.success('ลบสินค้าในรถเข็นเรียบร้อยแล้ว');
      dispatch(fetchGetMyCart());
    }
  };
  return (
    <CartDataCardStyle>
      <div className={'flex justify-between items-start p-[.7rem]'}>
        <div className={'flex items-start gap-2'}>
          {/* <Image */}
          {/*  src={`/${dataModel.modelName}`} */}
          {/*  alt={'img'} */}
          {/*  width={90} */}
          {/*  height={90} */}
          {/* /> */}
          <Avatar
            alt={dataModel.productName}
            src={dataModel.modelImage}
            sx={{ width: 90, height: 90 }}
            variant="rounded"
          />
          <div className={'detail'}>
            <div className={'name'}>
              {dataModel.productName} ({dataModel.modelName})
            </div>
            <div className={'description'}>
              ขนาด {numberWithCommas(dataModel.width, 0)}x
              {numberWithCommas(dataModel.length, 0)}x
              {numberWithCommas(dataModel.height, 0)} mm,
              {dataModel.materialName} {numberWithCommas(dataModel.gramGsm, 0)}{' '}
              แกรม, ระบบพิมพ์ {dataModel.printName} , {valueCoating},
              {valueSpecialTechnics}
            </div>
            <div className={'btn-action'}>
              <ButtonCustom
                name={'รายละเอียด'}
                btnType={'secondary'}
                size={'small'}
              />
              <>
                <ButtonCustom
                  name={`จำนวน ${numberWithCommas(dataModel.amount, 0)} ชิ้น`}
                  btnType={'secondary'}
                  size={'small'}
                  endIcon={
                    <ArrowDropDownRoundedIcon
                      style={{ color: '#0050FF', fontSize: '1.5rem' }}
                    />
                  }
                  // onAction={() => setOpenModal(true)}
                />
                {/* <ModalAppCustom */}
                {/*  title={`จำนวน`} */}
                {/*  value={`${numberWithCommas(dataModel.amount, 0)} ชิ้น (฿${numberWithCommas(dataModel.amountUnitPrice, 2)}/ชิ้น)`} */}
                {/*  open={openModal} */}
                {/*  handleClose={() => setOpenModal(false)} */}
                {/* > */}
                {/*  <AmountSelectCard printingId={dataModel.printingId} /> */}
                {/* </ModalAppCustom> */}
              </>
              <div className={'amount'}>
                ฿{numberWithCommas(dataModel.amountUnitPrice, 2)} /ชิ้น
              </div>
              <MenuItemMore
                name={<MoreHorizOutlinedIcon />}
                idMenu={`menu-more-${dataModel.cartId}`}
                idButton={`button-more-${dataModel.cartId}`}
                menuItems={[
                  {
                    name: 'ลิงก์',
                    icon: <ShareIcon />,
                    onAction: () => {
                      router.push(`/customize/preview/${dataModel.cartId}`);
                    },
                  },
                ]}
              />
              <ButtonCustom
                startIcon={<ModeEditOutlineRoundedIcon />}
                btnType={'secondary'}
                size={'small'}
              />
              <ButtonCustom
                startIcon={
                  <Image
                    src={'/icons/icon_cart_delete.svg'}
                    alt={'icon delete'}
                    width={22}
                    height={22}
                  />
                }
                btnType={'secondary'}
                size={'small'}
                onAction={() => setOpenModalDelete(!openModalDelete)}
              />
              <AnimatePresence initial={false}>
                {openModalDelete ? (
                  <motion.div
                    className={'flex items-center gap-[0.3rem]'}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0 }}
                    key="cart-card-delete"
                  >
                    <ButtonCustom
                      startIcon={<ClearRoundedIcon />}
                      btnType={'cancel'}
                      size={'small'}
                      onAction={() => setOpenModalDelete(false)}
                    />
                    <ButtonCustom
                      startIcon={<CheckRoundedIcon />}
                      btnType={'confirm'}
                      size={'small'}
                      onAction={() => onDeleteItemCart(dataModel.cartId)}
                    />
                  </motion.div>
                ) : null}
              </AnimatePresence>

              {/* <ModalAppCustom */}
              {/*  title={'ยืนยันการลบสินค้าในตระกร้า'} */}
              {/*  open={openModalDelete} */}
              {/*  handleClose={() => setOpenModalDelete(false)} */}
              {/* > */}
              {/*  /ๅ_ */}
              {/* </ModalAppCustom> */}
            </div>
          </div>
        </div>
        <div className={'price'}>
          ฿{numberWithCommas(dataModel.totalPrice, 2)}
        </div>
      </div>
      <div className={'footer-detail'}>
        <div className={'box-detail'}>
          <ShoppingCartOutlinedIcon />
          <span>สั่งซื้อสินค้าวันนี้</span>
          <div className={'font-bold'}>{dayjs().format(format)}</div>
        </div>
        <LineSeparator />
        <div className={'box-detail'}>
          <BoltOutlinedIcon />
          <span>ผลิตเแบบ</span>
          <div
            className={`font-bold`}
            style={{ color: periodDetail(dataModel.productPeriodId).color }}
          >
            {dataModel.productPeriodName}
          </div>
          <div>จะเสร็จใน</div>
          <div className={'font-bold'}>
            {dayjs(calculateCompletionDate(dataModel.productMaxPeriod)).format(
              format,
            )}
          </div>
        </div>
        <LineSeparator />
        {dataModel?.zipcode ? (
          <div className={'box-detail'}>
            <LocalShippingOutlinedIcon />
            <span>ส่งมอบสินค้าให้ที่</span>
            <div className={'text-[#0050FF] font-bold'}>
              {dataModel?.zipcode}
            </div>
            <div className={'font-bold'}>
              {dayjs(
                calculateCompletionDate(dataModel.productMaxPeriod + 1),
              ).format(format)}
            </div>
          </div>
        ) : (
          <div className={'box-detail'}>
            <LocalShippingOutlinedIcon />
            <span>รับสินค้าเอง</span>
            <div className={'font-bold'}>
              {dayjs(
                calculateCompletionDate(dataModel.productMaxPeriod + 1),
              ).format(format)}
            </div>
          </div>
        )}
      </div>
    </CartDataCardStyle>
  );
};

const LineSeparatorStyle = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  .dot {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: #000;
  }
  .line {
    width: 30px;
    height: 1px;
    background: #000;
  }
`;
const LineSeparator = () => {
  return (
    <LineSeparatorStyle>
      <div className={'dot'}></div>
      <div className={'line'}></div>
      <div className={'dot'}></div>
    </LineSeparatorStyle>
  );
};
export default CartDataCard;
