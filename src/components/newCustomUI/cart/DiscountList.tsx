import React from 'react';
import styled from 'styled-components';
import TextFieldInput from '@/components/common/TextFieldInput';
import ButtonCustom from '@/components/common/ButtonCustom';
import CouponCard from '@/components/newCustomUI/coupon/CouponCard';

const DiscountListStyle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  .box-search-main {
    position: sticky;
    top: 72px;
    z-index: 5;
    background: #fff;
    .box-search {
      padding: 10px 16px;
      background: #f5f5f5;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0.5rem;
      span {
        font-weight: bold;
      }
    }
  }
`;
const DiscountList = () => {
  return (
    <DiscountListStyle>
      <div className={'box-search-main'}>
        <div className="box-search">
          <span className={'w-[80px]'}>เพิ่มโค้ด</span>
          <TextFieldInput placeholder={'โค้ดส่วนลด'} />
          <ButtonCustom
            name={'ใช้โค้ด'}
            btnType={'tertiary'}
            classList={'w-[100px]'}
          />
        </div>
      </div>

      <div className={'flex flex-col gap-2'}>
        {[...Array(5)].map((_, index) => (
          <CouponCard key={index} />
        ))}
      </div>
    </DiscountListStyle>
  );
};

export default DiscountList;
