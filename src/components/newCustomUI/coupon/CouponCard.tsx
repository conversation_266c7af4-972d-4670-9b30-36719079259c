import React, { useState } from 'react';
import styled from 'styled-components';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import { Checkbox } from '@mui/material';
import BoltIcon from '@mui/icons-material/Bolt';

const CouponCardStyle = styled.div`
  position: relative;
  display: grid;
  grid-template-columns: 100px 1fr;
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -8px;
    right: 0;
    bottom: 0;
    margin: auto;
    margin-left: 0;
    background: #fff;
    width: 15px;
    height: 15px;
    border-radius: 50%;
  }
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: -8px;
    bottom: 0;
    margin: auto;
    margin-right: 0;
    background: #fff;
    width: 15px;
    height: 15px;
    border-radius: 50%;
  }
  .box-icon-name {
    padding: 0.5rem;
    background: #0050ff;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    svg {
      color: #fff;
    }
    .category-name {
      text-align: center;
      color: #fff;
      font-size: 14px;
    }
  }
  .detail {
    padding: 0.5rem 1rem;
    background: #eef3ff;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
  }
`;
const CouponCard = () => {
  const [checked, setChecked] = useState<boolean>(true);
  return (
    <CouponCardStyle>
      <div className={'box-icon-name'}>
        <ShoppingCartIcon />
        <div className={'category-name'}>
          ส่วนลด <br />
          สินค้า
        </div>
      </div>
      <div className={'detail'}>
        <div className={'flex items-start justify-between gap-2'}>
          <div className={'text-wrap w-[200px] text-[14px] font-bold'}>
            ส่วนลด 500 บาท เมื่อสั่งสินค้า ครบ 8,000 บาท
          </div>
          <Checkbox
            checked={checked}
            onChange={(event) => {
              const isChecked = event.target.checked;
              setChecked(isChecked);
            }}
            sx={{
              padding: 0,
              color: '#eee',
              svg: {
                path: {
                  fill: '#fff !important',
                },
              },
              '&.Mui-checked': {
                svg: {
                  path: {
                    fill: '#0050ff !important',
                  },
                },
                color: '#0050ff',
              },
            }}
          />
        </div>
        <div className={'flex items-center gap-1 text-[12px] text-[#000]'}>
          <div>ใกล้หมดอายุ: เหลือ 13 ชั่วโมง</div>
          <span className={'text-[#0050FF] cursor-pointer'}>อ่านเงื่อนไข</span>
        </div>
        <div
          className={
            'text-[#0050FF] text-[12px] flex items-center justify-center gap-1 bg-[#FFFFFF] rounded-md w-[120px] h-[25px]'
          }
        >
          <BoltIcon fontSize={'small'} />
          <span className={'pr-2 text-[#0050FF]'}>เหลือ 100 สิทธิ์</span>
        </div>
      </div>
    </CouponCardStyle>
  );
};

export default CouponCard;
