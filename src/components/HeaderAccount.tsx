import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import styled from 'styled-components';

const HeaderAccountStyle = styled.header`
  width: 100%;
  position: fixed;
  height: 64px;
  background-color: black;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .subtitle {
    color: white;
    font-size: 20px;
    font-weight: 700;
  }
`;

const HeaderAccount = () => {
  const router = useRouter();
  return (
    <HeaderAccountStyle>
      <Image
        priority
        className="cursor-pointer ml-4"
        src="/icons/back-gray.svg"
        alt=""
        onClick={() => router.back()}
        width={40}
        height={40}
      />
      <span className="subtitle">แก้ไขข้อมูลส่วนตัว</span>
      <div></div>
    </HeaderAccountStyle>
  );
};

export default HeaderAccount;
