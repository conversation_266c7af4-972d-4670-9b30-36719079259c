import React, { Fragment, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import styled, { css } from 'styled-components';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { deleteCookie, getCookie } from 'cookies-next';
import {
  getUserProfile,
  userSelector,
} from '@/store/reducers/backup/userSlice';
import { useAppDispatch } from '@/store/index';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import apiUser from '@/services/user';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import { isEmpty, isUndefined } from 'lodash';
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

const HeaderStyle = styled.header`
  backdrop-filter: blur(20px);
  background: #212121e5;
  position: fixed;
  top: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 5;
  justify-content: space-between;
`;

const MobileDrawerStyle = styled.div<{ $show: boolean }>`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #212121;
  position: fixed;
  top: 0;
  left: 0;
  transition: 0.5s;
  overflow: auto;
  ${({ $show }) =>
    $show
      ? css`
          opacity: 1;
          z-index: 10;
          //transform: translateY(0) scale(100%);
        `
      : css`
          //transform: translateY(-10%) scale(70%);
          opacity: 0;
          //z-index: -1;
          visibility: hidden;
        `}
  .btn-bottom {
    background-color: #212121;
  }
  @media (max-width: 425px) {
    .btn-bottom {
      padding: 1.5rem 0.5rem;
      justify-content: space-between;
      button {
        margin-right: 0;
      }
    }
  }
  @media (max-width: 375px) {
    .btn-name {
      padding-left: 0.8rem;
      justify-content: start;
      text-align: left;
      span {
        width: 100px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis;
      }
    }
  }
  @media (max-width: 320px) {
    .btn-name {
      span {
        width: 85px;
      }
    }
  }
`;

const MenuButtonStyle = styled.button`
  position: fixed;
  z-index: 100;
`;

const Header = () => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const dispatch = useAppDispatch();
  const { user } = useSelector(userSelector);
  const token = getCookie('access_token');
  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    if (token) {
      dispatch(getUserProfile());
    }
  }, []);

  const handleLogout = async () => {
    const resLogout = await apiUser.logout();
    if (resLogout.status) {
      deleteCookie('access_token');
      router.reload();
    }
  };

  useEffect(() => {
    setIsOpen(false);
  }, [router]);

  return (
    <Fragment>
      <HeaderStyle
        className={`desktop:h-[80px] h-[56px] border-gray-700 border-b-[1px] flex desktop:flex-col justify-around items-center p-6`}
      >
        <div className="desktop:flex hidden">
          <Link href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}>
            <Image
              priority
              className="h-[40px] m-[10px]"
              src="/icons/Digiboxs-White.png"
              alt=""
              width={200}
              height={40}
            />
          </Link>
        </div>
        <div className="desktop:flex hidden" style={{ fontSize: '14px' }}>
          <Link
            className="flex items-center m-[10px] text-white text-m cursor-pointer"
            href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
          >
            สินค้า
          </Link>
          <Link
            className="flex items-center m-[10px] text-white text-m cursor-pointer"
            href={`/coupon`}
          >
            คูปอง
          </Link>
          <Link
            className="flex items-center m-[10px] text-white text-m cursor-pointer"
            href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
          >
            ผลงานตัวอย่าง
          </Link>
          <Link
            className="flex items-center m-[10px] text-white text-m cursor-pointer"
            href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
          >
            เกี่ยวกับเรา
          </Link>
          <Link
            className="flex items-center m-[10px] text-white text-m cursor-pointer"
            href={`${publicRuntimeConfig.LINK_REDIRECT_URL}blog`}
          >
            บทความ
          </Link>
        </div>
        <div className={`desktop:flex hidden items-center`}>
          <Image
            priority
            className="cursor-pointer mr-2"
            src="/icons/shopping_cart.png"
            alt=""
            onClick={() => router.push('/cart')}
            width={24}
            height={24}
          />
          {!isUndefined(user) && !isEmpty(user) ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="rounded-[20px] bg-gray-800 border-gray-700 border-[1px] hover:bg-# h-[40px] p-2 cursor-pointer mr-2 focus-visible:ring-0 focus-visible:ring-offset-0">
                  <div className="flex justify-between items-center">
                    <span className="mr-1 text-white">{user.name}</span>
                    {user.imageUrl ? (
                      <div
                        className={`w-[28px] h-[28px] rounded-full content-center`}
                      >
                        <Image
                          className="w-full h-full object-cover rounded-full shadow-md"
                          src={user.imageUrl}
                          alt="Selected"
                          width={80}
                          height={80}
                        />
                      </div>
                    ) : (
                      <Image
                        priority
                        src="/icons/account_circle.png"
                        alt=""
                        width={28}
                        height={28}
                      />
                    )}
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/my-orders');
                  }}
                >
                  ออเดอร์
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/claims-order');
                  }}
                >
                  ออเดอร์เคลม
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/my-coupon');
                  }}
                >
                  คูปองของฉัน
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/my-account');
                  }}
                >
                  ตั้งค่าบัญชี
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => handleLogout()}
                >
                  ออกจากระบบ
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              className="rounded-[20px] bg-gray-800 border-gray-700 border-[1px] hover:bg-# w-[120px] h-[40px] p-[15px] px-6 cursor-pointer mr-2"
              onClick={() => router.push('/login')}
            >
              <div className="flex justify-between items-center">
                <span className="mr-1 text-white">เข้าสู่ระบบ</span>
                <Image
                  priority
                  src="/icons/account_circle.png"
                  alt=""
                  width={32}
                  height={32}
                />
              </div>
            </Button>
          )}
          <Button
            className="rounded-[20px] bg-primary-main hover:bg-primary-light w-[120px] h-[40px] p-[15px] cursor-pointer"
            onClick={() => router.push('/customize')}
          >
            สั่งผลิต
          </Button>
        </div>
        <div
          className={`${isOpen ? '!hidden' : ''} desktop:hidden grid grid-cols-3 gap-4 relative w-full items-center justify-items-center`}
        >
          {/* This section is responsive action */}
          <Image
            priority
            className={`${isOpen ? 'hidden' : ''} ml-4 flex cursor-pointer mr-2`}
            src="/icons/shopping_cart.png"
            alt=""
            onClick={() => router.push('/cart')}
            width={24}
            height={24}
          />
          <Image
            priority
            className={`${isOpen ? 'hidden' : ''} m-[10px]`}
            src="/icons/digibox-icon-gray.png"
            alt=""
            width={28}
            height={32}
            onClick={() => router.push('/')}
          />
          <div></div>
        </div>

        {/* isOpen animate */}
        {/* Absolute btn */}
      </HeaderStyle>
      <MenuButtonStyle
        onClick={handleClick}
        className="desktop:hidden flex flex-col justify-center items-center absolute desktop:top-[16px] desktop:right-[16px] top-[25px] right-[25px] z-100"
      >
        <span
          className={`bg-primary-main block transition-all duration-300 ease-out 
              h-0.5 w-6 rounded-sm ${
                isOpen ? 'rotate-45 translate-y-0.5' : '-translate-y-0.5'
              }`}
        ></span>
        <span
          className={`bg-primary-main block transition-all duration-300 ease-out 
                h-0.5 w-6 rounded-sm ${
                  isOpen ? '-rotate-45' : 'translate-y-0.5'
                }`}
        ></span>
      </MenuButtonStyle>
      <MobileDrawerStyle $show={isOpen}>
        <div className="pt-[20px] pb-[50px] desktop:mb-[100px] flex flex-col items-center gap-[20px]">
          <Image
            priority
            className={`m-[10px]`}
            src="/icons/digibox-icon-gray.png"
            alt=""
            width={56}
            height={64}
            onClick={() => router.push('/')}
          />
          <Image
            priority
            className={`m-[10px]`}
            src="/icons/digiboxs-white-text.png"
            alt=""
            width={150}
            height={24}
            onClick={() => router.push('/')}
          />
          <span className="text-primary-main tracking-[10px]">MENU</span>

          <div
            className={`flex flex-col gap-2 items-center justify-between h-full pb-[120px]`}
          >
            <Link
              className={`${router.asPath === '/' ? '!text-primary-main' : ''} flex font-thin items-center text-white text-xl cursor-pointer`}
              href="/"
            >
              หน้าหลัก
            </Link>
            <Link
              className={`${router.asPath === '/#description-section' ? '!text-primary-main' : ''} flex font-thin items-center text-white text-xl cursor-pointer`}
              href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
            >
              สินค้า
            </Link>
            <Link
              className={`${router.asPath === '/#info-head-section' ? '!text-primary-main' : ''} flex font-thin items-center text-white text-xl cursor-pointer`}
              href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
            >
              คูปอง
            </Link>
            <Link
              className={`${router.asPath === '/#ai-section' ? '!text-primary-main' : ''} flex font-thin items-center text-white text-xl cursor-pointer`}
              href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
            >
              ผลงานตัวอย่าง
            </Link>
            <Link
              className={`${router.asPath === '/#way-order-group' ? '!text-primary-main' : ''} flex font-thin items-center text-white text-xl cursor-pointer`}
              href={`${publicRuntimeConfig.LINK_REDIRECT_URL}`}
            >
              เกี่ยวกับเรา
            </Link>
            <Link
              className={`flex font-thin items-center text-white text-xl cursor-pointer`}
              href={`${publicRuntimeConfig.LINK_REDIRECT_URL}blog`}
            >
              บทความ
            </Link>
          </div>
        </div>

        <div
          className={`${isOpen ? 'flex' : 'hidden'} btn-bottom fixed grid grid-cols-2 gap-4 bottom-0 border-t-[1px] border-gray-400 w-full p-6`}
        >
          <Button
            className="rounded-[20px] bg-primary-main hover:bg-primary-light h-[40px] p-[15px] cursor-pointer"
            onClick={() => router.push('/customize')}
          >
            สั่งผลิต
          </Button>
          {/* todo mobile login */}
          {user.name && user.name ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="rounded-[20px] bg-gray-800 border-gray-700 border-[1px] hover:bg-# h-[40px] p-2 cursor-pointer mr-2 focus-visible:ring-0 focus-visible:ring-offset-0">
                  <div className="btn-name flex justify-center items-center w-full relative">
                    <span className="mr-1 text-white">{user.name}</span>
                    {user.imageUrl ? (
                      <div
                        className={`w-[28px] h-[28px] rounded-full content-center absolute right-0`}
                      >
                        <Image
                          className="w-full h-full object-cover rounded-full shadow-md"
                          src={user.imageUrl}
                          alt="Selected"
                          width={80}
                          height={80}
                        />
                      </div>
                    ) : (
                      <Image
                        className="absolute right-0"
                        priority
                        src="/icons/account_circle.png"
                        alt=""
                        width={28}
                        height={28}
                      />
                    )}
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/my-orders');
                  }}
                >
                  ออเดอร์
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/claims-order');
                  }}
                >
                  ออเดอร์เคลม
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    router.push('/my-account');
                  }}
                >
                  ตั้งค่าบัญชี
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => handleLogout()}
                >
                  ออกจากระบบ
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              className="rounded-[20px] bg-gray-800 border-gray-700 border-[1px] hover:bg-# w-full h-[40px] p-[15px] px-6 cursor-pointer mr-2"
              onClick={() => router.push('/login')}
            >
              <div className="flex justify-between items-center">
                <span className="mr-1 text-white">เข้าสู่ระบบ</span>
                <Image
                  priority
                  src="/icons/account_circle.png"
                  alt=""
                  width={32}
                  height={32}
                />
              </div>
            </Button>
          )}
        </div>
      </MobileDrawerStyle>
    </Fragment>
  );
};

export default Header;
