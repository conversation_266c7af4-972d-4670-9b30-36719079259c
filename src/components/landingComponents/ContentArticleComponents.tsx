import React, { useState } from 'react';
import styled from 'styled-components';
import { aiImage, cardOrder } from '../../const/mockLandingPageData';
import { Button } from '@/components/ui/button';
import CardContentLandingPage from '@/components/landingComponents/CardContentLandingPage';
import { Swiper, SwiperSlide } from 'swiper/react';
import Image from 'next/image';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

import { Autoplay, Navigation, Pagination } from 'swiper/modules';

const SwiperStyled = styled(Swiper)`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column-reverse;
  clip-path: inset(0 round 24px);
  overflow: hidden;
  padding: 0 80px;
  @media only screen and (max-width: 430px) {
    padding: 0 24px;
  }
  .swiper-wrapper {
    height: 100%;
    margin-bottom: 80px;
  }
  .nav-wrapper {
    .swiper-button-prev {
      width: 56px;
      height: 56px;
      &.swiper-button-disabled {
        filter: opacity(0.35);
        opacity: 1;
      }
      &::after {
        display: none;
      }
      &:active {
        transform: scale(1.25);
      }
    }
    .swiper-button-next {
      width: 56px;
      height: 56px;
      &.swiper-button-disabled {
        filter: opacity(0.35);
        opacity: 1;
      }
      &::after {
        display: none;
      }
      &:active {
        transform: scale(1.25);
      }
    }
  }
  .swiper-pagination-bullet {
    background-color: #616161;
    opacity: 0.3;
    width: 6px;
    height: 6px;
  }
  .swiper-pagination-bullet-active {
    background-color: #ff4f00;
    opacity: 1;
  }
`;

const ContentArticleStyled = styled.div`
  display: flex;
  flex-direction: column;
  background-color: #212121;
  .ai-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    @media only screen and (max-width: 1024px) {
      margin-left: 30px;
      margin-right: 30px;
    }
    @media only screen and (max-width: 430px) {
      margin-left: 30px;
      margin-right: 30px;
    }
    .header-group {
      @media only screen and (max-width: 430px) {
        flex-direction: column;
      }
    }
    h4 {
      font-size: 80px;
      color: white;
      font-weight: 700;
      @media only screen and (max-width: 430px) {
        font-size: 50px;
      }
      @media only screen and (max-width: 430px) {
        font-size: 36px;
      }
    }
    .subtitle-text {
      color: white;
      font-size: 20px;
      font-weight: 400;
      @media only screen and (max-width: 430px) {
        font-size: 14px;
      }
    }
  }
  .image-ai-grid {
    margin: 100px 80px 0 80px;
    position: relative;
    @media only screen and (max-width: 430px) {
      margin: 100px 24px 0 24px;
      height: 560px;
      overflow: hidden;
    }
    .image-card {
      height: 260px;
      width: 260px;
      @media only screen and (max-width: 1024px) {
        height: auto;
      }
      @media only screen and (max-width: 430px) {
        height: 160px;
        width: 100%;
      }
    }
    .shadow-container {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      box-shadow: 0px -255px 90px -160px rgba(33, 33, 33, 1) inset;
    }
    img {
      border-radius: 24px;
    }
  }
  .way-order-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80px;
    @media only screen and (max-width: 820px) {
      text-align: center;
    }
    @media only screen and (max-width: 430px) {
      text-align: center;
    }
    .text-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      @media only screen and (max-width: 430px) {
        margin-left: 60px;
        margin-right: 60px;
      }
      .subtitle-text {
        color: white;
        font-size: 20px;
        @media only screen and (max-width: 430px) {
          font-size: 14px;
        }
      }
      h4 {
        color: white;
        font-size: 80px;
        font-weight: 700;
        @media only screen and (max-width: 820px) {
          font-size: 50px;
        }
        @media only screen and (max-width: 430px) {
          font-size: 36px;
        }
      }
    }
  }
`;

const ContentArticleComponents = () => {
  const [firstSlide, setFirstSlide] = useState(true);

  return (
    <ContentArticleStyled>
      <div className="ai-section">
        <div className="flex flex-col items-center">
          <h4 className="header-4 pt-[80px]" id="ai-section">
            FREE PACKAGING DESIGN BY
          </h4>
          <div className="header-group flex text-center">
            <h4 className="header-4 mr-2"></h4>
            <h4 className="header-4 !text-primary-main"> AI • INSPIRATION</h4>
          </div>
          <span className="subtitle-text desktop:mt-[40px] mt-[32px]">
            การออกแบบบรรจุภัณฑ์ด้วย AI • INSPIRATION
            ที่น่าทึ่งเหมือนจริงและไม่ซ้ำใคร
            เลือกภาพที่คุณสนใจเป็นตัวอย่างบรรจุภัณฑ์ในทุกสไตล์ของคุณ ฟรี!
          </span>
        </div>
        <div></div>
      </div>
      <div className="image-ai-grid grid desktop:grid-cols-6 tablet:grid-cols-2 grid-cols-2 desktop:gap-[40px] gap-[8px]">
        <div className="shadow-container" />
        {aiImage.map((dataImage, index) => (
          <Image
            priority
            key={index}
            className="image-card"
            src={`/images/ai-inspirations/${dataImage}`}
            alt=""
            width={`100`}
            height={`100`}
          />
        ))}
      </div>
      <Button className="self-center w-[120px] h-[40px] bg-primary-main hover:bg-primary-light rounded-full mt-[32px] mb-[300px]">
        ดูรูปทั้งหมด
      </Button>
      <div className="way-order-group">
        <div className="text-group">
          <span
            id="way-order-group"
            className="subtitle-text !text-primary-main pt-[80px]"
          >
            วิธีสั่งผลิตบรรจุภัณฑ์
          </span>
          <h4>THE EASIEST WAY TO</h4>
          <h4 className="!text-primary-main">ORDERS</h4>
          <span className="subtitle-text">
            สั่งผลิตบรรจุภัณฑ์ของคุณได้อย่างง่ายดาย เปลี่ยนจินตนาการของคุณ
          </span>
          <span className="subtitle-text">
            ให้กลายเป็นจริงด้วยเพียงไม่กี่สัมผัส
          </span>
        </div>
        <SwiperStyled
          className="w-full mt-[100px] mb-[170px] h-full"
          pagination={true}
          modules={[Navigation, Pagination, Autoplay]}
          navigation={{
            prevEl: '.swiper-button-prev',
            nextEl: '.swiper-button-next',
          }}
          onSwiper={(event: any) => {
            setFirstSlide(event.isBeginning);
          }}
          onSlideChange={(swiper: any) => {
            setFirstSlide(swiper.isBeginning);
          }}
          breakpoints={{
            0: {
              slidesPerView: 1.3,
              spaceBetween: 24,
            },
            431: {
              slidesPerView: 1.3,
              spaceBetween: 24,
            },
            821: {
              slidesPerView: 1.3,
              spaceBetween: 40,
            },
            1280: {
              slidesPerView: 3.1,
              spaceBetween: 40,
            },
          }}
        >
          <div className="nav-wrapper">
            <div
              className={`swiper-button-prev cursor-pointer ${firstSlide ? '!hidden' : ''}`}
            >
              <Image
                priority
                className="mr-2 rotate-180"
                src="/icons/arrow-next.png"
                alt=""
                width={56}
                height={56}
              />
            </div>
            <div className="swiper-button-next cursor-pointer">
              <Image
                priority
                className="mr-2"
                src="/icons/arrow-next.png"
                alt=""
                width={56}
                height={56}
              />
            </div>
          </div>
          {cardOrder.map((item: any, index: number) => {
            return (
              <SwiperSlide className="h-full" key={index}>
                <CardContentLandingPage key={index} data={item} index={index} />
              </SwiperSlide>
            );
          })}
        </SwiperStyled>
      </div>
    </ContentArticleStyled>
  );
};

export default ContentArticleComponents;
