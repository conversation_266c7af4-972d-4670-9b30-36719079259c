import React from 'react';
import styled from 'styled-components';
import { Button } from '@/components/ui/button';
import Marquee from 'react-fast-marquee';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import Link from 'next/link';

const HeaderArticleStyled = styled.div`
  display: flex;
  flex-direction: column;
  background-image: url('/background/bg-landing.png');
  padding-top: 86px;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .header-section {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    margin-bottom: 10%;
    padding-top: 80px;
    .text-group {
      z-index: 1;
      display: flex;
      flex-direction: column;
      margin: 80px;
      h1 {
        display: inline-block;
        color: transparent;
        width: fit-content;
        font-size: 120px;
        background-clip: text;
      }
      img {
        position: absolute;
        top: -5%;
        left: 8%;
        width: 1746px;
        height: 1484px;
      }
      .content-1 {
        color: white;
        font-size: 36px;
      }
      .subtitle-2 {
        color: white;
        font-weight: 700;
        font-size: 34px;
      }
      button {
        margin-top: 132px;
        font-size: 24px;
        height: 56px;
        width: 260px;
      }
      @media only screen and (max-width: 820px) {
        align-items: center;
        text-align: center;
        margin: 42px 30px 0;
        h1 {
          font-size: 80px;
        }
        .content-1 {
          margin-top: 40px;
          font-size: 20px;
        }
        .subtitle-2 {
          margin-top: 24px;
          font-size: 28px;
        }
        button {
          margin-top: 40px;
          font-size: 14px;
          height: 40px;
          width: 200px;
        }
        img {
          left: -35%;
          top: 110%;
          width: 659px;
          height: 617px;
          max-width: fit-content;
        }
      }
      @media only screen and (max-width: 430px) {
        align-items: center;
        text-align: center;
        margin: 42px 30px 0;
        h1 {
          font-size: 36px;
        }
        .content-1 {
          margin-top: 40px;
          font-size: 14px;
        }
        .subtitle-2 {
          margin-top: 24px;
          font-size: 20px;
        }
        button {
          margin-top: 40px;
          font-size: 14px;
          height: 40px;
          width: 200px;
        }
        img {
          left: -35%;
          top: 110%;
          width: 659px;
          height: 617px;
          max-width: fit-content;
        }
      }
    }
  }
  .img-bg-desktop {
    position: absolute;
    //background-image: url('/images/product-two.png');
    object-fit: contain;
    //width: 100%;
    //height: 1450px;
    object-position: bottom right;
    visibility: visible;
    background-repeat: no-repeat;
    background-position: 95% -71%;
    @media only screen and (max-width: 430px) {
      display: none;
    }
  }
  .img-bg-mobile {
    background-image: url('/images/product-two.png');
    @media only screen and (max-width: 430px) {
      display: block;
      height: 617px;
      background-size: 150%;
      background-repeat: no-repeat;
      background-position: -103px 26px;
    }
  }
  .description-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    justify-content: center;
    margin: 80px auto 0;
    width: 70%;
    .content-1 {
      color: #ffffff;
    }
    .content-2 {
      color: #ff4f00;
    }
    h2 {
      font-size: 80px;
      font-weight: 700;
      color: white;
      @media only screen and (max-width: 820px) {
        font-size: 50px;
      }
    }
    @media only screen and (max-width: 820px) {
      display: flex;
      flex-direction: column;
      justify-content: start;
      margin: 80px auto 80px;
      width: 100%;
      padding: 80px 80px 0 80px;
      height: auto;
      .content-1 {
        color: #ffffff;
      }
      .content-2 {
        color: #ff4f00;
      }
      h2 {
        font-size: 60px;
        font-weight: 700;
        color: white;
      }
    }
    @media only screen and (max-width: 430px) {
      width: 100%;
      margin-top: 50px;
      padding: 0 40px;
      h2 {
        font-size: 36px;
      }
      .content-1 {
        font-size: 14px;
      }
      .content-2 {
        font-size: 14px;
        font-weight: 700;
      }
      img {
        width: 200px;
        height: 40px;
      }
    }
  }
  .info-head-section {
    margin: 0 80px 0;
    padding-top: 80px;
    h4 {
      font-size: 80px;
      color: white;
      @media only screen and (max-width: 820px) {
        font-size: 50px;
      }
      @media only screen and (max-width: 430px) {
        font-size: 36px;
      }
    }
    .contain-box-img {
      position: relative;
      display: flex;
      align-items: end;
      justify-content: end;
      @media only screen and (max-width: 430px) {
        justify-content: start;
      }
      img {
        height: 68px;
        width: 180px;
        position: absolute;
        bottom: 40px;
        @media only screen and (max-width: 820px) {
          margin-top: 40px;
          height: 40px;
          width: 104px;
          position: absolute;
          bottom: 10px;
        }
        @media only screen and (max-width: 430px) {
          margin-top: 40px;
          height: 40px;
          width: 104px;
          position: relative;
          bottom: 0;
        }
      }
    }
    @media only screen and (max-width: 430px) {
      margin-left: 24px;
      margin-right: 24px;
      display: flex;
      flex-direction: column;
    }
    .header-group {
      display: flex;
      color: red;
      h4:first-child {
        font-size: 80px;
        font-weight: 700;
        height: 150px;
        line-height: 150px;
        @media only screen and (max-width: 820px) {
          font-size: 50px;
          height: 50px;
          line-height: 85px;
        }
        @media only screen and (max-width: 430px) {
          font-size: 36px;
          line-height: normal;
          height: 100%;
        }
      }
      h4:last-child {
        width: fit-content;
        font-size: 120px;
        font-weight: 700;
        height: 120px;
        line-height: 120px;
        @media only screen and (max-width: 820px) {
          font-size: 70px;
          height: 70px;
          line-height: 70px;
        }
        @media only screen and (max-width: 430px) {
          font-weight: bold;
          font-size: 36px;
          line-height: normal;
          height: 100%;
        }
      }
    }
  }
  .info-section {
    margin: 64px 24px;
    @media only screen and (max-width: 1024px) {
      margin: 24px;
      flex-direction: column;
    }
    @media only screen and (max-width: 430px) {
      margin: 64px 24px;
      flex-direction: column;
    }
    .card-group {
      display: flex;
      @media only screen and (max-width: 1024px) {
        flex-direction: column;
        margin: 24px;
      }
      .card-separate {
        display: flex;
        margin-left: 40px;
        width: 50%;
        margin-top: 0;
        @media only screen and (max-width: 1024px) {
          margin-left: 0px;
          margin-top: 24px;
          width: 100%;
        }
        @media only screen and (max-width: 430px) {
          margin-top: 24px;
        }
      }
      .card-wrapper {
        display: flex;
        margin-left: 40px;
        width: 100%;
        margin-top: 0;
        @media only screen and (max-width: 1024px) {
          margin-left: 0px;
          margin-top: 24px;
        }
        @media only screen and (max-width: 430px) {
          margin-top: 24px;
          height: fit-content;
        }
      }
      .card {
        width: 100%;
        background-color: #ff4f00;
        height: auto;
        border-radius: 24px;
        border: 0;
        padding: 40px;
        @media only screen and (max-width: 1024px) {
          height: auto;
          width: 100%;
        }
        @media only screen and (max-width: 430px) {
          height: fit-content;
          width: 100%;
          padding: 24px;
        }

        span {
          display: flex;
          align-items: center;
          font-size: 36px;
          color: white;
          @media only screen and (max-width: 1024px) {
            font-size: 32px;
          }
          @media only screen and (max-width: 820px) {
            font-size: 28px;
          }
          @media only screen and (max-width: 430px) {
            font-size: 20px;
          }
        }
        button {
          font-size: 20px;
          margin-left: 16px;
          @media only screen and (max-width: 430px) {
            margin-top: 16px;
            font-size: 14px;
            margin-left: 0;
            height: 24px;

            img {
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
  .marquee-container {
    .loop-marquee {
      margin-top: 400px;
      @media only screen and (max-width: 820px) {
        margin-top: 400px;
      }
      @media only screen and (max-width: 430px) {
        margin-top: 100px;
      }
    }
  }
`;

const HeadArticleComponents = () => {
  return (
    <HeaderArticleStyled>
      <div className="header-section ">
        <Image
          priority
          className="img-bg-desktop"
          fill
          alt=""
          src="/images/product-two.png"
        />
        <div className="text-group">
          <h1 className="bg-gradient-to-l from-white via-gray-500 to-white">
            PRODUCE
          </h1>
          <h1 className="bg-gradient-to-l from-white via-gray-500 to-white">
            YOUR PACKAGING
          </h1>
          <h1 className="bg-gradient-to-l from-white via-gray-500 to-white">
            FROM THE FACTORY
          </h1>
          <span className="content-1">
            เปลี่ยนจินตนาการของคุณ ให้กลายเป็นจริง
          </span>
          <span className="subtitle-2">...ด้วยเพียงไม่กี่สัมผัส</span>
          <Link href="/customize"></Link>
          <Button className="mt-[132px] w-[260px] h-[56px] bg-primary-main hover:bg-primary-light text-2xl">
            เริ่มต้นสั่งผลิต
          </Button>
        </div>
      </div>
      {/* <Image priority src="/images/product-two.png" alt="" /> */}
      <div className="img-bg-mobile" />
      <div id="description-section" className="description-section">
        <span className="content-2 text-primary-main text-lg font-bold">
          เกี่ยวกับเรา
        </span>
        <h2>OUR FACTORY</h2>
        <span className="content-1 text-white text-lg">
          Our experiences, and products.
        </span>
        <span className="content-1 text-white mt-[64px] text-center whitespace-break-spaces">
          เราเป็นโรงพิมพ์ที่เชี่ยวชาญในบริการผลิตบรรจุภัณฑ์ที่หลายหลาย เช่น
          บรรจุภัณฑ์กล่องกระดาษ ถุงกระดาษ ซองฟอยล์ และ สื่อสิ่งพิมพ์แบบครบวงจร
          ที่คุณสามารถกำหนดหรือปรับแต่งบรรจุภัณฑ์ของคุณได้เอง
          ไม่ว่าจะเป็นบรรจุภัณฑ์ขนาดเล็ก หรือบรรจุภัณฑ์ขนาดใหญ่ พร้อมฟีเจอร์
          การออกแบบบรรจุภัณฑ์ด้วยเทคโนโลยี AI Generate พร้อมนำไปผลิตได้จริง
          หรือจะใช้รูปภาพเป็น Reference
          เพื่อนำไปสู่ขั้นตอนในการออกแบบอาร์ตเวิร์คที่รวดเร็วและตรงกับความต้องการของลูกค้ามากที่สุด
        </span>
        <Image
          priority
          className="mt-[64px]"
          src="/icons/factory-icon.png"
          alt=""
          width={292}
          height={64}
        />
      </div>
      <div id="info-head-section" className="info-head-section">
        <div className="flex flex-col">
          <h4>เราคือโรงพิมพ์</h4>
          <div className="header-group">
            <h4>ผลิต</h4>
            <h4>บรรจุภัณฑ์</h4>
          </div>
        </div>
        <div className="contain-box-img">
          <Image
            priority
            width={100}
            height={100}
            className=""
            src="/icons/icon-bundle.png"
            alt=""
          />
        </div>
      </div>
      <div className="info-section mt-[100px] mx-[80px]">
        <div className="card-group">
          <Card className="card">
            <div>
              <span>
                <Image
                  priority
                  className="desktop:mr-4 mr-2"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                มาตรฐานคุณภาพ
              </span>
              <span>
                ใส่ใจในทุกขั้นตอนการดำเนินการ
                <Image
                  priority
                  className="desktop:ml-4 ml-2 h-[24px] w-[24px]"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
              </span>
              <span className="flex">
                คุณภาพวัสดุ การผลิต
                <Image
                  priority
                  className="ml-4"
                  src="/icons/arrow-white.png"
                  alt=""
                  width={100}
                  height={10}
                />
              </span>
              <span>จนไปถึงคุณภาพการจัดส่ง</span>
              <div className="flex desktop:items-center items-start desktop:flex-row flex-col">
                <span>สินค้าถึงคุณ ที่ตรงตาม</span>
                <Button className="h-[40px] p-2 text-primary-main bg-white rounded-full flex justify-between">
                  <Image
                    priority
                    className="mr-2"
                    src="/icons/Primary-icon-color-24.png"
                    alt=""
                    height={24}
                    width={24}
                  />
                  มาตรฐานระดับสูง
                </Button>
              </div>
            </div>
          </Card>
          <div className="card-wrapper">
            <Card className="card bg-primary-main w-1/3 rounded-[24px] desktop:mr-[40px] mr-[16px] border-0 bg-[url('/images/info-1.png')] bg-cover !h-auto"></Card>
            <Card className="card bg-primary-main w-2/3 rounded-[24px] border-0 bg-[url('/images/info-2.png')] bg-cover flex justify-end items-end">
              <div className="flex flex-col items-end">
                <Image
                  priority
                  className="desktop:block hidden mr-2 mb-6"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                <span>โรงพิมพ์</span>
                <span>บรรจุภัณฑ์</span>
              </div>
            </Card>
          </div>
        </div>
        <div className="card-group flex flex-1 desktop:mt-[40px] mt-[16px]">
          <Card className="card bg-primary-main rounded-[24px] border-0 bg-[url('/images/info-3.png')] bg-cover">
            <div className="flex flex-col items-end">
              <Image
                priority
                className="mr-2 mb-6"
                src="/icons/Primary-icon-white-24.png"
                alt=""
                width={24}
                height={24}
              />
              <span>บริการแบบ</span>
              <span>ครบวงจร</span>
            </div>
          </Card>
          <div className="card-wrapper">
            <Card className="card bg-primary-main w-full rounded-[24px] border-0">
              <span>
                <Image
                  priority
                  className="h-[24px] w-[24px] mr-4"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                การจัดส่ง
              </span>
              <span>
                หากคุณจะต้องการบรรจุภัณฑ์
                <Image
                  priority
                  className="mx-4"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                บางส่วนแบบเร่งด่วน
              </span>
              <span className="flex">
                หรือจัดส่งไปยังคลังสินค้าได้ทั่วโลก
                <Image
                  priority
                  className="ml-4"
                  src="/icons/arrow-white.png"
                  alt=""
                  width={100}
                  height={10}
                />
              </span>
              <span>ควบคุม สถานที่ วันจัดส่ง</span>
              <div className="flex items-center">
                <span>หรือวิธีการจัดส่งตามที่คุณต้องการ</span>
              </div>
            </Card>
          </div>
        </div>
        <div className="card-group flex flex-1 desktop:mt-[40px] mt-[16px]">
          <Card className="card bg-primary-main rounded-[24px]  border-0">
            <div>
              <span>
                <Image
                  priority
                  className="mr-4"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                ปรึกษาให้คำแนะนำ
              </span>
              <span>
                เรายินดีให้บริการที่
                <Image
                  priority
                  className="mx-4"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  width={24}
                  height={24}
                />
                ครอบคลุม
              </span>
              <span className="flex">
                ให้คำแนะนำ อย่างเชี่ยวชาญ
                <Image
                  priority
                  className="ml-4"
                  src="/icons/arrow-white.png"
                  alt=""
                  width={100}
                  height={10}
                />
              </span>
              <span>และเหมาะกับผลิตภัณฑ์ </span>
              <div className="flex items-center">
                <span>ของคุณ.</span>
              </div>
            </div>
          </Card>
          <div className="card-wrapper">
            <Card className="card bg-primary-main border-0 bg-[url('/images/info-4.png')] bg-cover flex justify-end items-end">
              <div className="flex flex-col items-end">
                <Image
                  priority
                  className="mr-2 mb-6"
                  src="/icons/Primary-icon-white-24.png"
                  alt=""
                  height={24}
                  width={24}
                />
                <span>สะดวก</span>
                <span>รวดเร็ว</span>
              </div>
            </Card>
          </div>
        </div>
      </div>
      {/* Marquee section */}
      <div className="marquee-container h-[490px]">
        <Marquee
          className="loop-marquee border-y-[1px] border-y-gray-800 p-6 bg-bg"
          autoFill={true}
          pauseOnHover={true}
          speed={100}
        >
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            PACKAGING
          </span>
          <Image
            priority
            src="/icons/Reuse.png"
            alt=""
            width={24}
            height={24}
          />
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            PRINTING
          </span>
          <Image priority src="/icons/Box.png" alt="" width={24} height={24} />
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            COMMERCIAL PRINTING
          </span>
          <Image priority src="/icons/e.png" alt="" width={24} height={24} />
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            PACKAGING
          </span>
          <Image priority width={24} height={24} src="/icons/ce.png" alt="" />
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            PRINTING
          </span>
          <Image
            priority
            width={24}
            height={24}
            src="/icons/Recycle.png "
            alt=""
          />
          <span className="text-gray-800 text-4xl mr-[48px] ml-[48px]">
            COMMERCIAL PRINTING
          </span>
          <Image priority width={24} height={24} src="/icons/Fire.png" alt="" />
        </Marquee>
      </div>
    </HeaderArticleStyled>
  );
};

export default HeadArticleComponents;
