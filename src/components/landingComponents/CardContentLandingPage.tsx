import React from 'react';
import styled from 'styled-components';
import { Card } from '@/components/ui/card';
import Image from 'next/image';

const ContentArticleStyled = styled.div`
  height: 556px;
  width: 100%;
  @media only screen and (max-width: 820px) {
    height: 450px;
  }
  @media only screen and (max-width: 430px) {
    height: 360px;
  }
  .card-group {
    background-color: #424242;
    border-color: #616161;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    border-radius: 24px;
    padding: 40px;
    position: relative;
    @media only screen and (max-width: 430px) {
      padding: 24px;
    }
    .header-text {
      color: #ff4f00;
      font-size: 14px;
      font-weight: 700;
      @media only screen and (max-width: 430px) {
        font-size: 12px;
      }
    }
    .subheader-text {
      color: white;
      font-size: 36px;
      font-weight: 700;
      @media only screen and (max-width: 430px) {
        font-size: 20px;
      }
    }
    .content-text {
      color: white;
      font-size: 18px;
      font-weight: 400;
      @media only screen and (max-width: 430px) {
        font-size: 14px;
      }
    }
    img {
      position: absolute;
      bottom: 40px;
      right: 40px;
      width: 100px;
      height: 100px;
      @media only screen and (max-width: 820px) {
        width: 60px;
        height: 60px;
      }
      @media only screen and (max-width: 430px) {
        width: 48px;
        height: 48px;
      }
    }
  }
`;

interface CardContentLandingProps {
  data: any;
  index: number;
}

const CardContentLandingPage = (props: CardContentLandingProps) => {
  const { data, index } = props;
  return (
    <ContentArticleStyled>
      <Card className="card-group">
        <span className="header-text">{`ขั้นตอนที่ ${index + 1}`}</span>
        <span className="subheader-text">{data.title}</span>
        <span className="content-text ">{data.detail}</span>
        <Image priority src="/icons/tune.png" alt="" width={100} height={100} />
      </Card>
    </ContentArticleStyled>
  );
};

export default CardContentLandingPage;
