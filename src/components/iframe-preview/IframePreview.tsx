import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';

type PreviewProps = {
  modelId: number;
  modelType: '3d' | 'dieline';
  width: number;
  height: number;
  length: number;
  material: {
    inside: MaterialType;
    outside: MaterialType;
  };
  onInfo: (info: any) => void;
};

type MaterialType = 'white' | 'kraft' | 'gray';

const IframePreview = ({
  modelId,
  modelType,
  width,
  height,
  length,
  material,
  onInfo,
}: PreviewProps) => {
  const router = useRouter();
  const iframeRef: any = useRef(null);

  useEffect(() => {
    if (iframeRef.current) {
      // eslint-disable-next-line no-self-assign
      iframeRef.current.src = iframeRef.current.src;
    }
  }, [router.query, modelId, modelType, width, height, length, material]);

  // PostMessage API listener for receiving data from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log(
        'Received message from origin:',
        event.origin,
        'Data:',
        event.data,
      );

      // Check for authorized origin
      const isAuthorizedOrigin = event.origin.includes('app-dev.honconnect.co');

      if (!isAuthorizedOrigin) {
        console.warn(
          'Received message from unauthorized origin:',
          event.origin,
        );
        return;
      }

      // Handle the received data
      console.log('Processing message from iframe:', event.data);

      // Call the onInfo callback with the received data
      if (onInfo && typeof onInfo === 'function') {
        onInfo(event.data);
      } else {
        console.warn('onInfo callback is not available or not a function');
      }
    };

    // Add event listener for messages
    window.addEventListener('message', handleMessage);
    console.log('PostMessage listener added');

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('message', handleMessage);
      console.log('PostMessage listener removed');
    };
  }, [onInfo]);

  return (
    <iframe
      ref={iframeRef}
      src={`https://app-dev.honconnect.co/preview?modelId=${modelId}&modelType=${modelType}&width=${width}&height=${height}&length=${length}&inside=${material.inside}&outside=${material.outside}`}
      title="Preview"
      style={{
        border: 'none',
        width: '100%',
        height: '100%',
      }}
    />
  );
};

export default IframePreview;
