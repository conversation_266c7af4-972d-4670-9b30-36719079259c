const Svg3dIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
    >
      <mask
        id="mask0_2335_13007"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="17"
        height="16"
      >
        <rect x="0.5" width="16" height="16" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_2335_13007)">
        <path
          d="M7.89758 12.8678L4.60258 10.964C4.4118 10.8528 4.26369 10.7062 4.15825 10.5242C4.0528 10.3422 4.00008 10.1417 4.00008 9.92284V6.11517C4.00008 5.89639 4.0528 5.696 4.15825 5.514C4.26369 5.332 4.4118 5.18539 4.60258 5.07417L7.89758 3.17034C8.08869 3.061 8.28986 3.00634 8.50108 3.00634C8.71242 3.00634 8.91292 3.061 9.10258 3.17034L12.3976 5.07417C12.5884 5.18539 12.7365 5.332 12.8419 5.514C12.9474 5.696 13.0001 5.89639 13.0001 6.11517V9.92284C13.0001 10.1417 12.9474 10.3422 12.8419 10.5242C12.7365 10.7062 12.5884 10.8528 12.3976 10.964L9.10258 12.8678C8.91147 12.9772 8.7103 13.0318 8.49908 13.0318C8.28775 13.0318 8.08725 12.9772 7.89758 12.8678ZM8.00008 11.7768V8.29984L5.00008 6.57667V9.92284C5.00008 9.95706 5.00864 9.98912 5.02575 10.019C5.04286 10.049 5.06847 10.0747 5.10258 10.096L8.00008 11.7768ZM9.00008 11.7768L11.8976 10.096C11.9317 10.0747 11.9573 10.049 11.9744 10.019C11.9915 9.98912 12.0001 9.95706 12.0001 9.92284V6.57667L9.00008 8.29984V11.7768ZM2.66658 5.1665C2.5248 5.1665 2.40608 5.11856 2.31041 5.02267C2.21464 4.92689 2.16675 4.80817 2.16675 4.6665V2.87167C2.16675 2.54023 2.28475 2.2565 2.52075 2.0205C2.75675 1.7845 3.04047 1.6665 3.37191 1.6665H5.16675C5.30841 1.6665 5.42714 1.71445 5.52291 1.81034C5.6188 1.90623 5.66675 2.025 5.66675 2.16667C5.66675 2.30845 5.6188 2.42717 5.52291 2.52284C5.42714 2.61862 5.30841 2.6665 5.16675 2.6665H3.37191C3.31203 2.6665 3.26286 2.68573 3.22441 2.72417C3.18597 2.76262 3.16675 2.81178 3.16675 2.87167V4.6665C3.16675 4.80817 3.1188 4.92689 3.02291 5.02267C2.92703 5.11856 2.80825 5.1665 2.66658 5.1665ZM3.37191 14.3332C3.04047 14.3332 2.75675 14.2152 2.52075 13.9792C2.28475 13.7432 2.16675 13.4594 2.16675 13.128V11.3332C2.16675 11.1915 2.21469 11.0728 2.31058 10.977C2.40647 10.8811 2.52525 10.8332 2.66691 10.8332C2.80869 10.8332 2.92741 10.8811 3.02308 10.977C3.11886 11.0728 3.16675 11.1915 3.16675 11.3332V13.128C3.16675 13.1879 3.18597 13.2371 3.22441 13.2755C3.26286 13.3139 3.31203 13.3332 3.37191 13.3332H5.16675C5.30841 13.3332 5.42714 13.3811 5.52291 13.477C5.6188 13.5729 5.66675 13.6917 5.66675 13.8333C5.66675 13.9751 5.6188 14.0938 5.52291 14.1895C5.42714 14.2853 5.30841 14.3332 5.16675 14.3332H3.37191ZM13.6282 14.3332H11.8334C11.6917 14.3332 11.573 14.2852 11.4772 14.1893C11.3814 14.0934 11.3334 13.9747 11.3334 13.833C11.3334 13.6912 11.3814 13.5725 11.4772 13.4768C11.573 13.3811 11.6917 13.3332 11.8334 13.3332H13.6282C13.6881 13.3332 13.7373 13.3139 13.7757 13.2755C13.8142 13.2371 13.8334 13.1879 13.8334 13.128V11.3332C13.8334 11.1915 13.8814 11.0728 13.9772 10.977C14.0731 10.8811 14.1919 10.8332 14.3336 10.8332C14.4754 10.8332 14.5941 10.8811 14.6897 10.977C14.7855 11.0728 14.8334 11.1915 14.8334 11.3332V13.128C14.8334 13.4594 14.7154 13.7432 14.4794 13.9792C14.2434 14.2152 13.9597 14.3332 13.6282 14.3332ZM13.8334 4.6665V2.87167C13.8334 2.81178 13.8142 2.76262 13.7757 2.72417C13.7373 2.68573 13.6881 2.6665 13.6282 2.6665H11.8334C11.6917 2.6665 11.573 2.61856 11.4772 2.52267C11.3814 2.42678 11.3334 2.308 11.3334 2.16634C11.3334 2.02456 11.3814 1.90584 11.4772 1.81017C11.573 1.71439 11.6917 1.6665 11.8334 1.6665H13.6282C13.9597 1.6665 14.2434 1.7845 14.4794 2.0205C14.7154 2.2565 14.8334 2.54023 14.8334 2.87167V4.6665C14.8334 4.80817 14.7855 4.92689 14.6896 5.02267C14.5937 5.11856 14.4749 5.1665 14.3332 5.1665C14.1915 5.1665 14.0727 5.11856 13.9771 5.02267C13.8813 4.92689 13.8334 4.80817 13.8334 4.6665ZM8.50008 7.43834L11.4796 5.70234L8.60258 4.04467C8.56847 4.02334 8.5343 4.01267 8.50008 4.01267C8.46586 4.01267 8.43169 4.02334 8.39758 4.04467L5.52058 5.70234L8.50008 7.43834Z"
          fill="white"
        />
      </g>
    </svg>
  );
};

export default Svg3dIcon;
