const SvgDielineIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <mask
        id="mask0_2335_13012"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="16"
        height="16"
      >
        <rect width="16" height="16" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_2335_13012)">
        <path
          d="M3.59001 14.0768C3.12701 14.0768 2.73345 13.9148 2.40934 13.5908C2.08534 13.2667 1.92334 12.8731 1.92334 12.4101C1.92334 12.0367 2.03334 11.7061 2.25334 11.4184C2.47345 11.1309 2.75234 10.932 3.09001 10.8218V5.1781C2.75234 5.06787 2.47345 4.86898 2.25334 4.58143C2.03334 4.29376 1.92334 3.96321 1.92334 3.58976C1.92334 3.12676 2.08534 2.73321 2.40934 2.4091C2.73345 2.0851 3.12701 1.9231 3.59001 1.9231C3.96345 1.9231 4.29401 2.0331 4.58167 2.2531C4.86923 2.47321 5.06812 2.7521 5.17834 3.08976H10.822C10.9255 2.7521 11.1215 2.47321 11.41 2.2531C11.6986 2.0331 12.032 1.9231 12.4103 1.9231C12.8733 1.9231 13.2669 2.0851 13.591 2.4091C13.915 2.73321 14.077 3.12676 14.077 3.58976C14.077 3.9681 13.967 4.30154 13.747 4.5901C13.5269 4.87865 13.248 5.07465 12.9103 5.1781V10.8218C13.248 10.932 13.5269 11.1309 13.747 11.4184C13.967 11.7061 14.077 12.0367 14.077 12.4101C14.077 12.8731 13.915 13.2667 13.591 13.5908C13.2669 13.9148 12.8733 14.0768 12.4103 14.0768C12.0369 14.0768 11.7063 13.9668 11.4187 13.7468C11.1311 13.5267 10.9322 13.2478 10.822 12.9101H5.17834C5.06812 13.2478 4.86923 13.5267 4.58167 13.7468C4.29401 13.9668 3.96345 14.0768 3.59001 14.0768ZM3.59001 4.25643C3.7789 4.25643 3.93723 4.19254 4.06501 4.06476C4.19278 3.93698 4.25667 3.77865 4.25667 3.58976C4.25667 3.40087 4.19278 3.24254 4.06501 3.11476C3.93723 2.98698 3.7789 2.9231 3.59001 2.9231C3.40112 2.9231 3.24278 2.98698 3.11501 3.11476C2.98723 3.24254 2.92334 3.40087 2.92334 3.58976C2.92334 3.77865 2.98723 3.93698 3.11501 4.06476C3.24278 4.19254 3.40112 4.25643 3.59001 4.25643ZM12.4103 4.25643C12.5992 4.25643 12.7576 4.19254 12.8853 4.06476C13.0131 3.93698 13.077 3.77865 13.077 3.58976C13.077 3.40087 13.0131 3.24254 12.8853 3.11476C12.7576 2.98698 12.5992 2.9231 12.4103 2.9231C12.2215 2.9231 12.0631 2.98698 11.9353 3.11476C11.8076 3.24254 11.7437 3.40087 11.7437 3.58976C11.7437 3.77865 11.8076 3.93698 11.9353 4.06476C12.0631 4.19254 12.2215 4.25643 12.4103 4.25643ZM5.17834 11.9101H10.822C10.9049 11.6469 11.0412 11.4204 11.231 11.2308C11.4207 11.041 11.6471 10.9047 11.9103 10.8218V5.1781C11.6471 5.09954 11.4196 4.96432 11.2277 4.77243C11.0358 4.58054 10.9006 4.35298 10.822 4.08976H5.17834C5.09545 4.35298 4.95912 4.57943 4.76934 4.7691C4.57967 4.95887 4.35323 5.09521 4.09001 5.1781V10.8218C4.35323 10.9047 4.57967 11.041 4.76934 11.2308C4.95912 11.4204 5.09545 11.6469 5.17834 11.9101ZM12.4103 13.0768C12.5992 13.0768 12.7576 13.0129 12.8853 12.8851C13.0131 12.7573 13.077 12.599 13.077 12.4101C13.077 12.2212 13.0131 12.0629 12.8853 11.9351C12.7576 11.8073 12.5992 11.7434 12.4103 11.7434C12.2215 11.7434 12.0631 11.8073 11.9353 11.9351C11.8076 12.0629 11.7437 12.2212 11.7437 12.4101C11.7437 12.599 11.8076 12.7573 11.9353 12.8851C12.0631 13.0129 12.2215 13.0768 12.4103 13.0768ZM3.59001 13.0768C3.7789 13.0768 3.93723 13.0129 4.06501 12.8851C4.19278 12.7573 4.25667 12.599 4.25667 12.4101C4.25667 12.2212 4.19278 12.0629 4.06501 11.9351C3.93723 11.8073 3.7789 11.7434 3.59001 11.7434C3.40112 11.7434 3.24278 11.8073 3.11501 11.9351C2.98723 12.0629 2.92334 12.2212 2.92334 12.4101C2.92334 12.599 2.98723 12.7573 3.11501 12.8851C3.24278 13.0129 3.40112 13.0768 3.59001 13.0768Z"
          fill="#616161"
        />
      </g>
    </svg>
  );
};

export default SvgDielineIcon;
