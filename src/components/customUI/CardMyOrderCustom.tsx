import React, { Fragment, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import styled from 'styled-components';
import Image from 'next/image';
import dayjs from '@/utils/dayjs';
import { numberWithCommas } from '@/utils/numberFormat';
import { useRouter } from 'next/router';
import CancelOrderModal from '@/components/customUI/modal/CancelOrderModal';
import apiOrder from '@/services/order';
import { toast } from 'sonner';
import ReQuotationModal from '@/components/customUI/modal/ReQuotationModal';
import apiRevision from '@/services/revision';
import { RevisionType } from '@/types/revision';
import { dataResult, priceDiscountVat } from '@/utils/discount';

const MyOrdersStyled = styled(Card)`
  margin-bottom: 24px;
  box-shadow: 0px 0px 16px 0px rgba(33, 33, 33, 0.05);
  @media only screen and (max-width: 430px) {
    margin-bottom: 24px;
    border-radius: 0;
  }
  .card-head-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #dbe2e5;
    padding: 24px;
    @media only screen and (max-width: 430px) {
      flex-direction: column;
    }
    .text-wrapper-head {
      @media only screen and (max-width: 430px) {
        margin-bottom: 16px;
      }
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
      }
      .content {
        font-size: 14px;
        font-weight: 400;
        color: #bdbdbd;
      }
    }
    .action-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      span {
        font-size: 12px;
        margin: 0 8px;
        cursor: pointer;
      }
      button {
        font-size: 12px;
        background-color: white;
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        margin: 0 8px;
      }
      button.cancel {
        color: red;
        @media only screen and (max-width: 430px) {
          margin: 0;
          margin-left: 8px;
        }
      }
      button.re-qoutation {
        color: #212121;
      }
      button.detail {
        background-color: white;
        color: #212121;
        @media only screen and (max-width: 430px) {
          flex: 1;
          margin: 0;
        }
      }
    }
  }
  .card-content-wrapper {
    padding: 24px;
    .card-item-wrapper {
      display: flex;
      flex-direction: row;
      .text-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: calc(100% - 80px);
        padding-left: 8px;
        @media only screen and (max-width: 430px) {
          padding-left: 8px;
        }
        .content-bold {
          font-size: 14px;
          font-weight: 700;
        }
        .content {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
  .card-footer-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 24px;
    border-top: 1px solid #dbe2e5;
    .text-wrapper {
      .content-bold {
        font-size: 14px;
        font-weight: 700;
      }
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
      }
    }
  }
`;

interface CardMyOrderCustomProps {
  data: any;
  selectKey: string;
  fetchData: () => void;
}

const CardMyOrderCustom = (props: CardMyOrderCustomProps) => {
  const { data, selectKey, fetchData } = props;
  const [totalPrice, setTotalPrice] = useState<number>();
  const router = useRouter();
  const checkKeyWaiting = (key: string) =>
    key === 'waitingQuotation' || key === 'waitingPayment';
  const checkKeyCancel = (key: string) => key === 'canceled';
  const [openModalCancel, setOpenModalCancel] = useState(false);
  const [openModalQuotation, setOpenModalQuotation] = useState(false);

  const handleSubmitModalCancel = async (orderId: string, value: any) => {
    if (checkKeyCancel(selectKey)) {
      const resDeleteOrder = await apiOrder.deleteOrderById(orderId);
      if (resDeleteOrder.status) {
        fetchData();
        toast.success('ลบรายการสำเร็จ');
        setOpenModalCancel(false);
      }
    } else {
      const resCancelOrder = await apiOrder.cancelOrderById(orderId, value);
      if (resCancelOrder.status) {
        fetchData();
        toast.success('ยกเลิกรายการสำเร็จ');
        setOpenModalCancel(false);
      }
    }
  };

  const handleSubmitModalQuotation = async (orderId: string) => {
    const resQuotationOrder = await apiOrder.reQuotationById(orderId);
    if (resQuotationOrder.status) {
      fetchData();
      toast.success('ทำการเสนอราคาใหม่สำเร็จ');
      setOpenModalQuotation(false);
    }
  };
  useEffect(() => {
    const total =
      data.orderItems &&
      data.orderItems.reduce(function (sum: number, obj: any) {
        return sum + obj.item.totalPrice;
      }, 0);
    const sumVat = Number(priceDiscountVat(total, data.discounts));
    const result = dataResult(data.discounts, total, data.shippingCost, sumVat);
    setTotalPrice(Number(result?.sumTotalPrice));
  }, [data]);

  const renderActionButtons = (id: string, data: RevisionType) => {
    if (checkKeyCancel(selectKey)) {
      return (
        <Fragment>
          {data.isRevision ? (
            <span
              className="underline"
              onClick={() =>
                router.push(
                  `/my-orders/re-quotation?revisionId=${data.revisions[data.revisions.length - 1].id}&orderNumber=${data.orderNumber}`,
                )
              }
            >
              ดูรายการเสนอราคา
            </span>
          ) : (
            <span
              className="underline"
              onClick={() =>
                apiRevision.createRevision(id).then((response: any) => {
                  if (response.status) {
                    router.push(
                      `/my-orders/re-quotation?revisionId=${response.data}&orderNumber=${data.orderNumber}`,
                    );
                  } else {
                    toast.error(response.error);
                  }
                })
              }
            >
              เสนอราคาอีกครั้ง
            </span>
          )}
          <Button
            className="detail"
            onClick={() => {
              router.push(`/my-orders/order-detail/${id}`);
            }}
          >
            ดูรายละเอียด
          </Button>
          <Button className="cancel" onClick={() => setOpenModalCancel(true)}>
            <Image
              className="rounded-xl"
              priority
              src="/icons/delete.svg"
              alt=""
              width={24}
              height={24}
            />
          </Button>
        </Fragment>
      );
    }
    return (
      <>
        <Button
          className="detail"
          onClick={() => {
            router.push(`/my-orders/order-detail/${id}`);
          }}
        >
          ดูรายละเอียด
        </Button>
        {checkKeyWaiting(selectKey) && (
          <Button
            className="cancel p-2"
            onClick={() => setOpenModalCancel(true)}
          >
            <Image
              className="rounded-xl"
              priority
              src="/icons/delete.svg"
              alt=""
              width={24}
              height={24}
            />
          </Button>
        )}
      </>
    );
  };

  return (
    <MyOrdersStyled className="card-wrapper">
      <div className="card-head-wrapper ">
        <div className="text-wrapper-head flex flex-col">
          <span className="subtitle-bold">{data.orderNumber}</span>
          <span className="content">
            วันที่สั่งซื้อ: {dayjs(data.createdDate).format('D MMM YYYY, H:mm')}
          </span>
        </div>
        <div className="action-wrapper">
          {renderActionButtons(data.id, data)}
        </div>
      </div>
      <CancelOrderModal
        open={openModalCancel}
        handleClose={(value: any) => setOpenModalCancel(value)}
        handleSubmitModal={(value: any) =>
          handleSubmitModalCancel(data.id, value)
        }
        status={selectKey}
        data={data}
      />
      <ReQuotationModal
        open={openModalQuotation}
        handleClose={(value: any) => setOpenModalQuotation(value)}
        handleSubmitModal={() => handleSubmitModalQuotation(data.id)}
        data={data}
      />
      {data.orderItems &&
        data.orderItems.map((item: any, index: number) => (
          <div key={index} className="card-content-wrapper">
            <div key={index} className="card-item-wrapper">
              <div className="w-[80px] h-[80px]">
                <Image
                  className="rounded-xl w-full h-full"
                  priority
                  src={item.item.model.imageUrl}
                  alt=""
                  width={92}
                  height={92}
                />
              </div>
              <div className="text-wrapper flex flex-col">
                <div className="flex flex-col">
                  <span className="content-bold w-full flex justify-between">
                    {item.item.model.name}
                    <span className="content-bold">
                      {numberWithCommas(item.item.totalPrice)}
                    </span>
                  </span>
                  <span className="content tablet:w-full truncate w-1/2">
                    {`ขนาด: ${item.item.width} x ${item.item.length} x 
                    ${item.item.height} cm วัสดุ: ${item.item.material.name} •
                    ${item.item.material.gram}
                      แกรม`}
                  </span>
                </div>
                <div className="flex flex-row">
                  <span className="content-bold">
                    {`x ${numberWithCommas(item.item.amount, 0)}`}
                  </span>
                  <span className="content ml-1">
                    {`(${numberWithCommas(item.item.unitPrice)} /ชิ้น)`}
                  </span>
                </div>
              </div>
            </div>
            {/* <div className="text-wrapper"> */}
            {/*  <span className="content-bold"> */}
            {/*    {numberWithCommas(item.item.totalPrice)} */}
            {/*  </span> */}
            {/* </div> */}
          </div>
        ))}
      <div className="card-footer-wrapper">
        <div className="text-wrapper flex flex-row items-center">
          <Image
            className="mr-2"
            priority
            src="/icons/sell.svg"
            alt=""
            width={24}
            height={24}
          />
          <span className="content-bold">ยอดชำระเงินทั้งหมด: </span>
        </div>
        <div className="text-wrapper">
          <span className="subtitle-bold">{numberWithCommas(totalPrice)}</span>
        </div>
      </div>
    </MyOrdersStyled>
  );
};

export default CardMyOrderCustom;
