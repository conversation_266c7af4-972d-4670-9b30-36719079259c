import React, { useState, useEffect } from 'react';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import styled from 'styled-components';
import CloseIcon from '@mui/icons-material/Close';
import TextField from '@mui/material/TextField';
import CouponStyle from '@/components/customUI/CouponCard/CouponStyle';
import apiDiscount from '@/services/discount';
import { isEmpty } from 'lodash';
import CouponRadio from '@/components/customUI/CouponCard/CouponRadio';
import { Form, Formik } from 'formik';
import { useAppDispatch } from '@/store/index';
import { couponSelector, setCoupon } from '@/store/reducers/backup/couponSlice';
import { useSelector } from 'react-redux';

type Props = {
  open: boolean;
  handleClose: () => void;
};
const ModalCouponListStyle = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  width: 600px;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dbe2e5;
    > div {
      width: 40px;
    }
    h2 {
      font-weight: 700;
      font-size: 20px;
    }
    button {
      color: #212121;
      min-width: unset;
      &:hover {
        background: unset;
      }
    }
  }
  .box-enter-coupon {
    padding: 1rem;
    border-bottom: 1px solid #dbe2e5;
    width: 100%;
    .input-group {
      display: flex;
      justify-content: space-between;
      .MuiFilledInput-root {
        &:after {
          content: unset;
          border: none !important;
        }
        &:before {
          content: unset;
        }
      }
      .MuiInputBase-hiddenLabel {
        background: unset !important;
        input {
          border-radius: 8px 0 0 8px;
          border: 1px solid #dbe2e5;
          background-color: rgba(0, 0, 0, 0.06);
          &:focus {
            background-color: #fff;
          }
        }
      }

      button {
        background: #601feb;
        color: #fff;
        border-radius: 0 8px 8px 0;
        border: 1px solid #601feb !important;
      }
    }
  }
  .box-coupon-list {
    padding: 1rem;
    border-bottom: 1px solid #dbe2e5;
    height: 400px;
    overflow-y: auto;
    .box-form-control {
      //margin-bottom: 1.2rem;
    }
    > p {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    .not-found-coupon {
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ddd;
      p {
        font-size: 14px;
      }
    }
  }
  .box-bottom {
    padding: 1rem;
    button {
      box-shadow: unset;
      border-radius: 8px;
      background: #212121;
    }
  }
  @media (max-width: 768px) {
    top: 0;
    left: 0;
    transform: unset;
    background: #fff;
    width: 100%;
    height: 100%;
    border-radius: 0;
    border: none;
    .box-coupon-list {
      height: 550px;
    }
    @media (max-height: 576px) {
      .box-coupon-list {
        height: 360px;
      }
    }
    @media (max-height: 425px) {
      .box-coupon-list {
        height: 210px;
      }
    }
  }
  @media (max-width: 576px) {
    .box-coupon-list {
      height: 550px;
    }
  }
  @media (max-width: 425px) {
    header {
      padding: 1rem 0;
      h2 {
        font-size: 18px;
      }
    }
  }
`;

const ModalCouponList = ({ open, handleClose }: Props) => {
  const dispatch = useAppDispatch();
  const couponSelectors = useSelector(couponSelector);
  const [dataMyDiscount, setDataMyDiscount] = useState<any>({});
  const [dataDiscountCategory, setDataDiscountCategory] = useState<any>([]);

  const getUserDiscountList = async () => {
    const res = await apiDiscount.getUserDiscountList({
      page: 0,
      size: 100,
      ascending: true,
    });
    if (res.status) {
      const sortMyDiscountCategory = res.data.content?.sort(
        (a: any, b: any) => {
          return (
            a.discount.discountCategory.id - b.discount.discountCategory.id
          );
        },
      );
      setDataMyDiscount(sortMyDiscountCategory);
    }
  };
  const getDiscountCategoryList = async () => {
    const res = await apiDiscount.getDiscountCategoryList();
    if (res.status) {
      setDataDiscountCategory(res.data);
    }
  };

  useEffect(() => {
    getDiscountCategoryList();
  }, []);
  useEffect(() => {
    getUserDiscountList();
  }, []);

  return (
    <Modal open={open} onClose={handleClose}>
      <ModalCouponListStyle>
        <Formik
          initialValues={{}}
          onSubmit={(values: any) => {
            const discounts = Object.keys(values).map((item: any) => {
              return values[item];
            });
            dispatch(setCoupon(discounts));
            handleClose();
          }}
        >
          {(formik) => (
            <Form>
              <header>
                <div />
                <h2>คูปองที่ใช้กับรายการสั่งซื้อนี้ได้</h2>
                <Button onClick={handleClose}>
                  <CloseIcon />
                </Button>
              </header>
              <div className={'box-enter-coupon'}>
                <form>
                  <div className="input-group">
                    <TextField
                      placeholder="เพิ่มรหัสคูปองส่วนลด"
                      hiddenLabel
                      defaultValue=""
                      variant="filled"
                      size="small"
                      fullWidth
                    />
                    <Button>ใช้</Button>
                  </div>
                </form>
              </div>
              <div className={'box-coupon-list'}>
                <p>คูปองส่วนลด</p>
                {!isEmpty(dataMyDiscount) ? (
                  <CouponStyle>
                    {' '}
                    <div className={'box-category-modal'}>
                      {dataDiscountCategory.map(
                        (category: any, index: number) => {
                          const categoryId = category.id;
                          const { name } = category;
                          const discountId =
                            !isEmpty(couponSelectors) &&
                            couponSelectors.find((item: any) => {
                              return item.discountCategory.name === name;
                            });
                          return (
                            <CouponRadio
                              key={index}
                              name={name}
                              categoryId={Number(categoryId)}
                              setFieldValue={(name, value) =>
                                formik.setFieldValue(name, value)
                              }
                              discountId={!isEmpty(discountId) && discountId.id}
                            />
                          );
                        },
                      )}
                    </div>
                  </CouponStyle>
                ) : (
                  <div className={'not-found-coupon'}>
                    <p>ไม่พบคูปองส่วนลด</p>
                  </div>
                )}
              </div>
              <div className="box-bottom">
                <Button type={'submit'} fullWidth variant="contained">
                  ตกลง
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </ModalCouponListStyle>
    </Modal>
  );
};

export default ModalCouponList;
