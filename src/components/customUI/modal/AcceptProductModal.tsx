import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { OrderType } from '@/types/order';
import apiOrder from '@/services/order';
import { toast } from 'sonner';

interface AcceptProductModalType {
  open: boolean;
  handleClose: (value: any) => void;
  // handleSubmitModal: (value: any) => void;
  setFetch: React.Dispatch<React.SetStateAction<boolean>>;
  data: OrderType;
}

const AcceptProductModal = (props: AcceptProductModalType) => {
  const { open, handleClose, data, setFetch } = props;

  const handleSubmitModal = async () => {
    const res = await apiOrder.customerConfirmReceived(data.id);
    if (res.status) {
      toast.success(res.message);
      handleClose(false);
      setFetch(true);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-quotation-order sm:max-w-[425px]">
        <DialogHeader className="border-b-[1px] flex">
          <DialogTitle className="text-center p-4">
            ยืนยันยอมรับสินค้า
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
          <div className="bg-[#F5F5F5] rounded-full w-fit p-[30px]">
            <Image
              className="rounded-xl"
              priority
              src="/icons/exclamation.svg"
              alt=""
              width={40}
              height={40}
            />
          </div>
          <div className="text-wrapper mt-[32px] flex flex-col">
            <span className="content">
              กรุณาตรวจสอบสินค้า ก่อนกดยืนยันการรับสินค้า หลังจากยืนยันแล้ว
            </span>
            <span className="content">
              ในคำสั่งรายการสินค้า จะไม่สามารถขอเคลมสินค้าได้
            </span>
          </div>
        </div>
        <DialogFooter className="flex flex-row p-6">
          <Button
            type={'button'}
            className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
            onClick={() => handleClose(false)}
          >
            ยังไม่ใช่ตอนนี้
          </Button>
          <Button
            type={'submit'}
            onClick={handleSubmitModal}
            className="w-1/2 ml-2"
          >
            ตกลง
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AcceptProductModal;
