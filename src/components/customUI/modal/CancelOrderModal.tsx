import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import React from 'react';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { OrderType } from '@/types/order';
import { Textarea } from '@/components/ui/textarea';
import * as yup from 'yup';
import Image from 'next/image';

interface CancelOrderModalType {
  open: boolean;
  handleClose: (value: any) => void;
  handleSubmitModal: (value: any) => void;
  data: OrderType;
  status: any;
}

const CancelOrderModal = (props: CancelOrderModalType) => {
  const { open, handleClose, handleSubmitModal, status, data } = props;

  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-cancel-order sm:max-w-[425px]">
        {status !== 'canceled' ? (
          ''
        ) : (
          <DialogHeader className="border-b-[1px] flex">
            <DialogTitle className="text-center p-4">
              ยืนยันลบรายการสั่งซื้อ
            </DialogTitle>
          </DialogHeader>
        )}
        <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
          <div className="bg-[#FFECEC] rounded-full w-fit p-[30px]">
            <Image
              className="rounded-xl"
              priority
              src="/icons/delete.svg"
              alt=""
              width={40}
              height={40}
            />
          </div>
          {status !== 'canceled' ? (
            <div className="text-wrapper mt-[32px] flex flex-col">
              <span className="subtitle-bold">ยกเลิกรายการ</span>
              <span className="content">
                {`หากคุณต้องการยกเลิกรายการ “${data.orderNumber}”`}
              </span>
              <span className="content">โปรดระบุหมายเหตุการยกเลิก</span>
            </div>
          ) : (
            <div className="text-wrapper mt-[32px] flex flex-col">
              <span className="subtitle-bold"></span>
              <span className="content">
                คุณยืนยันลบรายการสั่งซื้อสินค้าหมายเลข
              </span>
              <span className="content-bold">
                {data.orderNumber} <span className="content"> ใช่หรือไม่</span>
              </span>
            </div>
          )}
        </div>
        {status !== 'canceled' ? (
          <Formik
            initialValues={{ description: '' }}
            onSubmit={(values) => handleSubmitModal(values)}
            validationSchema={yup.object().shape({
              description: yup.string().required('กรุณากรอกข้อมูล'),
            })}
          >
            {(formik) => (
              <Form>
                <Field name="description">
                  {({ field }: any) => (
                    <>
                      <Textarea
                        onChange={(event: any) =>
                          formik.setFieldValue(
                            'description',
                            event.target.value,
                          )
                        }
                        value={field.value}
                        placeholder="ข้อมูลเพิ่มเติมที่ต้องการเกี่ยวกับสินค้านี้"
                        className="resize-none border-1-[#EEEEEE] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div
                            className="text-red-500"
                            style={{ fontSize: '14px', marginTop: '.2rem' }}
                          >
                            {msg}
                          </div>
                        )}
                      </ErrorMessage>
                    </>
                  )}
                </Field>
                <DialogFooter className="flex flex-row mt-6">
                  <Button
                    type={'button'}
                    className="w-1/2 bg-white text-black border-[1px] border-black me-2 hover:bg-white hover:text-black"
                    onClick={() => handleClose(false)}
                  >
                    ยกเลิก
                  </Button>
                  <Button type={'submit'} className="w-1/2 ml-2">
                    ยืนยันยกเลิกรายการ
                  </Button>
                </DialogFooter>
              </Form>
            )}
          </Formik>
        ) : (
          <DialogFooter className="flex flex-row p-6">
            <Button
              type={'button'}
              className="w-1/2 bg-white text-black border-[1px] border-black me-2 hover:bg-white hover:text-black"
              onClick={() => handleClose(false)}
            >
              ไม่, ยังไม่ใช่ตอนนี้
            </Button>
            <Button
              type={'submit'}
              onClick={handleSubmitModal}
              className="w-1/2 ml-2"
            >
              ยืนยันลบ
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CancelOrderModal;
