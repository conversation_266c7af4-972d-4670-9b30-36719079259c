import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import React from 'react';
import { OrderType } from '@/types/order';
import Image from 'next/image';

interface ReQuotationModalType {
  open: boolean;
  handleClose: (value: any) => void;
  handleSubmitModal: (value: any) => void;
  data: OrderType;
}

const ReQuotationModal = (props: ReQuotationModalType) => {
  const { open, handleClose, handleSubmitModal } = props;

  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-quotation-order sm:max-w-[425px]">
        <DialogHeader className="border-b-[1px] flex">
          <DialogTitle className="text-center p-4">
            ยืนยันเสนอราคาอีกครั้ง
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
          <div className="bg-[#F5F5F5] rounded-full w-fit p-[30px]">
            <Image
              className="rounded-xl"
              priority
              src="/icons/source_notes.svg"
              alt=""
              width={40}
              height={40}
            />
          </div>
          <div className="text-wrapper mt-[32px] flex flex-col">
            <span className="content">
              กรุณาตรวจสอบรายการออเดอร์ ก่อนกดยืนยันเสนอราคาอีกครั้ง
            </span>
            {/* <span className="content">โปรดระบุหมายเหตุการยกเลิก</span> */}
          </div>
        </div>
        <DialogFooter className="flex flex-row p-6">
          <Button
            type={'button'}
            className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
            onClick={() => handleClose(false)}
          >
            ไม่, ยังไม่ใช่ตอนนี้
          </Button>
          <Button
            type={'submit'}
            onClick={handleSubmitModal}
            className="w-1/2 ml-2"
          >
            ยืนยันเสนอราคาอีกครั้ง
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReQuotationModal;
