import React, { Fragment, useEffect } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { isEmpty, isUndefined } from 'lodash';
import { MaterialType } from '@/types/material';
import { ErrorMessage, useFormikContext } from 'formik';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { materialSelector } from '@/store/reducers/backup/materialSlice';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { Skeleton } from '@/components/ui/skeleton';

const MaterialCustom = () => {
  const { materials, materialLoading } = useSelector(materialSelector);
  const { values, setFieldValue } = useFormikContext<FormValues>();

  useEffect(() => {
    if (!isEmpty(materials) && !isUndefined(materials)) {
      const materialId =
        values.materialId === 0 ? materials[0].id : values.materialId;
      setFieldValue('materialId', materialId).then();
    }
  }, [materials]);

  const handleSelectMaterial = async (materialId: number) => {
    await setFieldValue('materialId', materialId);
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-3"
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">วัสดุ</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        {materialLoading ? (
          <div className="w-full h-[80px] animate-pulse">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          <div className="grid desktop:grid-cols-repeat-2 grid-cols-1 gap-4 mt-6">
            <Fragment>
              {!isEmpty(materials) &&
                materials.map((data: MaterialType, index: number) => (
                  <Card
                    key={index}
                    className={`p-3 cursor-pointer ${Number(values.materialId) === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                    onClick={() => handleSelectMaterial(data.id)}
                  >
                    <div className="flex items-center align-middle cursor-pointer">
                      <Image
                        priority
                        className="rounded-full mr-[10px]"
                        src={
                          data.imageUrl
                            ? data.imageUrl
                            : '/images/default-image.png'
                        }
                        alt=""
                        width={56}
                        height={56}
                      />
                      <div className="flex flex-col p-4 py-0">
                        <label className="text-sm font-bold cursor-pointer">
                          {data.name}
                        </label>
                        <label className="text-sm font-bold cursor-pointer">
                          {data.gram}
                          {data.gram ? ' แกรม' : ''}
                        </label>
                      </div>
                    </div>
                  </Card>
                ))}
              <ErrorMessage name={'materialId'}>
                {(msg) => <div className="text-red-500">{msg}</div>}
              </ErrorMessage>
            </Fragment>
          </div>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default MaterialCustom;
