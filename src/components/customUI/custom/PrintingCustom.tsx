import React, { Fragment, useEffect } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { ErrorMessage, Field, useFormikContext } from 'formik';
import { dataArrayMockPrint } from '../../../const/mockDataOrderPage';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { isEmpty, isUndefined } from 'lodash';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';

const PrintingCustom = () => {
  const { values, setFieldValue } = useFormikContext<FormValues>();

  useEffect(() => {
    setFieldValue('printing', 1).then();
    setFieldValue('printingPrice', 0).then();
  }, []);

  const handlerSelectPrinting = async (printId: number, price: number) => {
    await setFieldValue('printing', printId);
    await setFieldValue('printingPrice', price);
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-3"
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">การพิมพ์</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        <Field name="printing">
          {({ field }: any) => (
            <Fragment>
              <div className="grid desktop:grid-cols-repeat-2 grid-cols-1 gap-4 mt-6">
                {!isEmpty(dataArrayMockPrint) &&
                  !isUndefined(dataArrayMockPrint) &&
                  dataArrayMockPrint.map((data: any, index: number) => (
                    <Card
                      key={index}
                      className={`p-3 cursor-pointer ${Number(values.printing) === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                      onClick={() => handlerSelectPrinting(data.id, data.price)}
                    >
                      <div className="flex items-center align-middle cursor-pointer">
                        <Image
                          priority
                          className="rounded-full mr-[10px]"
                          src={
                            data.imageUrl
                              ? data.imageUrl
                              : '/images/default-image.png'
                          }
                          alt=""
                          width={56}
                          height={56}
                        />
                        <div className="flex flex-col p-4 py-0">
                          <label className="text-sm font-bold cursor-pointer">
                            {data.name}
                          </label>
                        </div>
                      </div>
                    </Card>
                  ))}
              </div>
              <ErrorMessage name={field.name}>
                {(msg) => <div className="text-red-500">{msg}</div>}
              </ErrorMessage>
            </Fragment>
          )}
        </Field>
      </AccordionContent>
    </AccordionItem>
  );
};

export default PrintingCustom;
