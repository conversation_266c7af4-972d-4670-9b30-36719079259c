import React, { useEffect, useState } from 'react';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { Field, useFormikContext } from 'formik';
import CardSpecialCustom from '@/components/customUI/custom/CardSpecialCustom';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import InputCustom from '@/components/customUI/InputCustom';
import { SpecialTechnicType } from '@/types/specialTechnic';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { specialTechnicSelector } from '@/store/reducers/backup/specialTechnicSlice';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';

type ValidateType = {
  width: {
    error: boolean;
    message: string;
  };
  height: {
    error: boolean;
    message: string;
  };
};

const SpecialTechnicCustom = () => {
  const { values, setFieldValue } = useFormikContext<FormValues>();
  const { specialTechnics } = useSelector(specialTechnicSelector);
  const [selectedSpecial, setSelectedSpecial] = useState<any>(null);
  const [selectedSpecialAction, setSelectedSpecialAction] = useState<any>({});
  const [selectedSizeSpecialW, setSelectedSizeSpecialW] = useState(100);
  const [selectedSizeSpecialH, setSelectedSizeSpecialH] = useState(100);
  const [validateSize, setValidateSize] = useState<ValidateType>({
    width: {
      error: false,
      message: '',
    },
    height: {
      error: false,
      message: '',
    },
  });
  const [selectedList, setSelectedList] = useState<SpecialTechnicType[]>([]);
  const [isActiveModal, setIsActiveModal] = useState(false);

  useEffect(() => {
    if (!isEmpty(values.specialTechnic)) {
      setSelectedList(values.specialTechnic);
    }
  }, [values.specialTechnic]);

  const deleteSpecial = async (deleteIndex: number) => {
    const filteredList = selectedList.filter(
      (_: any, index: number) => index !== deleteIndex,
    );
    setSelectedList(filteredList);
    await setFieldValue('specialTechnic', filteredList);
  };

  const addSpecial = async () => {
    const newArrayData: SpecialTechnicType = {
      ...selectedSpecialAction,
      width: selectedSizeSpecialW,
      height: selectedSizeSpecialH,
    };
    const selected = selectedList;
    selected.push(newArrayData);
    await setFieldValue('specialTechnic', selected);
    setSelectedList(selected);
    setSelectedSpecial(0);
    setSelectedSizeSpecialW(100);
    setSelectedSizeSpecialH(100);
    setIsActiveModal(false);
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-1"
      data-state={'open'}
    >
      {!isEmpty(values) && !isUndefined(values) && (
        <AccordingTriggerCustom className="p-0">
          <div className="w-full flex justify-between">
            <p className="font-bold"> เทคนิคพิเศษ</p>
            <p>
              จำนวน
              {values.specialTechnic ? values.specialTechnic.length : 0}
              เทคนิคพิเศษ
            </p>
          </div>
        </AccordingTriggerCustom>
      )}
      <AccordionContent>
        <div className="w-full">
          <Field name="specialTechnic">
            {({ field }: any) => (
              <div className="w-full grid grid-cols-repeat-1 items-center gap-4 my-2">
                {!isEmpty(field.value) &&
                  !isUndefined(field.value) &&
                  field.value.map((data: any, index: number) => (
                    <CardSpecialCustom
                      key={index}
                      data={data}
                      selected={selectedSpecial}
                      setSelected={setSelectedSpecial}
                      setSelectedAction={setSelectedSpecialAction}
                      isDelete={true}
                      onDelete={() => deleteSpecial(index)}
                    />
                  ))}
              </div>
            )}
          </Field>
          <Dialog open={isActiveModal} onOpenChange={setIsActiveModal}>
            <DialogTrigger className="z-0" asChild>
              <Button
                className="bg-secondary-main rounded-[20px] hover:bg-secondary-light w-full mt-2"
                onClick={() => {
                  setIsActiveModal(true);
                }}
              >
                <div className="flex justify-between items-center w-full cursor-pointer">
                  <Image
                    priority
                    className="m-[10px]"
                    src="/icons/add_FILL0_wght400_GRAD0_opsz24 1.png"
                    alt=""
                    width={24}
                    height={24}
                  />
                  <label className="cursor-pointer">เพิ่มเทคนิคพิเศษ</label>
                  <label> </label>
                </div>
              </Button>
            </DialogTrigger>
            <DialogContent
              onInteractOutside={(e) => {
                e.preventDefault();
              }}
              className="w-full tablet:w-[640px] tablet:max-w-[640px] p-0 rounded-[16px]"
            >
              <DialogHeader className="border-b-[1px] flex">
                <DialogTitle className="text-center p-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      {!isNull(selectedSpecial) && (
                        <Image
                          priority
                          className="mr-[10px] cursor-pointer"
                          src="/icons/arrow_back.png"
                          alt=""
                          width={24}
                          height={24}
                          onClick={() => {
                            setSelectedSpecial(0);
                          }}
                        />
                      )}
                    </div>
                    <span>เทคนิคพิเศษ</span>
                  </div>
                </DialogTitle>
              </DialogHeader>
              {!isNull(selectedSpecial) && selectedSpecial !== 0 ? (
                <div className="p-[40px] pt-[24px] flex flex-col justify-center items-center">
                  <Image
                    priority
                    className="mr-[10px]"
                    src={
                      selectedSpecialAction.imageUrl
                        ? selectedSpecialAction.imageUrl
                        : '/images/default-image.png'
                    }
                    alt=""
                    width={122}
                    height={122}
                  />
                  <div className="mt-4">
                    <span className="text-[36px] font-bold">{`${selectedSizeSpecialW} x ${selectedSizeSpecialH} mm`}</span>
                  </div>
                  <div className="w-full grid grid-cols-2 gap-4">
                    <div className="flex flex-col">
                      <InputCustom
                        type={'number'}
                        min={20}
                        name="specialW"
                        label="W"
                        value={selectedSizeSpecialW}
                        setSelected={setSelectedSizeSpecialW}
                        onBlur={() => {
                          if (selectedSizeSpecialW === 0) {
                            setSelectedSizeSpecialW(0);
                          }
                        }}
                        onChange={(event: any) => {
                          const { value } = event.target;
                          if (Number(value) === 0) {
                            setSelectedSizeSpecialW(0);
                            setValidateSize({
                              ...validateSize,
                              width: {
                                error: true,
                                message: 'กรุณากรอก ความกว้างเทคนิคพิเศษ',
                              },
                            });
                            return;
                          }
                          if (value < 20) {
                            setSelectedSizeSpecialW(value);
                            setValidateSize({
                              ...validateSize,
                              width: {
                                error: true,
                                message:
                                  'กรุณากรอก ความกว้างเทคนิคพิเศษ อย่างน้อย 20 mm',
                              },
                            });
                            return;
                          }
                          setSelectedSizeSpecialW(value);
                          setValidateSize({
                            ...validateSize,
                            width: {
                              error: false,
                              message: '',
                            },
                          });
                        }}
                      />
                      {validateSize.width.error && (
                        <div className="text-red-500">
                          {validateSize.width.message}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col">
                      <InputCustom
                        type={'number'}
                        name="specialH"
                        label="H"
                        value={selectedSizeSpecialH}
                        setSelected={setSelectedSizeSpecialH}
                        onBlur={() => {
                          if (selectedSizeSpecialH === 0) {
                            setSelectedSizeSpecialH(0);
                          }
                        }}
                        onChange={(event: any) => {
                          let { value } = event.target;
                          value = value.replace(/[^0-9]/g, '');
                          value = value.replace(/^0+/, '');
                          if (value.length === 0) {
                            setSelectedSizeSpecialH(0);
                            setValidateSize({
                              ...validateSize,
                              height: {
                                error: true,
                                message: 'กรุณากรอก ความสูงเทคนิคพิเศษ',
                              },
                            });
                            return;
                          }
                          if (value < 20) {
                            setSelectedSizeSpecialH(value);
                            setValidateSize({
                              ...validateSize,
                              height: {
                                error: true,
                                message:
                                  'กรุณากรอก ความสูงเทคนิคพิเศษ อย่างน้อย 20 mm',
                              },
                            });
                            return;
                          }
                          setSelectedSizeSpecialH(value);
                          setValidateSize({
                            ...validateSize,
                            height: {
                              error: false,
                              message: '',
                            },
                          });
                        }}
                      />
                      {validateSize.height.error && (
                        <div className="text-red-500">
                          {validateSize.height.message}
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    disabled={
                      validateSize.width.error || validateSize.height.error
                    }
                    className="text-[20px] h-12 mt-4 bg-secondary-main rounded-[8px] hover:bg-secondary-light w-full"
                    onClick={() => addSpecial()}
                  >
                    เพิ่ม
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-3 tablet:grid-cols-4 gap-4 p-10 pt-6">
                  {!isEmpty(specialTechnics) &&
                    !isUndefined(specialTechnics) &&
                    specialTechnics.map(
                      (data: SpecialTechnicType, index: number) => (
                        <CardSpecialCustom
                          key={index}
                          selected={selectedSpecial}
                          data={data}
                          setSelected={setSelectedSpecial}
                          setSelectedAction={setSelectedSpecialAction}
                          isDelete={false}
                          onDelete={(specialId) => deleteSpecial(specialId)}
                        />
                      ),
                    )}
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default SpecialTechnicCustom;
