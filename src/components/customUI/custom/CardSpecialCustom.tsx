import React, { Fragment } from 'react';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { SpecialTechnicType } from '@/types/specialTechnic';

interface CardSpecialCustomProps {
  data: SpecialTechnicType;
  selected: any;
  setSelected: React.Dispatch<React.SetStateAction<number>>;
  setSelectedAction: React.Dispatch<React.SetStateAction<SpecialTechnicType>>;
  isDelete: boolean;
  detailActive?: boolean;
  onDelete: (index: number) => void;
}

const CardCustom = ({
  data,
  setSelected,
  setSelectedAction,
  isDelete,
  onDelete,
}: CardSpecialCustomProps) => {
  const getCardRef = (_event: any) => {
    setSelected(data.id);
    setSelectedAction(data);
  };

  return (
    <Fragment>
      {isDelete ? (
        <div className="flex flex-col justify-center items-center w-full">
          <Card className={`w-full p-3`}>
            <div className="flex items-center justify-between align-middle">
              <div className="flex items-center justify-center">
                <Image
                  priority
                  className="mr-[10px]"
                  src={
                    data.imageUrl ? data.imageUrl : '/images/default-image.png'
                  }
                  alt=""
                  width={56}
                  height={56}
                />
                <label className="text-sm font-bold">{data.name}</label>
                <label className="mx-2">•</label>
                <label className="text-sm font-bold">
                  {`${data.width}x${data.height} mm`}
                </label>
              </div>
              <Image
                priority
                onClick={() => onDelete(data.id)}
                className="mr-[10px] cursor-pointer"
                src={'/icons/delete.svg'}
                alt=""
                height={24}
                width={24}
              />
            </div>
          </Card>
        </div>
      ) : (
        <div className={`relative`}>
          <Card
            className={`border-0 cursor-pointer rounded-[16px]`}
            onClick={getCardRef}
          >
            <div className="flex justify-center items-center rounded-[16px] align-middle cursor-pointer">
              <Image
                priority
                className="rounded-[16px] max-h-[100px] tablet:max-h-[122px] tablet:h-[122px] tablet:w-[122px]"
                src={
                  data.imageUrl ? data.imageUrl : '/images/default-image.png'
                }
                alt=""
                width={122}
                height={122}
              />
            </div>
          </Card>
          <div className="text-center truncate whitespace-pre-wrap mt-2">
            <p>{data.name}</p>
          </div>
        </div>
      )}
    </Fragment>
  );
};

export default CardCustom;
