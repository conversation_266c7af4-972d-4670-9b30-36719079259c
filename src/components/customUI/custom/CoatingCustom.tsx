import React, { useEffect, useState } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { Field, useFormikContext } from 'formik';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { isEmpty, isUndefined } from 'lodash';
import { CoatingType } from '@/types/coating';
import Image from 'next/image';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { coatingSelector } from '@/store/reducers/backup/coatingSlice';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { Skeleton } from '@/components/ui/skeleton';

const CoatingCustom = () => {
  const { setFieldValue } = useFormikContext<FormValues>();
  const { coatings, coatingLoading } = useSelector(coatingSelector);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (!isEmpty(coatings) && !isUndefined(coatings)) {
      setFieldValue('coatingId', coatings[0].id).then();
      setFieldValue('coatingPrice', coatings[0].price).then();
    }
  }, [coatings]);

  const handleSelect = (coatingId: string) => {
    const coating = coatings.find(
      (data: CoatingType) => data.id === Number(coatingId),
    );

    if (!isUndefined(coating)) {
      setFieldValue('coatingId', Number(coatingId)).then();
      setFieldValue('coatingPrice', coating.price).then();
    }
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-1"
      data-state={'open'}
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between mb-6">
          <p className="font-bold"> การเคลือบ</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        {coatingLoading ? (
          <div className="w-full h-[80px] animate-pulse">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          <Field name="coatingId">
            {({ field }: any) => (
              <Select
                open={open}
                name={field.name}
                value={field.value.toString()}
                onValueChange={(coatingId: string) => handleSelect(coatingId)}
                onOpenChange={() => {
                  setTimeout(() => {
                    setOpen(!open);
                  }, 100);
                }}
              >
                <SelectTrigger className="w-full h-[80px] focus:ring-offset-0 focus:ring-offset focus:ring-neutral-0 focus-visible:ring-0 focus-visible:ring-offset-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="z-[1000]">
                  {!isEmpty(coatings) &&
                    !isUndefined(coatings) &&
                    coatings.map((data: CoatingType, index: number) => (
                      <SelectItem
                        className="flex flex-row"
                        key={index}
                        value={data.id.toString()}
                      >
                        <div className="flex justify-center align-middle items-center">
                          <Image
                            priority
                            className="mr-[24px]"
                            src={
                              data.imageUrl
                                ? data.imageUrl
                                : '/images/default-image.png'
                            }
                            alt=""
                            width={56}
                            height={56}
                          />
                          <label className="text-sm font-bold">
                            {data.name}
                          </label>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            )}
          </Field>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default CoatingCustom;
