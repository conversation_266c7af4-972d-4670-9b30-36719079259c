import React, { Fragment, useContext, useEffect } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { ErrorMessage, Field, useFormikContext } from 'formik';
import _, { isEmpty, isUndefined } from 'lodash';
import { ModelType } from '@/types/model';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { customSelector, setPrices } from '@/store/reducers/backup/customSlice';
import { useAppDispatch } from '@/store/index';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { Skeleton } from '@/components/ui/skeleton';
import { ModelList } from '@/lib/packaging-3d/utils/model-list';
import { FormTo3dContext } from '@/components/customUI/custom/CustomizeComponent';

const ModelCustom = () => {
  const dispatch = useAppDispatch();
  const { models, modelLoading } = useSelector(customSelector);
  const { values, setFieldValue, resetForm } = useFormikContext<FormValues>();
  const { value3d, setValue3d } = useContext(FormTo3dContext);

  const handleSelectModel = async (modelId: number) => {
    resetForm({
      values: {
        ...values,
        amountId: 0,
        amount: 0,
        amountPrice: 0,
      },
    });

    const modelItem = await models.find(
      (item: ModelType) => item.id === Number(modelId),
    );

    if (!isUndefined(modelItem)) {
      await setFieldValue('modelId', modelItem.id);
      const { prices } = await modelItem;
      if (!isEmpty(prices) && !isUndefined(prices)) {
        dispatch(setPrices(prices));
        setFieldValue('amountId', prices[0].id).then();
        setFieldValue('amount', prices[0].amount).then();
        setFieldValue('amountPrice', prices[0].price).then();
      } else {
        dispatch(setPrices([]));
      }
    }
  };

  useEffect(() => {
    setValue3d({ ...value3d, modelCode: null });
    const modelItem = models.find(
      (item: ModelType) => item.id === Number(values.modelId),
    );
    if (!isUndefined(modelItem)) {
      const isSupport = _.some(
        ModelList,
        (o: any) => o.code === modelItem.modelCode,
      );
      setTimeout(
        () =>
          setValue3d({
            ...value3d,
            modelName: modelItem.name || null,
            modelCode: modelItem.modelCode || null,
            isSupport: isSupport,
          }),
        1000,
      );
    } else {
      setTimeout(
        () =>
          setValue3d({
            ...value3d,
            modelName: null,
            modelCode: null,
            isSupport: false,
          }),
        1000,
      );
    }
  }, [values.modelId, models]);

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-1"
      data-state={'open'}
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">โมเดล</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        {modelLoading ? (
          <div className="w-full h-[80px] animate-pulse">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          <Field name="modelId">
            {({ field }: any) => (
              <Fragment>
                <div className="grid gap-4 mt-6 grid-cols-repeat">
                  {!isEmpty(models) &&
                    !isUndefined(models) &&
                    models.map((data: ModelType, index: number) => (
                      <div
                        key={index}
                        className={`flex flex-col justify-center items-center`}
                      >
                        <Card
                          className={`cursor-pointer rounded-[16px] ${Number(field.value) === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                          onClick={() => handleSelectModel(data.id)}
                        >
                          <div className="flex justify-center items-center rounded-[16px] align-middle">
                            <Image
                              priority
                              className="rounded-[16px]"
                              src={
                                data.imageUrl
                                  ? data.imageUrl
                                  : '/images/default-image.png'
                              }
                              alt=""
                              width={140}
                              height={140}
                            />
                          </div>
                        </Card>
                        <label
                          className={`mt-4 text-xs font-bold ${Number(field.value) === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                        >
                          {data.name}
                        </label>
                      </div>
                    ))}
                </div>
                <ErrorMessage name={field.name}>
                  {(msg) => <div className="text-red-500">{msg}</div>}
                </ErrorMessage>
              </Fragment>
            )}
          </Field>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default ModelCustom;
