import React, { useEffect } from 'react';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { dataArrayMockExamProduct } from '../../../const/mockDataOrderPage';
import CardExamProductCustom from '@/components/customUI/CardExamProductCustom';
import { isEmpty, isUndefined } from 'lodash';
import { useFormikContext } from 'formik';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';

const ProductDemoCustom = () => {
  const { values, setFieldValue } = useFormikContext<FormValues>();
  useEffect(() => {
    setFieldValue('productDemo', 1).then();
    setFieldValue('productDemoPrice', 0).then();
  }, []);

  const handlerSelectExam = (examId: number, price: number) => {
    setFieldValue('productDemo', examId).then();
    setFieldValue('productDemoPrice', price).then();
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-3"
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">ตัวอย่างสินค้า</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        <div className="grid grid-cols-repeat-1 gap-4 mt-6">
          {!isEmpty(dataArrayMockExamProduct) &&
            !isUndefined(dataArrayMockExamProduct) &&
            dataArrayMockExamProduct.map((data: any, index: any) => (
              <CardExamProductCustom
                key={index}
                data={data}
                selected={values.productDemo}
                onClick={(examId: number) =>
                  handlerSelectExam(examId, data.price)
                }
              />
            ))}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default ProductDemoCustom;
