import React, { useContext, useEffect } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { ErrorMessage, Field, useFormikContext } from 'formik';
import InputCustom from '@/components/customUI/InputCustom';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { FormTo3dContext } from '@/components/customUI/custom/CustomizeComponent';

const SizeCustom = () => {
  const { values } = useFormikContext<FormValues>();
  const { value3d, setValue3d } = useContext(FormTo3dContext);

  useEffect(() => {
    setValue3d({
      ...value3d,
      width: values.width || 0,
      height: values.height || 0,
      length: values.length || 0,
    });
  }, [values.width, values.height, values.length]);

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-2"
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">ขนาด</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        <div className="flex flex-col justify-between">
          <div className="grid tablet:grid-cols-1 desktop:grid-cols-3 gap-4">
            <div className="flex flex-col">
              <Field name="width">
                {({ field }: any) => (
                  <div>
                    <InputCustom
                      type={'number'}
                      label="W"
                      name="width"
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="flex flex-col">
              <Field name="length">
                {({ field }: any) => (
                  <div>
                    <InputCustom
                      type={'number'}
                      label="L"
                      name="length"
                      onChange={field.onChange}
                      value={values.length}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="flex flex-col">
              <Field name="height">
                {({ field }: any) => (
                  <div>
                    <InputCustom
                      type="number"
                      label="H"
                      name="height"
                      onChange={field.onChange}
                      value={values.height}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default SizeCustom;
