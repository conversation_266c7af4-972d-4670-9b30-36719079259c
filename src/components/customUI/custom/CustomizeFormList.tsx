'use client';

import { ErrorMessage, useFormikContext } from 'formik';
import React, { Fragment, useEffect, useState } from 'react';
import { ProductType } from '@/types/product';
import { isEmpty, isNull, isUndefined } from 'lodash';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

import Image from 'next/image';

import { Button } from '@/components/ui/button';
import { numberWithCommas } from '@/utils/numberFormat';
import { useSelector } from 'react-redux';
import { productSelector } from '@/store/reducers/backup/productSlice';
import styled from 'styled-components';
import { Textarea } from '@/components/ui/textarea';
import { ReloadIcon } from '@radix-ui/react-icons';
import SpecialTechnicCustom from '@/components/customUI/custom/SpecialTechnicCustom';
import { useAppDispatch } from '@/store/index';
import { getSpecialTechnic } from '@/store/reducers/backup/specialTechnicSlice';
import { getMaterial } from '@/store/reducers/backup/materialSlice';
import { getCoating } from '@/store/reducers/backup/coatingSlice';
import ProductCustom from '@/components/customUI/custom/ProductCustom';
import ModelCustom from '@/components/customUI/custom/ModelCustom';
import { setModels, setPrices } from '@/store/reducers/backup/customSlice';
import MaterialCustom from '@/components/customUI/custom/MaterialCustom';
import CoatingCustom from '@/components/customUI/custom/CoatingCustom';
import PriceCustom from '@/components/customUI/custom/PriceCustom';
import PrintingCustom from '@/components/customUI/custom/PrintingCustom';
import SizeCustom from '@/components/customUI/custom/SizeCustom';
import ArtworkCustom from '@/components/customUI/custom/ArtworkCustom';
import ProductDemoCustom from '@/components/customUI/custom/ProductDemoCustom';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { PriceType } from '@/types/model';

export const AccordionCustom = styled(Accordion)``;
export const AccordingTriggerCustom = styled(AccordionTrigger)`
  div {
    font-size: 14px;
    align-items: center;
    :first-child {
      font-size: 20px;
    }
  }
  position: relative;
  svg {
    display: none;
  }
  img {
    display: none;
  }
  &:hover {
    text-decoration: unset;
  }
`;

type PropsType = {
  editMode: string;
  touched: any;
  errors: any;
  resetForm: (values: any) => void;
};

const MyForm = (props: PropsType) => {
  const dispatch = useAppDispatch();
  const [openAccordingItem] = useState(['item-1', 'item-2', 'item-3']);
  const { values, setFieldValue } = useFormikContext<FormValues>();
  const { products } = useSelector(productSelector);

  const [totalPrice, setTotalPrice] = useState(0);
  const [unitPrice, setUnitPrice] = useState(0);

  const loading = false;

  const setProduct = async (productId: number) => {
    const productItem = await products.find(
      (item: ProductType) => item.id === productId,
    );

    if (!isUndefined(productItem) && !isEmpty(productItem)) {
      await setFieldValue('productId', productId);
      const { productCategoryId, models } = productItem;
      await dispatch(getMaterial(productCategoryId));
      await dispatch(getCoating(productCategoryId));
      await dispatch(getSpecialTechnic(productCategoryId));

      if (!isEmpty(models)) {
        await dispatch(setModels(models));
        if (props.editMode === 'new') {
          await setFieldValue('modelId', models[0].id);
          const { prices } = models[0];
          if (!isEmpty(prices) && !isUndefined(prices)) {
            await dispatch(setPrices(prices));
            await setFieldValue('amountId', prices[0].id);
            await setFieldValue('amount', prices[0].amount);
            await setFieldValue('amountPrice', prices[0].price);
          }
        } else {
          const model = await models.find(
            (model: any) => model.id === values.modelId,
          );
          if (!isUndefined(model) && !isEmpty(model)) {
            if (!isEmpty(model.prices) && !isUndefined(model.prices)) {
              const { prices } = model;
              await dispatch(setPrices(prices));
              const amount = await prices.find(
                (item: PriceType) => item.amount === values.amount,
              );
              if (!isUndefined(amount)) {
                await setFieldValue('amount', amount.amount);
                await setFieldValue('amountPrice', amount.price);
              } else {
                await setFieldValue('amount', prices[0].amount);
                await setFieldValue('amountPrice', prices[0].price);
              }
            }
          }
        }
      }
    }
  };

  const calculatePrice = () => {
    const {
      amount,
      specialTechnic,
      coatingPrice,
      artworkPrice,
      printingPrice,
      productDemoPrice,
    } = values;

    if (!isNull(amount)) {
      const specialPrice =
        !isEmpty(specialTechnic) && !isNull(specialTechnic)
          ? specialTechnic.reduce(
              (sum: number, special: any) => sum + special.price,
              0,
            )
          : 0;
      const priceMat = values.amount * values.amountPrice;
      const total =
        priceMat +
        coatingPrice +
        artworkPrice +
        printingPrice +
        productDemoPrice +
        specialPrice;
      const unitPrice = total / amount;

      setTotalPrice(total);
      setUnitPrice(unitPrice);
    }
  };

  useEffect(() => {
    calculatePrice();
  }, [values]);

  useEffect(() => {
    if (!isEmpty(products) && !isEmpty(products) && !isUndefined(products)) {
      if (props.editMode === 'new') {
        if (values.productId !== 0) {
          setProduct(values.productId);
        } else {
          setProduct(1);
        }
      } else if (values.productId !== 0) {
        setProduct(values.productId);
      }
    }
  }, [products, values.productId]);

  return (
    <AccordionCustom
      defaultValue={openAccordingItem}
      value={openAccordingItem}
      type="multiple"
      className="w-full"
    >
      <ProductCustom mode={props.editMode} />
      <ModelCustom />
      <SizeCustom />
      <PriceCustom />
      <MaterialCustom />
      <PrintingCustom />
      <CoatingCustom />
      <SpecialTechnicCustom />
      <ArtworkCustom />
      <ProductDemoCustom />
      <AccordionItem
        className="bg-white p-6 border-[1px] rounded-lg mb-6"
        value="item-3"
      >
        <AccordingTriggerCustom className="p-0">
          <div className="w-full flex justify-between">
            <p className="font-bold">หมายเหตุ</p>
          </div>
        </AccordingTriggerCustom>
        <AccordionContent>
          <div className="grid grid-cols-repeat-1 gap-4 mt-6">
            <div className="flex flex-col">
              <Textarea
                onChange={(event: any) =>
                  setFieldValue('description', event.target.value)
                }
                value={values.description}
                placeholder="ข้อมูลเพิ่มเติมที่ต้องการเกี่ยวกับสินค้านี้"
                className="resize-none border-1-[#EEEEEE] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
              />
              {props.touched.description && props.errors.description ? (
                <ErrorMessage name="description">
                  {(msg) => <div className="text-red-500">{msg}</div>}
                </ErrorMessage>
              ) : null}
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem
        className="bg-black p-6 border-[1px] rounded-lg mb-6"
        value="item-3"
      >
        <AccordionContent className="flex tablet:justify-between flex-col tablet:flex-row">
          <div className="flex mb-4 tablet:mb-0 tablet:self-center">
            <div className="flex flex-col justify-start">
              <span className="text-white text-xl font-bold">
                {numberWithCommas(totalPrice, 2)}
              </span>
              <span className="text-white text-m">
                {`${numberWithCommas(unitPrice, 2)} บาท x ${numberWithCommas(values.amount, 0)} 
                 ชิ้น`}
              </span>
            </div>
          </div>
          <div className="flex tablet:justify-center">
            <Button
              disabled={loading}
              className="bg-primary-main h-[48px] w-full tablet:w-fit hover:bg-primary-light"
              type="submit"
            >
              {loading ? (
                <Fragment>
                  <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                  กำลังดำเนินการ
                </Fragment>
              ) : (
                <Fragment>
                  <Image
                    priority
                    className="rounded-[16px] mr-[15px]"
                    src="/icons/shopping_cart.png"
                    alt=""
                    width={24}
                    height={24}
                  />

                  <span className="text-white text-lg font-bold">
                    {props.editMode === 'edit'
                      ? 'แก้ไขข้อมูลสินค้า'
                      : 'เพิ่มไปยังรถเข็น'}
                  </span>
                </Fragment>
              )}
            </Button>
          </div>
        </AccordionContent>
      </AccordionItem>
    </AccordionCustom>
  );
};

export default MyForm;
