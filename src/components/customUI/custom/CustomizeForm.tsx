import React, { useEffect, useState } from 'react';

import * as yup from 'yup';

import { Form, Formik } from 'formik';
import MyForm from '@/components/customUI/custom/CustomizeFormList';
import { isNull, isUndefined } from 'lodash';
import { GetCartSpecialTechnics, GetCartType } from '@/types/cart';
import { RevisionItemType } from '@/types/revision';

interface CustomizeFormProps {
  submit: (values: any) => void;
  editData: GetCartType | RevisionItemType | null;
  editMode: string;
  custom?: any;
}

export interface FormValues {
  productId: number;
  modelId: number;
  width: number;
  length: number;
  height: number;
  materialId: number;
  amountId: number;
  amount: number;
  amountPrice: number;
  printing: number;
  printingPrice: number;
  coatingId: number;
  specialTechnic: GetCartSpecialTechnics[] | [];
  isArtwork: number;
  artworkUrl: string;
  artworkPrice: number;
  productDemo: number;
  productDemoPrice: number;
  description: string;
  status: number;
  unitPrice: number;
  trackingNumber: string;
  totalPrice: number;
  coatingPrice: number;
}

const CustomizeForm = ({
  submit,
  editMode,
  editData,
  custom,
}: CustomizeFormProps) => {
  const [initialValues, setInitialValues] = useState<FormValues>({
    productId: 0,
    modelId: 0,
    width: 30,
    length: 20,
    height: 50,
    materialId: 0,
    amountId: 0,
    amount: 0,
    amountPrice: 0,
    printing: 1,
    printingPrice: 0,
    coatingId: 0,
    specialTechnic: [],
    isArtwork: 1,
    artworkUrl: '',
    artworkPrice: 0,
    productDemo: 1,
    productDemoPrice: 0,
    description: '',
    status: 0,
    unitPrice: 0,
    trackingNumber: '',
    totalPrice: 0,
    coatingPrice: 0,
  });

  const validationSchema = yup.object().shape({
    modelId: yup.string().required('กรุณาเลือกโมเดล'),
    width: yup
      .number()
      .min(20, 'กรุณากรอกค่าขั้นต่ำ 20')
      .required('กรุณากรอกข้อมูล'),
    amount: yup.string().required('กรุณาเลือกจำนวน'),
    length: yup
      .number()
      .min(20, 'กรุณากรอกค่าขั้นต่ำ 20')
      .required('กรุณากรอกข้อมูล'),
    height: yup
      .number()
      .min(20, 'กรุณากรอกค่าขั้นต่ำ 20')
      .required('กรุณากรอกข้อมูล'),
    isArtwork: yup.number().nullable(),
    artworkUrl: yup.string().when('isArtwork', (isArtwork) => {
      if (Number(isArtwork) === 2) {
        return yup.string().required('กรุณากรอกลิงก์ตัวอย่าง');
      }
      return yup.string().nullable();
    }),
  });

  useEffect(() => {
    if (editMode === 'edit' || editMode === 're-quotation') {
      if (!isUndefined(editData) && !isNull(editData)) {
        setInitialValues({
          ...initialValues,
          productId: editData.model.productId,
          modelId: editData.model.id,
          width: editData.width,
          length: editData.length,
          height: editData.height,
          materialId: editData.material.id,
          amount: editData.material.amount,
          amountPrice: editData.material.price,
          printing: editData.printing.id,
          printingPrice: editData.printing.price,
          coatingId: editData.coating.id,
          specialTechnic: editData.specialTechnics,
          isArtwork: editData.artwork.status ? 2 : 1,
          artworkUrl: editData.artwork.url,
          artworkPrice: editData.artwork.price,
          productDemo: editData.productDemo.id,
          productDemoPrice: editData.productDemo.price,
          description: editData.description,
          unitPrice: editData.unitPrice,
          coatingPrice: editData.coating.price,
        });
      }
    } else if (
      editMode === 'custom' &&
      !isUndefined(custom) &&
      !isNull(custom)
    ) {
      setInitialValues({
        ...initialValues,
        productId: custom.productId,
        modelId: custom.modelId,
        amount: custom.amount,
      });
    }
  }, [editData, custom]);

  return (
    !isUndefined(initialValues) && (
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={submit}
        enableReinitialize={true}
        dirty={true}
      >
        {(formik) => (
          <Form>
            <MyForm {...formik} editMode={editMode} />
          </Form>
        )}
      </Formik>
    )
  );
};
export default CustomizeForm;
