import React, { Fragment } from 'react';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { dataArrayMockArtwork } from '../../../const/mockDataOrderPage';
import { Input } from '@/components/ui/input';
import { ErrorMessage, useFormikContext } from 'formik';
import { isEmpty, isUndefined } from 'lodash';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';

const ArtworkCustom = () => {
  const { values, setFieldValue, touched, errors } =
    useFormikContext<FormValues>();

  const handlerSelectArtwork = async (artwork: number, price: number) => {
    await setFieldValue('isArtwork', artwork);
    await setFieldValue('artworkPrice', price);
  };
  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-3"
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">อาร์ตเวิร์ก</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        <Fragment>
          <div className="grid tablet:grid-cols-1 desktop:grid-cols-repeat-2 gap-4 mt-6">
            {!isEmpty(dataArrayMockArtwork) &&
              !isUndefined(dataArrayMockArtwork) &&
              dataArrayMockArtwork.map((data: any, index: any) => (
                <Card
                  key={index}
                  className={`p-3 cursor-pointer ${Number(values.isArtwork) === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                  onClick={() => handlerSelectArtwork(data.id, data.price)}
                >
                  <div className="flex items-center align-middle cursor-pointer">
                    <Image
                      priority
                      className="rounded-full mr-[10px]"
                      src={
                        data.imageUrl
                          ? data.imageUrl
                          : '/images/default-image.png'
                      }
                      alt=""
                      width={56}
                      height={56}
                    />
                    <div className="flex flex-col p-4 py-0">
                      <label className="text-sm font-bold cursor-pointer">
                        {data.name}
                      </label>
                      <label className="text-xs cursor-pointer">
                        {data.detail}
                      </label>
                    </div>
                  </div>
                </Card>
              ))}
          </div>
          {values.isArtwork === 2 && (
            <div className="mt-6">
              <span>ลิงก์ตัวอย่างสินค้า</span>
              <Input
                className="mt-4 border-1-[#EEEEEE] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                onChange={(event: any) => {
                  const { value } = event.target;
                  setFieldValue('artworkUrl', value).then();
                }}
                placeholder={'วางลิงก์ตัวอย่างสินค้าที่คุณต้องการไว้ที่นี่'}
                value={values.artworkUrl}
              />
              {touched.artworkUrl && errors.artworkUrl ? (
                <ErrorMessage name="artworkUrl">
                  {(msg) => <div className="text-red-500">{msg}</div>}
                </ErrorMessage>
              ) : null}
            </div>
          )}
        </Fragment>
      </AccordionContent>
    </AccordionItem>
  );
};

export default ArtworkCustom;
