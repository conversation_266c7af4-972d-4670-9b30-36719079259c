import styled from 'styled-components';
import { PackageMockup } from '@/lib/packaging-3d/packaging-mockup';
import { MODE, PAPER_SIDE } from '@/lib/packaging-3d/utils/constants';
import {
  Hand,
  MousePointer,
  Pause,
  Play,
  RotateCcw,
  RotateCw,
} from 'lucide-react';
import { Slider } from '@mui/material';
import React, { useContext, useEffect, useMemo, useState } from 'react';

import _ from 'lodash';
import { ModelList } from '@/lib/packaging-3d/utils/model-list';
import { FormTo3dContext } from '@/components/newCustomUI/customize/CustomizeForm';
// import { FormTo3dContext } from '@/components/customUI/custom/CustomizeComponent';

export default function Model3dDisplay() {
  const [width, setWidth] = useState<number>(25);
  const [height, setHeight] = useState<number>(10);
  const [length, setLength] = useState<number>(15);
  const [is3D, setIs3D] = useState<boolean>(true);
  const [isHelper] = useState<boolean>(false);
  const [isPlay, setIsPlay] = useState<boolean>(false);
  const [isMapControl, setIsMapControl] = useState<boolean>(false);
  const [isFreeControl, setIsFreeControl] = useState<boolean>(true);
  const [actionState, setActionState] = useState<number>(100);
  const [zoom, setZoom] = useState<number>(100);
  const [side] = useState<number>(PAPER_SIDE.Back);
  const [baseSize, setBaseSize] = useState<number>(600);
  const [rotate, setRotate] = useState<{ x: number; y: number; z: number }>({
    x: 0,
    y: 0,
    z: 0,
  });

  const { value3d } = useContext(FormTo3dContext);

  useEffect(() => {
    if (
      value3d?.modelCode === 'HPM-PK04-001A0' ||
      value3d?.modelCode === 'HPM-PK04-002A0'
    ) {
      setBaseSize(450);
    } else {
      setBaseSize(600);
    }
  }, [value3d?.modelCode]);

  useEffect(() => {
    if (value3d?.modelCode) {
      const defaultModel = _.find(
        ModelList,
        (o: any) => o.code === value3d.modelCode,
      );
      if (defaultModel) {
        if (
          value3d?.modelCode !== 'HPM-PK04-001A0' &&
          value3d?.modelCode !== 'HPM-PK04-002A0'
        ) {
          if (
            value3d.width === 0 ||
            value3d.height === 0 ||
            value3d.length === 0
          ) {
            setWidth(defaultModel.defaultSize.width);
            setHeight(defaultModel.defaultSize.height);
            setLength(defaultModel.defaultSize.length);
          } else {
            setWidth(value3d.width);
            setHeight(value3d.height);
            setLength(value3d.length);
          }
        }
        if (value3d?.modelCode === 'HPM-PK04-001A0') {
          if (value3d.width === 0) {
            setWidth(defaultModel.defaultSize.width);
          } else {
            setWidth(value3d.width);
          }
        }
        if (value3d?.modelCode === 'HPM-PK04-002A0') {
          if (value3d.width === 0 || value3d.height === 0) {
            setWidth(defaultModel.defaultSize.width);
            setHeight(defaultModel.defaultSize.height);
          } else {
            setWidth(value3d.width);
            setHeight(value3d.height);
          }
        }
      }
    }
  }, [value3d.width, value3d.height, value3d.length]);

  const isAnimation = () => {
    if (value3d?.modelCode) {
      const modelInfo = _.find(
        ModelList,
        (o: any) => o.code === value3d?.modelCode,
      );
      return modelInfo?.isAnimation || false;
    }
    return false;
  };

  useMemo(() => {
    if (value3d?.modelCode) {
      setZoom(100);
      const defaultModel = _.find(
        ModelList,
        (o: any) => o.code === value3d.modelCode,
      );
      if (defaultModel) {
        setWidth(defaultModel.defaultSize.width);
        setHeight(defaultModel.defaultSize.height);
        setLength(defaultModel.defaultSize.length);
      }
    }
  }, [value3d?.modelCode]);

  return (
    <>
      <div className={'name-label'}>
        <div className={'product'}>{value3d?.product || ''}</div>
        <div className={'model'}>{value3d?.modelName || ''}</div>
      </div>
      <div className={'display-mode'}>
        <SwitchButton isActive={is3D} isShadow>
          <div className={'btn left'} onClick={() => setIs3D(false)}>
            2D
          </div>
          <div className={'btn right'} onClick={() => setIs3D(true)}>
            3D
          </div>
        </SwitchButton>
      </div>
      {value3d?.modelCode ? (
        <>
          {value3d?.isSupport ? (
            <PackageMockup
              modelId={value3d?.modelCode}
              width={width}
              height={height}
              length={length}
              unit={'mm'}
              is3D={is3D}
              isFreeControl={isFreeControl}
              actionState={actionState}
              mode={MODE.Preview}
              side={side}
              isHelper={isHelper}
              isPlay={isPlay}
              isMapControl={isMapControl}
              onCanvasSize={() => null}
              onCropPositions={() => null}
              textures={{ in: [], out: [] }}
              material={null}
              baseSize={baseSize}
              zoom={zoom}
              isDimension={true}
              rotation={{ x: rotate.x, y: rotate.y, z: rotate.y }}
            />
          ) : (
            <div>{`โมเดลนี้ยังไม่รองรับกับการแสดง ${is3D ? '3' : '2'} มิติ`}</div>
          )}
        </>
      ) : (
        <div>กำลังประมวลผล...</div>
      )}
      <div className={'action-panel'}>
        {is3D && (
          <ActionPanel>
            <div
              className={'input-container'}
              style={{ opacity: isPlay ? '0.5' : '1' }}
            >
              <div
                className={`btn-icon ${isFreeControl ? 'active' : ''}`}
                onClick={() => {
                  setIsMapControl(false);
                  setIsFreeControl(true);
                }}
              >
                <IconHeroStyle>
                  <MousePointer />
                </IconHeroStyle>
              </div>
              <div
                className={`btn-icon ${isMapControl ? 'active' : ''}`}
                onClick={() => {
                  setIsMapControl(true);
                  setIsFreeControl(false);
                }}
              >
                <IconHeroStyle>
                  <Hand />
                </IconHeroStyle>
              </div>
            </div>
            <div
              className={`btn-icon ${isPlay ? 'active' : ''}`}
              onClick={() => {
                if (isFreeControl && isAnimation()) {
                  setIsPlay(!isPlay);
                }
              }}
              style={{
                opacity: !isFreeControl || !isAnimation() ? '0.5' : '1',
              }}
            >
              <IconHeroStyle>{isPlay ? <Pause /> : <Play />}</IconHeroStyle>
            </div>
            <div className={'divide'} />
            <div
              className={`btn-icon `}
              onClick={() => {
                if (rotate.y <= 0) {
                  setRotate({ ...rotate, y: rotate.y - 1 });
                } else {
                  setRotate({ ...rotate, y: -1 });
                }
              }}
            >
              <IconHeroStyle>
                <RotateCcw />
              </IconHeroStyle>
            </div>
            <div
              className={`btn-icon`}
              onClick={() => {
                if (rotate.y >= 0) {
                  setRotate({ ...rotate, y: rotate.y + 1 });
                } else {
                  setRotate({ ...rotate, y: 1 });
                }
              }}
            >
              <IconHeroStyle>
                <RotateCw />
              </IconHeroStyle>
            </div>
            <div className={'divide'} />
            <div
              className={'input-container'}
              style={{
                opacity:
                  isPlay || !isFreeControl || !isAnimation() ? '0.5' : '1',
              }}
            >
              <div className={'text'}>Open</div>
              <div className={'slider-container'}>
                <Slider
                  aria-label="Volume"
                  value={actionState}
                  disabled={!isFreeControl || !isAnimation()}
                  onChange={(_event: Event, newValue: number | number[]) => {
                    setActionState(newValue as number);
                  }}
                />
              </div>
              <div className={'text'}>Close</div>
            </div>
            <div className={'divide'} />
            <div
              className={`btn-icon`}
              onClick={() => {
                setZoom(zoom - 10);
              }}
            >
              <IconHeroStyle>-</IconHeroStyle>
            </div>
            <div className={'text'}>{`${zoom}%`}</div>
            <div
              className={`btn-icon `}
              onClick={() => {
                setZoom(zoom + 10);
              }}
            >
              <IconHeroStyle>+</IconHeroStyle>
            </div>
          </ActionPanel>
        )}
      </div>
    </>
  );
}

const SwitchButton = styled.div<{
  isActive?: boolean;
  borderSize?: string;
  fullWidth?: boolean;
  isShadow?: boolean;
}>`
  display: flex;
  align-items: center;
  box-shadow: ${(props: any) =>
    props.isShadow && 'rgba(0, 0, 0, 0.16) 0px 1px 4px'};
  border: ${(props: any) => !props.isShadow && '1px solid #ddd'};
  border-radius: 8px;
  padding: 4px;
  background: #fff;

  .btn {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #666666;
    font-weight: 600;
    cursor: pointer;
    border-radius: 6px;
  }

  .left {
    width: ${(props: any) => props.fullWidth && '100%'};
    border-right: 1px solid rgba(222, 222, 222, 0.27);
    border-radius: ${(props: any) =>
      `${props.borderSize} 0 0 ${props.borderSize}`};
    background: ${(props: any) => (!props.isActive ? '#605DEC' : '#ffffff')};
    color: ${(props: any) => (!props.isActive ? '#ffffff' : '#666666')};
  }
  .right {
    width: ${(props: any) => props.fullWidth && '100%'};
    border-radius: ${(props: any) =>
      `0 ${props.borderSize} ${props.borderSize} 0`};
    background: ${(props: any) => (props.isActive ? '#605DEC' : '#ffffff')};
    color: ${(props: any) => (props.isActive ? '#ffffff' : '#666666')};
  }
`;

const ActionPanel = styled.div`
  width: fit-content;
  border-radius: 6px;
  background: #fff;
  padding: 4px 8px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  display: flex;
  align-items: center;
  column-gap: 16px;

  .input-container {
    display: flex;
    column-gap: 8px;
    align-items: center;

    .slider-container {
      width: 100px;
      display: flex;
      align-items: center;
      padding: 0 8px;
      span {
        color: #605dec !important;
      }
      .MuiSlider-thumb {
        height: 16px !important;
        width: 16px !important;
        color: #fff !important;
        &:hover {
          box-shadow: unset !important;
        }
      }
      .MuiSlider-track {
        height: 8px !important;
      }
      .MuiSlider-rail {
        height: 8px !important;
        color: #f3f2ffff;
      }
    }
  }

  .text {
    color: #666666;
    font-size: 12px;
  }

  .btn-icon {
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    color: #666666;
    font-size: 14px;

    &:hover {
      color: #605dec;
      background: #f3f2ffff;
    }
  }

  .btn-icon.active {
    color: #fff;
    background: #605dec;
  }

  .divide {
    border-right: 1px solid #e5e7eb;
    height: 60%;
    width: 2px;
  }
`;

const IconHeroStyle = styled.div`
  width: 15px;
  height: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 14px;
    height: 14px;
  }
`;
