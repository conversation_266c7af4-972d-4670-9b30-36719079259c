import React, { Fragment } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { ErrorMessage, Field, useFormikContext } from 'formik';
import { isEmpty, isUndefined } from 'lodash';
import { PriceType } from '@/types/model';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { customSelector } from '@/store/reducers/backup/customSlice';
import { Card } from '@/components/ui/card';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { Skeleton } from '@/components/ui/skeleton';
import styled from 'styled-components';
import { calculatePercentage } from '@/utils/calculator';
import { numberWithCommas } from '@/utils/numberFormat';

const PriceStyle = styled.div`
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  .tag-percentage {
    background: #ff4f6e;
    color: #fff;
    border-radius: 4px;
    padding: 0px 7px;
    text-align: center;
    font-size: 12px;
  }
  .price-discount {
    font-size: 12px;
    color: #bdbdbd;
    text-decoration: line-through;
  }
`;
const PriceCustom = () => {
  const { values, setFieldValue } = useFormikContext<FormValues>();
  const { prices, priceLoading } = useSelector(customSelector);

  const handleSelectAmount = async (amountId: number) => {
    const price = await prices.find(
      (item: PriceType) => item.id === Number(amountId),
    );
    const amountPrice = Number(
      price.percentage > 0
        ? calculatePercentage(price.price, price.percentage)
        : price.price,
    );
    if (!isUndefined(price)) {
      await setFieldValue('amountId', price.id);
      await setFieldValue('amount', price.amount);
      await setFieldValue('amountPrice', amountPrice);
    }
  };

  return (
    <AccordionItem
      className="bg-white p-6 border-[1px] rounded-lg mb-6"
      value="item-1"
      data-state={'open'}
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between">
          <p className="font-bold">จำนวน</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        {priceLoading ? (
          <div className="w-full h-[80px] animate-pulse">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          <Field name="amountId">
            {({ field }: any) => (
              <Fragment>
                <div className="grid desktop:grid-cols-repeat-2 grid-cols-1 gap-4 mt-6">
                  {!isEmpty(prices) &&
                    !isUndefined(prices) &&
                    prices.map((data: PriceType, index: number) => (
                      <Card
                        key={index}
                        className={`p-6 cursor-pointer ${values.amount === data.amount ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
                        onClick={() => handleSelectAmount(data.id)}
                      >
                        <div className="flex flex-col justify-center items-center align-middle cursor-pointer">
                          <label className="text-sm font-bold cursor-pointer">
                            {numberWithCommas(data.amount, 0)} ชิ้น
                          </label>
                          <label className="text-sm cursor-pointer">
                            {numberWithCommas(
                              data.percentage > 0
                                ? calculatePercentage(
                                    data.price,
                                    data.percentage,
                                  )
                                : data.price,
                            )}{' '}
                            บาท/ชิ้น
                          </label>
                          <PriceStyle>
                            {data.percentage > 0 && (
                              <>
                                <div className="tag-percentage">
                                  <div>-{data.percentage}%</div>
                                </div>
                                <div className="price-discount">
                                  ฿ {data.price}
                                </div>
                              </>
                            )}
                          </PriceStyle>
                        </div>
                      </Card>
                    ))}
                </div>
                <ErrorMessage name={field.name}>
                  {(msg) => <div className="text-red-500">{msg}</div>}
                </ErrorMessage>
              </Fragment>
            )}
          </Field>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default PriceCustom;
