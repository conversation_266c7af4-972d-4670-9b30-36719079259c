import React, { Fragment, useContext, useEffect } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { ErrorMessage, Field, useFormikContext } from 'formik';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import _, { isEmpty } from 'lodash';
import { ProductType } from '@/types/product';
import Image from 'next/image';
import { AccordingTriggerCustom } from '@/components/customUI/custom/CustomizeFormList';
import { useSelector } from 'react-redux';
import { productSelector } from '@/store/reducers/backup/productSlice';
import { FormValues } from '@/components/customUI/custom/CustomizeForm';
import { Skeleton } from '@/components/ui/skeleton';
import { FormTo3dContext } from '@/components/customUI/custom/CustomizeComponent';

type PropsType = {
  mode: string;
};
const ProductCustom = ({ mode }: PropsType) => {
  const { products, productLoading } = useSelector(productSelector);
  const { values, setFieldValue, resetForm } = useFormikContext<FormValues>();
  const { value3d, setValue3d } = useContext(FormTo3dContext);

  const handleSelectProduct = (productId: string) => {
    resetForm({
      values: {
        ...values,
        productId: 0,
        modelId: 0,
        amountId: 0,
        amount: 0,
        amountPrice: 0,
        materialId: 0,
        coatingId: 0,
        specialTechnic: [],
      },
    });
    setFieldValue('productId', Number(productId)).then();
    if (productId) {
      const productInfo = _.find(
        products,
        (o: any) => o.id === Number(productId),
      );
      if (productInfo) {
        setValue3d({ ...value3d, product: productInfo.name || null });
      } else {
        setValue3d({ ...value3d, product: null });
      }
    }
  };

  useEffect(() => {
    if (values.productId) {
      const productInfo = _.find(
        products,
        (o: any) => o.id === Number(values.productId),
      );
      if (productInfo) {
        setValue3d({ ...value3d, product: productInfo.name || null });
      } else {
        setValue3d({ ...value3d, product: null });
      }
    }
  }, [values.productId]);

  return (
    <AccordionItem
      className="bg-white p-4 tablet:p-6 border-[1px] rounded-lg mb-6"
      value="item-1"
      data-state={'open'}
    >
      <AccordingTriggerCustom className="p-0">
        <div className="w-full flex justify-between mb-6">
          <p className="font-bold">สินค้า</p>
        </div>
      </AccordingTriggerCustom>
      <AccordionContent>
        {productLoading ? (
          <div className="w-full h-[80px] animate-pulse">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          <Field name="productId">
            {({ field }: any) => (
              <Fragment>
                <Select
                  disabled={mode === 're-quotation'}
                  name={field.name}
                  value={field.value.toString()}
                  onValueChange={(productId) => handleSelectProduct(productId)}
                >
                  <SelectTrigger className="w-full h-[80px] focus:ring-offset-0 focus:ring-offset focus:ring-neutral-0 focus-visible:ring-0 focus-visible:ring-offset-0">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {!isEmpty(products) &&
                      products.map((data: ProductType, index: number) => (
                        <SelectItem
                          className="flex flex-row"
                          key={index}
                          value={data.id.toString()}
                        >
                          <div className="flex justify-center align-middle items-center">
                            <Image
                              priority
                              className="mr-[24px]"
                              src={
                                data.imageUrl
                                  ? data.imageUrl
                                  : '/images/default-image.png'
                              }
                              alt=""
                              width={56}
                              height={56}
                            />
                            <label className="text-sm font-bold">
                              {data.name}
                            </label>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <ErrorMessage name={field.name}>
                  {(msg) => <div className="text-red-500">{msg}</div>}
                </ErrorMessage>
              </Fragment>
            )}
          </Field>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default ProductCustom;
