import React, { createContext, useState } from 'react';
import styled from 'styled-components';
import CustomizeForm from '@/components/customUI/custom/CustomizeForm';
import { GetCartType } from '@/types/cart';
import { RevisionItemType } from '@/types/revision';
import Model3dDisplay from '@/components/customUI/custom/Model3dDisplay';

const CustomizeStyle = styled.div`
  display: flex;
  width: 100%;
  margin-top: 80px;
  padding: 60px 40px 0;
  @media only screen and (max-width: 430px) {
    flex-direction: column;
    padding: 30px 10px 0;
  }
  @media only screen and (max-width: 820px) {
    //padding: 16px 10px 0;
    margin-top: 56px;
  }
  .model-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60%;
    height: calc(100vh - 300px);
    margin-bottom: 40px;
    @media only screen and (max-width: 430px) {
      width: 100%;
      display: none;
    }
    .name-label {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 0;
      z-index: 2;
      .product {
        font-size: 18px;
        font-weight: 700;
      }
      .model {
        font-size: 12px;
        font-weight: 500;
      }
    }
    .display-mode {
      position: absolute;
      top: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      z-index: 2;
    }
    .action-panel {
      position: absolute;
      bottom: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      z-index: 2;
    }
  }
  .customize-container {
    display: flex;
    width: 40%;
    margin-bottom: 40px;
    @media only screen and (max-width: 430px) {
      width: 100%;
    }
  }
`;

type CustomizeComponentProps = {
  submit: (values: any) => void;
  editData: GetCartType | RevisionItemType | null;
  editMode: string;
  custom?: any;
};

type FormTo3dType = {
  product: string | null;
  modelName: string | null;
  modelCode: string | null;
  isSupport: boolean;
  width: number;
  minWidth: number;
  length: number;
  minLength: number;
  height: number;
  minHeight: number;
};
export const FormTo3dContext = createContext<FormTo3dType | any | null>(null);

const CustomizeComponent = (props: CustomizeComponentProps) => {
  const [value3d, setValue3d] = useState<FormTo3dType | null>({
    product: '',
    modelName: '',
    modelCode: '',
    isSupport: true,
    width: 20,
    minWidth: 20,
    length: 20,
    minLength: 20,
    height: 20,
    minHeight: 20,
  });

  return (
    <FormTo3dContext.Provider value={{ value3d, setValue3d }}>
      <CustomizeStyle>
        <div className="model-container">
          <Model3dDisplay />
        </div>
        <div className="customize-container">
          <div className="flex flex-col w-full">
            <CustomizeForm {...props} />
          </div>
        </div>
      </CustomizeStyle>
    </FormTo3dContext.Provider>
  );
};

export default CustomizeComponent;
