import React, { Fragment, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import * as yup from 'yup';
import apiThaiAddress from '@/services/thaiAddress';
import { isEmpty, isUndefined } from 'lodash';
import { UserShippingAddressType, UserTaxAddressType } from '@/store/type/user';
import {
  SubDistrictDetailsType,
  SubDistrictType,
  ZipcodeType,
} from '@/types/address';
import { ConnectedFocusError } from 'focus-formik-error';

interface FormModalType {
  isTax?: boolean;
  handleSubmitAddress: (value: any) => void;
  open: boolean;
  handleClose: (value: any) => void;
  loading: boolean;
  editData?: UserTaxAddressType | UserShippingAddressType;
  editMode: boolean;
}

const addressSchema = yup.object().shape({
  firstname: yup.string().required('กรุณากรอก ชื่อ'),
  lastname: yup.string().required('กรุณากรอก นามสกุล'),
  phoneNumber: yup
    .string()
    .required('กรุณากรอก หมายเลขโทรศัพท์')
    .matches(/^[0-9]+$/, 'กรุณากรอกหมาย เลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
    .min(10, 'กรุณากรอก หมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
    .max(10, 'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
  address: yup.string().required('กรุณากรอก ที่อยู่'),
  email: yup
    .string()
    .email('กรุณากรอก อีเมลให้ถูกต้อง')
    .required('กรุณากรอก อีเมล'),
  zipCode: yup
    .string()
    .required('กรุณากรอก รหัสไปรษณีย์')
    .matches(/^[0-9]+$/, 'กรุณากรอก รหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
    .min(5, 'กรุณากรอก รหัสไปรษณีย์ ให้ครบ 5 หลัก')
    .max(5, 'กรุณากรอก รหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
  province: yup.string().required('กรุณากรอก จังหวัด'),
  district: yup.string().required('กรุณากรอก อำเภอ'),
  subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
});

const TaxPersonSchema = yup.object().shape({
  taxPayerType: yup.string().required('กรุณาเลือก ประเภทผู้เสียภาษี'),
  taxId: yup
    .string()
    .max(13, 'กรุณากรอก ให้ครบ 13 ตัว')
    .min(13, 'กรุณากรอก ให้ครบ 13 ตัว')
    .required('กรุณากรอก เลขประจําตัวผู้เสียภาษีอากร'),
  firstname: yup.string().required('กรุณากรอก ชื่อ'),
  lastname: yup.string().required('กรุณากรอก นามสกุล'),
  phoneNumber: yup
    .string()
    .required('กรุณากรอก หมายเลขโทรศัพท์')
    .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
    .min(10, 'กรุณากรอก หมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
    .max(10, 'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
  email: yup
    .string()
    .email('กรุณากรอก อีเมล ให้ถูกต้อง')
    .required('กรุณากรอก อีเมล'),
  address: yup.string().required('กรุณากรอก ที่อยู่'),
  zipCode: yup
    .string()
    .required('กรุณากรอก รหัสไปรษณีย์')
    .matches(/^[0-9]+$/, 'กรุณากรอก รหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
    .min(5, 'กรุณากรอก รหัสไปรษณีย์ ให้ครบ 5 หลัก')
    .max(5, 'กรุณากรอก รหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
  province: yup.string().required('กรุณากรอก จังหวัด'),
  district: yup.string().required('กรุณากรอก อำเภอ'),
  subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
});

const TaxCorporateSchema = yup.object().shape({
  taxPayerType: yup.string().required('กรุณาเลือก ประเภทผู้เสียภาษี'),
  taxId: yup
    .string()
    .max(13, 'กรุณากรอก ให้ครบ 13 ตัว')
    .min(13, 'กรุณากรอก ให้ครบ 13 ตัว')
    .required('กรุณากรอก เลขประจําตัวผู้เสียภาษีอากร'),
  taxPayerName: yup.string().required('กรุณากรอก ชื่อบริษัท'),
  phoneNumber: yup
    .string()
    .required('กรุณากรอก หมายเลขโทรศัพท์')
    .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
    .min(10, 'กรุณากรอก หมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
    .max(10, 'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
  email: yup
    .string()
    .email('กรุณากรอก อีเมล ให้ถูกต้อง')
    .required('กรุณากรอก อีเมล'),
  address: yup.string().required('กรุณากรอก ที่อยู่'),
  zipCode: yup
    .string()
    .required('กรุณากรอกรหัสไปรษณีย์')
    .matches(/^[0-9]+$/, 'กรุณากรอกรหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
    .min(5, 'กรุณากรอก รหัสไปรษณีย์ ให้ครบ 5 หลัก')
    .max(5, 'กรุณากรอก รหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
  province: yup.string().required('กรุณากรอก จังหวัด'),
  district: yup.string().required('กรุณากรอก อำเภอ'),
  subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
});

const FormModal = ({
  isTax,
  handleSubmitAddress,
  open,
  handleClose,
  loading,
  editData,
  editMode,
}: FormModalType) => {
  const [arraySubDistrict, setArraySubDistrict] = useState<
    SubDistrictDetailsType[]
  >([]);
  const [taxType, setTaxType] = useState('1');
  const [errorZipcode, setErrorZipcode] = useState('');

  const objAddress =
    editMode && !isUndefined(editData)
      ? {
          firstname: editData.name.split(' ')[0],
          lastname: editData.name.split(' ')[1],
          phoneNumber: editData.phoneNumber,
          email: editData.email,
          address: editData.address,
          zipCode: editData.zipCode,
          province: editData.province,
          district: editData.district,
          subDistrict: editData.subDistrict,
          taxPayerType: editData.taxPayerType,
        }
      : {
          firstname: null,
          lastname: null,
          phoneNumber: null,
          email: null,
          address: null,
          zipCode: null,
          province: null,
          district: null,
          subDistrict: null,
          taxPayerType: null,
        };

  const objTax =
    editMode && !isUndefined(editData)
      ? {
          isTax: true,
          taxPayerType: editData.taxPayerType?.toString(),
          taxPayerName: editData.name,
          taxId: editData.taxId,
          firstname:
            editData.taxPayerType?.toString() === '1'
              ? editData.name.split(' ')[0]
              : editData.name,
          lastname:
            editData.taxPayerType?.toString() === '1'
              ? editData.name.split(' ')[1]
              : ' ',
          phoneNumber: editData.phoneNumber,
          email: editData.email,
          address: editData.address,
          zipCode: editData.zipCode,
          province: editData.province,
          district: editData.district,
          subDistrict: editData.subDistrict,
        }
      : {
          isTax: true,
          taxPayerType: '1',
          taxPayerName: null,
          taxId: null,
          firstname: null,
          lastname: null,
          phoneNumber: null,
          email: null,
          address: null,
          zipCode: null,
          province: null,
          district: null,
          subDistrict: null,
        };

  const handleFindZipcode = async (formik: any, keyword: string) => {
    const resZipcode = await apiThaiAddress.getZipcode(parseInt(keyword));
    if (resZipcode.status) {
      formik.setFieldValue('province', resZipcode?.data.provinceId.name);
      formik.setFieldValue('district', resZipcode?.data.districtId.name);
      setErrorZipcode('');
      const resAddress = await apiThaiAddress.getSubDistrict(
        resZipcode?.data.districtId.id,
      );
      if (resAddress.status) {
        setArraySubDistrict(resAddress?.data);
        formik.setFieldValue('subDistrict', resAddress.data[0]?.name);
      }
    } else {
      setErrorZipcode('รหัสไปรษณีย์นี้ ไม่มีในระบบ');
    }
  };

  const searchZipcode = async (formik: any, event: any) => {
    setErrorZipcode('');
    const newValue = event.target.value;
    if (/^\d*$/.test(newValue)) {
      formik.setFieldValue('zipCode', newValue);
    }
    if (newValue.length === 5) {
      await handleFindZipcode(formik, newValue);
    } else {
      formik.setFieldValue('province', null);
      formik.setFieldValue('district', null);
      formik.setFieldValue('subDistrict', null);
    }
  };

  const fetchAddress = async (zipcode: number) => {
    const zipCode: ZipcodeType = await apiThaiAddress.getZipcode(zipcode);

    if (zipCode.status) {
      const {
        data: { districtId },
      } = zipCode;
      const subDistrict: SubDistrictType = await apiThaiAddress.getSubDistrict(
        districtId.id,
      );
      if (subDistrict.status) {
        setArraySubDistrict(subDistrict.data);
      }
    }
  };

  useEffect(() => {
    if (open && editMode && !isUndefined(editData)) {
      fetchAddress(parseInt(editData.zipCode));
    }
  }, [open]);
  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-quotation-order max-w-[640px] max-h-[700px] overflow-y-scroll">
        <DialogHeader className="border-b-[1px] flex">
          <DialogTitle className="text-center p-4">
            {isTax
              ? editMode
                ? 'แก้ไขข้อมูลใบกำกับภาษี'
                : 'เพิ่มข้อมูลใบกำกับภาษี'
              : editMode
                ? 'แก้ไขข้อมูลการจัดส่งสินค้า'
                : 'เพิ่มข้อมูลการจัดส่งสินค้า'}
          </DialogTitle>
        </DialogHeader>
        <Formik
          initialValues={isTax ? objTax : objAddress}
          validationSchema={
            isTax
              ? taxType === '1'
                ? TaxPersonSchema
                : TaxCorporateSchema
              : addressSchema
          }
          onSubmit={(values) => handleSubmitAddress(values)}
          enableReinitialize={true}
        >
          {(formik) => (
            <Form className="form-address-custom-container h-full relative">
              <ConnectedFocusError />
              {isTax ? (
                <Fragment>
                  <h3 className="text-[20px] font-bold px-6 mb-4">
                    ข้อมูลส่วนตัว
                  </h3>
                  <div className="flex-row text-start mb-8 px-6">
                    <label className="text-bold text-m">
                      ประเภทผู้เสียภาษี
                    </label>
                    <Field name="taxPayerType">
                      {({ field }: any) => (
                        <div className="mt-[14px]">
                          <Select
                            name={field.name}
                            onValueChange={(event: any) => {
                              setTaxType(event);
                              formik.setFieldValue(field.name, event);
                            }}
                            value={field.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="เลือกตำบล/แขวง" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={'1'}>บุคคลธรรมดา</SelectItem>
                              <SelectItem value={'2'}>นิติบุคคล</SelectItem>
                            </SelectContent>
                          </Select>
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                  <div className="flex-row text-start mb-8 px-6">
                    <label className="text-bold text-m">
                      เลขประจําตัวผู้เสียภาษีอากร
                    </label>
                    <Field name="taxId">
                      {({ field }: any) => (
                        <div>
                          <Input
                            type="text"
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุเลขประจําตัวผู้เสียภาษีอากร'}
                            maxLength={13}
                            onChange={(e: any) => {
                              const input = e.target;
                              const { value } = input;
                              formik.setFieldValue(
                                field.name,
                                value.replace(/\D/g, ''),
                              );
                            }}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                </Fragment>
              ) : (
                <h3 className="text-[20px] font-bold px-6 mb-4">ผู้รับ</h3>
              )}
              {!isTax ? (
                <div className="flex flex-row text-start mb-8 px-6">
                  <div className="mr-2 w-1/2">
                    <label className="text-bold text-m">ชื่อ</label>
                    <Field name="firstname">
                      {({ field }: any) => (
                        <div>
                          <Input
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุชื่อจริง'}
                            onChange={field.onChange}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                  <div className="ml-2 w-1/2">
                    <label className="text-bold text-m">นามสกุล</label>
                    <Field name="lastname">
                      {({ field }: any) => (
                        <div>
                          <Input
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุนามสกุล'}
                            onChange={field.onChange}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                </div>
              ) : formik.values.taxPayerType === '2' ? (
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m">ชื่อบริษัท</label>
                  <Field name="taxPayerName">
                    {({ field }: any) => (
                      <div>
                        <Input
                          disabled={loading}
                          name={field.name}
                          className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                          placeholder={'ระบุชื่อบริษัท'}
                          onChange={field.onChange}
                          value={field.value}
                        />
                        <ErrorMessage name={field.name}>
                          {(msg) => (
                            <div className="text-red-500 text-xs mt-1">
                              {msg}
                            </div>
                          )}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
              ) : (
                <div className="flex flex-row text-start mb-8 px-6">
                  <div className="mr-2 w-1/2">
                    <label className="text-bold text-m">ชื่อ</label>
                    <Field name="firstname">
                      {({ field }: any) => (
                        <div>
                          <Input
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุชื่อจริง'}
                            onChange={field.onChange}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                  <div className="ml-2 w-1/2">
                    <label className="text-bold text-m">นามสกุล</label>
                    <Field name="lastname">
                      {({ field }: any) => (
                        <div>
                          <Input
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุนามสกุล'}
                            onChange={field.onChange}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => (
                              <div className="text-red-500 text-xs mt-1">
                                {msg}
                              </div>
                            )}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                </div>
              )}
              <div className="flex-row text-start mb-8 px-6">
                <label className="text-bold text-m">โทรศัพท์</label>
                <Field name="phoneNumber">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุหมายเลขโทรศัพท์'}
                        maxLength={10}
                        onChange={(e: any) => {
                          const input = e.target;
                          const { value } = input;
                          formik.setFieldValue(
                            field.name,
                            value.replace(/\D/g, ''),
                          );
                        }}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div className="text-red-500 text-xs mt-1">{msg}</div>
                        )}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
              <div className="flex-row text-start mb-8 px-6">
                <label className="text-bold text-m">อีเมล</label>
                <Field name="email">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุอีเมล'}
                        onChange={field.onChange}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div className="text-red-500 text-xs mt-1">{msg}</div>
                        )}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
              <h3 className="text-[20px] mb-4text-[20px] font-bold px-6 mb-4">
                ที่อยู่
              </h3>
              <div className="flex-row text-start mb-8 px-6">
                <label className="text-bold text-m">ที่อยู่</label>
                <Field name="address">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ'}
                        onChange={field.onChange}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div className="text-red-500 text-xs mt-1">{msg}</div>
                        )}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
              <div className="flex-row text-start mb-8 px-6">
                <label className="text-bold text-m">รหัสไปรษณีย์</label>
                <Field name="zipCode">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0 appearance-none"
                        placeholder={'ระบุเลขรหัสไปรษณีย์'}
                        maxLength={5}
                        onChange={(e) => searchZipcode(formik, e)}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div className="text-red-500 text-xs mt-1">{msg}</div>
                        )}
                      </ErrorMessage>
                      {errorZipcode.length > 0 && (
                        <div className="text-red-500 text-xs mt-1">
                          {errorZipcode}
                        </div>
                      )}
                    </div>
                  )}
                </Field>
              </div>
              {!isEmpty(formik.values.province) && (
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m">จังหวัด</label>
                  <Field name="province">
                    {({ field }: any) => (
                      <div>
                        <Input
                          disabled={true}
                          name={field.name}
                          className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                          onChange={field.onChange}
                          value={field.value}
                        />
                        <ErrorMessage name={field.name}>
                          {(msg) => (
                            <div className="text-red-500 text-xs mt-1">
                              {msg}
                            </div>
                          )}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
              )}
              {!isEmpty(formik.values.district) && (
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m">อำเภอ</label>
                  <Field name="district">
                    {({ field }: any) => (
                      <div>
                        <Input
                          disabled={true}
                          name={field.name}
                          className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                          onChange={field.onChange}
                          value={field.value}
                        />
                        <ErrorMessage name={field.name}>
                          {(msg) => (
                            <div className="text-red-500 text-xs mt-1">
                              {msg}
                            </div>
                          )}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
              )}
              {!isEmpty(formik.values.subDistrict) && (
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m">ตำบล/แขวง</label>
                  <Field name="subDistrict">
                    {({ field }: any) => (
                      <div className="mt-[14px]">
                        <Select
                          name={field.name}
                          onValueChange={(event: any) =>
                            formik.setFieldValue(field.name, event)
                          }
                          value={field.value}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="เลือกตำบล/แขวง" />
                          </SelectTrigger>
                          <SelectContent>
                            {arraySubDistrict.map(
                              (
                                subDistrictData: SubDistrictDetailsType,
                                index,
                              ) => (
                                <SelectItem
                                  key={index}
                                  value={subDistrictData.name}
                                >
                                  {subDistrictData.name}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        <ErrorMessage name={field.name}>
                          {(msg) => (
                            <div className="text-red-500 text-xs mt-1">
                              {msg}
                            </div>
                          )}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
              )}
              <DialogFooter className="flex flex-row p-6 w-full bg-white">
                <Button
                  disabled={loading}
                  type={'button'}
                  className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
                  onClick={() => handleClose(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  disabled={loading}
                  type={'submit'}
                  className="w-1/2 ml-2"
                >
                  บันทึก
                </Button>
              </DialogFooter>
            </Form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default FormModal;
