import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';
import * as yup from 'yup';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/store/index';
import {
  getUserProfile,
  resetUser,
  userSelector,
} from '@/store/reducers/backup/userSlice';
import { deleteCookie, getCookie } from 'cookies-next';
import { toast } from 'sonner';
import DeleteAccountModal from '@/components/customUI/account/DeleteAccountModal';
import apiUser from '@/services/user';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

const FormAccountCustomStyle = styled.div`
  .form-account-container {
    .form-account-card {
      padding: 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: start;
    }
    .image-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      .text-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .content {
          font-size: 14px;
          font-weight: 700;
        }
        .caption {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
  .content {
    font-size: 14px;
    font-weight: 400;
    &.bold {
      font-weight: 700;
    }
  }
`;

interface FormAccountCustomProps {
  submit: (values: any) => void;
  loading: boolean;
}

const addressSchema = yup.object().shape({
  firstname: yup.string().required('กรุณากรอก ชื่อ'),
  lastname: yup.string().required('กรุณากรอก นามสกุล'),
  phoneNumber: yup
    .string()
    .required('กรุณากรอก หมายเลขโทรศัพท์')
    .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
    .min(10, 'กรุณากรอก หมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
    .max(10, 'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
});

const FormAccountCustom = (props: FormAccountCustomProps) => {
  const { submit, loading } = props;
  const router = useRouter();
  const { user } = useSelector(userSelector);
  const token = getCookie('access_token');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const dispatch = useAppDispatch();
  const [loadingDelete, setLoadingDelete] = useState(false);

  const [IsActiveModalDelete, setIsActiveModalDelete] = useState(false);

  const handleFileUpload = () => {
    // Trigger the click event of the file input element
    if (fileInputRef.current) {
      fileInputRef?.current?.click();
    }
  };

  const handleFileChange = (event: any, formik: any) => {
    const file = event.target.files[0];

    if (file) {
      const imageUrl = URL.createObjectURL(file);
      const fileType = file.type;
      const maxSize = 800000; // 5MB
      if (fileType !== 'image/png' && fileType !== 'image/jpeg') {
        toast.error('Please upload an image of type PNG or JPEG.');
        return;
      }
      if (file.size > maxSize) {
        toast.error('File size exceeds 800KB. Please upload a smaller file.');
        return;
      }
      formik.setFieldValue('image', file);
      setSelectedImage(imageUrl);
    }
  };

  const handleSubmitModalDelete = async () => {
    setLoadingDelete(true);
    const resDelete = await apiUser.deleteAccount();
    if (resDelete.status) {
      setLoadingDelete(false);
      deleteCookie('access_token');
      dispatch(resetUser());
      toast.success(resDelete.message);
      setTimeout(() => {
        router.push('/login');
      }, 1000);
    } else {
      setLoadingDelete(false);
      toast.error(resDelete.error);
    }
  };

  useEffect(() => {
    if (token) {
      dispatch(getUserProfile());
    }
  }, []);

  useEffect(() => {
    setSelectedImage(user.imageUrl);
  }, [user]);

  return (
    <FormAccountCustomStyle>
      <Formik
        initialValues={{
          firstname: user.name?.split(' ')[0],
          lastname: user.name?.split(' ')[1],
          phoneNumber: user.phoneNumber,
          email: user.email,
          image: user.imageUrl,
        }}
        validationSchema={addressSchema}
        enableReinitialize={true}
        onSubmit={(values) => submit(values)}
      >
        {(formik) => (
          <Form className="form-account-container">
            <Card className="form-account-card">
              <Field name="image">
                {({ field }: any) => (
                  <>
                    <div className="image-wrapper">
                      <Input
                        name={field.name}
                        className="hidden"
                        ref={fileInputRef}
                        onChange={(values: any) =>
                          handleFileChange(values, formik)
                        }
                        type={'file'}
                      />
                      {selectedImage ? (
                        <div
                          className={`w-[80px] h-[80px] rounded-full content-center mr-4`}
                        >
                          <Image
                            className="w-full h-full object-cover rounded-full shadow-md"
                            src={selectedImage}
                            alt="Selected"
                            width={80}
                            height={80}
                          />
                        </div>
                      ) : (
                        <Image
                          className="mr-4"
                          priority
                          src="/icons/profile-demo.svg"
                          alt=""
                          width={80}
                          height={80}
                        />
                      )}
                      <div className="text-wrapper flex flex-col flex-1">
                        <span className="content">รูปโปร์ไฟล์</span>
                        <span className="caption">
                          JPG, GIF or PNG. ขนาดสูงสุด 800k
                        </span>
                      </div>
                      <Button type={'button'} onClick={handleFileUpload}>
                        อัปโหลดรูปภาพ
                      </Button>
                    </div>
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </>
                )}
              </Field>
            </Card>
            <div className="flex flex-row text-start my-8">
              <div className="mr-2 w-1/2">
                <label className="content bold">ชื่อ</label>
                <Field name="firstname">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุชื่อจริง'}
                        onChange={field.onChange}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => <div className="text-red-500">{msg}</div>}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
              <div className="ml-2 w-1/2">
                <label className="content bold">นามสกุล</label>
                <Field name="lastname">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุนามสกุล'}
                        onChange={field.onChange}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => <div className="text-red-500">{msg}</div>}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
            </div>
            <div className="flex flex-row text-start mb-8">
              <div className="w-full">
                <label className="content bold">โทรศัพท์</label>
                <Field name="phoneNumber">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={loading}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุหมายเลขโทรศัพท์'}
                        maxLength={10}
                        onChange={(e: any) => {
                          const input = e.target;
                          const { value } = input;
                          formik.setFieldValue(
                            field.name,
                            value.replace(/\D/g, ''),
                          );
                        }}
                        // onChange={(e: any) => {
                        //   const newValue = e.target.value;
                        //   if (/^\d*$/.test(newValue)) {
                        //     formik.setFieldValue(field.name, newValue);
                        //   }
                        // }}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => (
                          <div className="text-red-500 text-xs mt-1">{msg}</div>
                        )}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
            </div>
            <div className="flex flex-row text-start mb-8">
              <div className="w-full">
                <label className="content bold">อีเมล</label>
                <Field name="email">
                  {({ field }: any) => (
                    <div>
                      <Input
                        disabled={true}
                        name={field.name}
                        className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                        placeholder={'ระบุอีเมล'}
                        onChange={field.onChange}
                        value={field.value}
                      />
                      <ErrorMessage name={field.name}>
                        {(msg) => <div className="text-red-500">{msg}</div>}
                      </ErrorMessage>
                    </div>
                  )}
                </Field>
              </div>
            </div>
            <div className="flex flex-col text-start mb-8">
              <span className="content bold mb-2">ลบบัญชี</span>
              <span className="content mb-2">
                ลบบัญชีของคุณและเนื้อหาทั้งหมดของคุณอย่างถาวร
              </span>
              <Button
                type={'button'}
                className="bg-red-500 w-fit flex justify-center rounded-[8px] cursor-pointer hover:bg-red-400"
                onClick={() => {
                  setIsActiveModalDelete(true);
                }}
              >
                <Image
                  priority
                  className="mr-2"
                  src="/icons/delete-white.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                <span className="text-white">ลบบัญชี</span>
              </Button>
              <DeleteAccountModal
                open={IsActiveModalDelete}
                handleClose={setIsActiveModalDelete}
                handleSubmitModal={handleSubmitModalDelete}
                loading={loadingDelete}
              />
            </div>
            <div className="flex w-full mb-6">
              <Button
                disabled={loading}
                type="button"
                className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
                onClick={() => {
                  router.back();
                }}
              >
                ยกเลิก
              </Button>
              <Button
                disabled={loading}
                type="submit"
                className="confirm-button ml-2 w-1/2"
              >
                ตกลง
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </FormAccountCustomStyle>
  );
};

export default FormAccountCustom;
