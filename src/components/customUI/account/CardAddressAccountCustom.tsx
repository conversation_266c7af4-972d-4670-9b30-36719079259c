import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import DeleteAddressModal from '@/components/customUI/account/DeleteAddressModal';
import apiUser from '@/services/user';
import { getUserProfile } from '@/store/reducers/backup/userSlice';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/index';
import FormModal from '@/components/customUI/account/FormModal';
import {
  FormAddressType,
  UserShippingAddressType,
  UserTaxAddressType,
} from '@/store/type/user';
import { isUndefined } from 'lodash';

const CardAddressAccountCustomStyled = styled.div`
  .subtitle {
    font-size: 20px;
    font-weight: 700;
  }
  .caption {
    font-size: 12px;
    font-weight: 400;
  }
`;

interface CardAddressCustomProps {
  data: UserTaxAddressType | UserShippingAddressType;
}

const CardAddressAccountCustom = ({ data }: CardAddressCustomProps) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [isActiveDelete, setIsActiveDelete] = useState(false);
  const [isActiveForm, setIsActiveForm] = useState(false);
  const dispatch = useAppDispatch();

  const handleSubmitDelete = async (data: any) => {
    setLoadingDelete(true);
    const resDeleteAddress = await apiUser.deleteAddressById(data.id);
    if (resDeleteAddress.status) {
      setLoadingDelete(false);
      setIsActiveDelete(false);
      dispatch(getUserProfile());
      toast.success(resDeleteAddress.message);
    } else {
      setLoadingDelete(false);
      toast.error(resDeleteAddress.error);
    }
  };

  const handleSubmitAddress = async (values: FormAddressType) => {
    const payload = values.isTax
      ? {
          name:
            values.taxPayerType === '1'
              ? `${values.firstname} ${values.lastname}`
              : values.taxPayerName,
          phoneNumber: values.phoneNumber,
          address: values.address,
          zipCode: values.zipCode,
          province: values.province,
          district: values.district,
          subDistrict: values.subDistrict,
          isTax: values.isTax,
          taxPayerType: values.taxPayerType,
          taxId: values.taxId,
          email: values.email,
        }
      : {
          name: `${values.firstname} ${values.lastname}`,
          phoneNumber: values.phoneNumber,
          address: values.address,
          zipCode: values.zipCode,
          province: values.province,
          district: values.district,
          subDistrict: values.subDistrict,
          isTax: false,
          taxPayerType: null,
          taxId: null,
          email: values.email,
        };
    const resEditAddress = await apiUser.editAddress(data.id, payload);
    if (resEditAddress.status) {
      setIsActiveForm(false);
      dispatch(getUserProfile());
      toast.success(resEditAddress.message);
    } else {
      toast.error(resEditAddress.error);
    }
  };

  return (
    <CardAddressAccountCustomStyled>
      <div className="border-[1px] mb-6 border-[#DBE2E5] p-4 rounded-[8px] wrapper flex justify-between space-x-2">
        <div className="w-full flex flex-col">
          <span className="subtitle">{data.name}</span>
          {!isUndefined(data.taxPayerType) && (
            <span className="caption">{`${data.taxPayerType === 1 ? 'บุลคลธรรมดา' : 'นิติบุคคล'} • ${data.taxId}`}</span>
          )}

          <span className="caption">{`โทรศัพท์: ${data.phoneNumber}`}</span>
          <span className="caption">{`อีเมล: ${data.email || '-'}`}</span>
          <span className="caption">{`ที่อยู่: ${data.address} ต.${data.subDistrict} อ.${data.district} จ.${data.province} ${data.zipCode}`}</span>
        </div>
        <Image
          priority
          className="self-start cursor-pointer"
          src="/icons/edit-gray.svg"
          alt=""
          width={24}
          height={24}
          onClick={() => {
            setIsActiveForm(true);
            // setEditMode(true);
            // setIsActiveAdd(true);true
          }}
        />
        <Image
          priority
          className="self-start cursor-pointer"
          src="/icons/delete-gray.svg"
          alt=""
          width={24}
          height={24}
          onClick={() => {
            setIsActiveDelete(true);
            // setEditMode(true);
            // setIsActiveAdd(true);true
          }}
        />
      </div>
      <DeleteAddressModal
        open={isActiveDelete}
        data={data}
        handleClose={setIsActiveDelete}
        handleSubmitModal={(value) => handleSubmitDelete(value)}
        loading={loadingDelete}
      />
      <FormModal
        editData={data}
        handleClose={setIsActiveForm}
        open={isActiveForm}
        handleSubmitAddress={(value: any) => handleSubmitAddress(value)}
        isTax={!!data.taxPayerType}
        editMode={true}
        loading={false}
      />
    </CardAddressAccountCustomStyled>
  );
};

export default CardAddressAccountCustom;
