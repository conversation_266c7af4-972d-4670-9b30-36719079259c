import React, { useState } from 'react';
import styled from 'styled-components';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';
import * as yup from 'yup';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/router';

const FormPasswordCustomStyle = styled.div`
  .form-password-container {
    .show-hide-pass {
      position: absolute;
      top: 8px;
      right: 8px;
      cursor: pointer;
    }
    .image-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      .text-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .content {
          font-size: 14px;
          font-weight: 700;
        }
        .caption {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
  .content {
    font-size: 14px;
    font-weight: 400;
    &.bold {
      font-weight: 700;
    }
  }
`;

interface FormPasswordCustomProps {
  submit: (values: any) => void;
  loading: boolean;
}

const addressSchema = yup.object().shape({
  oldPassword: yup.string().required('กรุณากรอก รหัสผ่านปัจจุบัน'),
  password: yup.string().required('กรุณากรอก รหัสผ่านใหม่'),
  passwordConfirmation: yup
    .string()
    .oneOf([yup.ref('password')], 'กรุณากรอก รหัสผ่านให้ตรงกัน')
    .required('กรุณากรอก ยืนยันรหัสผ่านใหม่'),
});

const FormPasswordCustom = (props: FormPasswordCustomProps) => {
  const { submit, loading } = props;
  const router = useRouter();
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirmation, setShowPasswordConfirmation] =
    useState(false);
  return (
    <FormPasswordCustomStyle>
      <Formik
        initialValues={{
          oldPassword: '',
          password: '',
          passwordConfirmation: '',
        }}
        validationSchema={addressSchema}
        onSubmit={(values) => submit(values)}
      >
        {(_formik) => (
          <Form className="form-password-container">
            <div className="flex-row text-start mb-8">
              <label className="content bold">รหัสปัจจุบัน</label>
              <Field name="oldPassword">
                {({ field }: any) => (
                  <div className="relative">
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="mt-3.5 focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุรหัสผ่าน'}
                      type={showOldPassword ? 'texts' : 'password'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    {!showOldPassword ? (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/hide-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowOldPassword(true)}
                      />
                    ) : (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/show-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowOldPassword(false)}
                      />
                    )}
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="flex-row text-start mb-8">
              <label className="content bold">รหัสใหม่</label>
              <Field name="password">
                {({ field }: any) => (
                  <div className="relative">
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="mt-3.5 focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุรหัสผ่าน'}
                      type={showPassword ? 'texts' : 'password'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    {!showPassword ? (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/hide-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowPassword(true)}
                      />
                    ) : (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/show-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowPassword(false)}
                      />
                    )}
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="flex-row text-start mb-8">
              <label className="content bold">ยืนยันรหัสผ่าน</label>
              <Field name="passwordConfirmation">
                {({ field }: any) => (
                  <div className="relative">
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="mt-3.5 focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุรหัสผ่าน'}
                      type={showPasswordConfirmation ? 'texts' : 'password'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    {!showPasswordConfirmation ? (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/hide-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowPasswordConfirmation(true)}
                      />
                    ) : (
                      <Image
                        priority
                        className={`show-hide-pass`}
                        src="/icons/show-password-gray.svg"
                        alt=""
                        height={24}
                        width={24}
                        onClick={() => setShowPasswordConfirmation(false)}
                      />
                    )}
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="flex w-full mb-6">
              <Button
                disabled={loading}
                type="button"
                className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
                onClick={() => {
                  router.back();
                }}
              >
                ยกเลิก
              </Button>
              <Button
                disabled={loading}
                type="submit"
                className="confirm-button ml-2 w-1/2"
              >
                ตกลง
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </FormPasswordCustomStyle>
  );
};

export default FormPasswordCustom;
