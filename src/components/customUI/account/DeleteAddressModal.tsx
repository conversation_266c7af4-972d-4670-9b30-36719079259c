import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

interface DeleteAddressModalType {
  open: boolean;
  handleClose: (value: any) => void;
  handleSubmitModal: (value: any) => void;
  loading: boolean;
  data: any;
}

const DeleteAddressModal = (props: DeleteAddressModalType) => {
  const { open, data, loading, handleClose, handleSubmitModal } = props;
  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-quotation-order sm:max-w-[425px]">
        <DialogHeader className="border-b-[1px] flex">
          <DialogTitle className="text-center p-4">
            ยืนยันลบข้อมูลการจัดส่ง
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
          <div className="bg-[#FFECEC] rounded-full w-fit p-[30px]">
            <Image
              className="rounded-xl"
              priority
              src="/icons/delete.svg"
              alt=""
              width={40}
              height={40}
            />
          </div>
          <div className="text-wrapper mt-[32px] flex flex-col">
            <span className="content">
              คุณต้องการลบข้อมูลการจัดส่งใช่หรือไม่
            </span>
            <span className="content">ระบบจะทำการลบข้อมูลการจัดส่งของคุณ</span>
          </div>
        </div>
        <DialogFooter className="flex flex-row p-6">
          <Button
            disabled={loading}
            type={'button'}
            className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
            onClick={() => handleClose(false)}
          >
            ยังไม่ใช่ตอนนี้
          </Button>
          <Button
            disabled={loading}
            type={'submit'}
            onClick={() => handleSubmitModal(data)}
            className="w-1/2 ml-2"
          >
            ตกลง
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteAddressModal;
