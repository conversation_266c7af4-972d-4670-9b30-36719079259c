import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { OrderType } from '@/types/order';

interface DeleteAccountModalType {
  open: boolean;
  handleClose: (value: any) => void;
  handleSubmitModal: () => void;
  data?: OrderType;
  loading: boolean;
}

const DeleteAccountModal = (props: DeleteAccountModalType) => {
  const { open, loading, handleClose, handleSubmitModal } = props;

  return (
    <Dialog open={open} onOpenChange={(value: any) => handleClose(value)}>
      <DialogContent className="modal-quotation-order sm:max-w-[425px]">
        <DialogHeader className="border-b-[1px] flex">
          <DialogTitle className="text-center p-4">ยืนยันลบบัญชี</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
          <div className="bg-[#FFECEC] rounded-full w-fit p-[30px]">
            <Image
              className="rounded-xl"
              priority
              src="/icons/delete.svg"
              alt=""
              width={40}
              height={40}
            />
          </div>
          <div className="text-wrapper mt-[32px] flex flex-col">
            <span className="content">คุณต้องการลบบัญชี ใช่หรือไม่</span>
            <span className="content">
              ระบบจะทำการลบบัญชีของคุณและเนื้อหาทั้งหมดของคุณอย่างถาวร
            </span>
          </div>
        </div>
        <DialogFooter className="flex flex-row p-6">
          <Button
            disabled={loading}
            type={'button'}
            className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
            onClick={() => handleClose(false)}
          >
            ยังไม่ใช่ตอนนี้
          </Button>
          <Button
            disabled={loading}
            type={'submit'}
            onClick={handleSubmitModal}
            className="w-1/2 ml-2"
          >
            ตกลง
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteAccountModal;
