import React, { useEffect, useState } from 'react';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';

import * as yup from 'yup';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import apiThaiAddress from '@/services/thaiAddress';
import { Button } from '@/components/ui/button';
import { isEmpty } from 'lodash';
import { ConnectedFocusError } from 'focus-formik-error';

interface FormAddressCustomType {
  editData?: any;
  editMode?: boolean;
  setEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  submit: (values: any) => void;
  loading: boolean;
  setIsActiveAdd: React.Dispatch<React.SetStateAction<boolean>>;
}

const FormAddressCustom = ({
  editData,
  editMode,
  setEditMode,
  submit,
  loading,
  setIsActiveAdd,
}: FormAddressCustomType) => {
  const [errorZipcode, setErrorZipcode] = useState('');
  const addressSchema = yup.object().shape({
    firstname: yup.string().required('กรุณากรอก ชื่อ'),
    lastname: yup.string().required('กรุณากรอก นามสกุล'),
    phoneNumber: yup
      .string()
      .required('กรุณากรอก หมายเลขโทรศัพท์')
      .min(10, 'กรุณากรอกหมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
      .max(10, 'กรุณากรอกหมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
    email: yup
      .string()
      .email('กรุณากรอก อีเมล ให้ถูกต้อง')
      .required('กรุณากรอก อีเมล'),
    address: yup.string().required('กรุณากรอก ที่อยู่'),
    zipCode: yup
      .string()
      .required('กรุณากรอกรหัสไปรษณีย์')
      .matches(/^[0-9]+$/, 'กรุณากรอกรหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
      .min(5, 'กรุณากรอกรหัสไปรษณีย์ ให้ครบ 5 หลัก')
      .max(5, 'กรุณากรอกรหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
    province: yup.string().required('กรุณากรอก จังหวัด'),
    district: yup.string().required('กรุณากรอก อำเภอ'),
    subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
  });

  const [arraySubDistrict, setArraySubDistrict] = useState<any[]>([]);

  const handleFindZipcode = async (formik: any, keyword: string) => {
    const resZipcode = await apiThaiAddress.getZipcode(parseInt(keyword));
    if (resZipcode.status) {
      formik.setFieldValue('province', resZipcode?.data.provinceId.name);
      formik.setFieldValue('district', resZipcode?.data.districtId.name);
      setErrorZipcode('');
      const resAddress = await apiThaiAddress.getSubDistrict(
        resZipcode?.data.districtId.id,
      );
      if (resAddress.status) {
        setArraySubDistrict(resAddress?.data);
        formik.setFieldValue('subDistrict', resAddress.data[0]?.name);
      }
    } else {
      setErrorZipcode('รหัสไปรษณีย์นี้ ไม่มีในระบบ');
    }
  };

  const searchZipcode = async (formik: any, event: any) => {
    formik.setFieldValue('zipCode', event?.target?.value);
    const key = event?.target?.value;
    if (key.length === 5) {
      await handleFindZipcode(formik, key);
    } else {
      formik.setFieldValue('province', null);
      formik.setFieldValue('district', null);
      formik.setFieldValue('subDistrict', null);
    }
  };

  useEffect(() => {
    if (editMode) {
      apiThaiAddress
        .getZipcode(parseInt(editData?.zipCode))
        .then((resZipcode: any) => {
          if (resZipcode.status) {
            apiThaiAddress
              .getSubDistrict(resZipcode?.data?.districtId?.id)
              .then((res: any) => {
                if (res.status) {
                  setArraySubDistrict(res?.data);
                }
              });
          }
        });
    }
  }, []);

  return (
    <Formik
      initialValues={
        editMode
          ? {
              firstname: editData.name.split(' ')[0],
              lastname: editData.name.split(' ')[1],
              phoneNumber: editData.phoneNumber,
              address: editData.address,
              email: editData.email,
              zipCode: editData.zipCode,
              province: editData.province,
              district: editData.district,
              subDistrict: editData.subDistrict,
            }
          : {
              firstname: '',
              lastname: '',
              phoneNumber: '',
              email: '',
              address: '',
              zipCode: '',
              province: '',
              district: '',
              subDistrict: '',
            }
      }
      validateOnBlur={true}
      validationSchema={addressSchema}
      onSubmit={(values) => submit(values)}
    >
      {(formik) => (
        <Form className="form-address-custom-container">
          <ConnectedFocusError />
          <h3 className="text-head px-6 pt-6">ผู้รับ</h3>
          <div className="flex flex-row text-start mb-8 px-6">
            <div className="mr-2 w-1/2">
              <label className="text-bold text-m">ชื่อ</label>
              <Field name="firstname">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุชื่อจริง'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => (
                        <div className="text-red-500 text-xs mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="ml-2 w-1/2">
              <label className="text-bold text-m">นามสกุล</label>
              <Field name="lastname">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุนามสกุล'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => (
                        <div className="text-red-500 text-xs mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          </div>
          <div className="flex-row text-start mb-8 px-6">
            <label className="text-bold text-m">โทรศัพท์</label>
            <Field name="phoneNumber">
              {({ field }: any) => (
                <div>
                  <Input
                    type="text"
                    maxLength={10}
                    disabled={loading}
                    name={field.name}
                    className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุหมายเลขโทรศัพท์'}
                    onChange={(e: any) => {
                      const input = e.target;
                      const { value } = input;
                      formik.setFieldValue(
                        field.name,
                        value.replace(/\D/g, ''),
                      );
                    }}
                    value={field.value}
                  />
                  <ErrorMessage name={field.name}>
                    {(msg) => (
                      <div className="text-red-500 text-xs mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex-row text-start mb-8 px-6">
            <label className="text-bold text-m">อีเมล</label>
            <Field name="email">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    name={field.name}
                    className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุอีเมล'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name={field.name}>
                    {(msg) => (
                      <div className="text-red-500 text-xs mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <h3 className="text-head px-6">ที่อยู่</h3>
          <div className="flex-row text-start mb-8 px-6">
            <label className="text-bold text-m">ที่อยู่</label>
            <Field name="address">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    name={field.name}
                    className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name={field.name}>
                    {(msg) => (
                      <div className="text-red-500 text-xs mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex-row text-start mb-8 px-6">
            <label className="text-bold text-m">รหัสไปรษณีย์</label>
            <Field name="zipCode">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    type={'number'}
                    name={field.name}
                    className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0 appearance-none"
                    placeholder={'ระบุเลขรหัสไปรษณีย์'}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      if (newValue.length > 5) {
                        return true;
                      }
                      if (/^\d*$/.test(newValue)) {
                        // formik.setFieldValue(field.name, newValue);
                        searchZipcode(formik, e);
                      }
                    }}
                    value={field.value}
                  />
                  <ErrorMessage name={field.name}>
                    {(msg) => (
                      <div className="text-red-500 text-xs mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                  {errorZipcode.length > 0 ? (
                    <div className="text-red-500 text-xs mt-1">
                      {errorZipcode}
                    </div>
                  ) : (
                    ''
                  )}
                </div>
              )}
            </Field>
          </div>

          {!isEmpty(formik.values.province) && (
            <div className="flex-row text-start mb-8 px-6">
              <label className="text-bold text-m">จังหวัด</label>
              <Field name="province">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={true}
                      name={field.name}
                      className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => (
                        <div className="text-red-500 text-xs mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          )}
          {!isEmpty(formik.values.district) && (
            <div className="flex-row text-start mb-8 px-6">
              <label className="text-bold text-m">อำเภอ</label>
              <Field name="district">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={true}
                      name={field.name}
                      className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => (
                        <div className="text-red-500 text-xs mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          )}
          {!isEmpty(formik.values.subDistrict) && (
            <div className="flex-row text-start mb-8 px-6">
              <label className="text-bold text-m">ตำบล/แขวง</label>
              <Field name="subDistrict">
                {({ field }: any) => (
                  <div className="mt-[14px]">
                    <Select
                      name={field.name}
                      onValueChange={(event: any) =>
                        formik.setFieldValue(field.name, event)
                      }
                      value={field.value}
                      // defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="เลือกตำบล/แขวง" />
                      </SelectTrigger>
                      <SelectContent>
                        {arraySubDistrict.map((subDistrictData: any, index) => (
                          <SelectItem key={index} value={subDistrictData.name}>
                            {subDistrictData.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name={field.name}>
                      {(msg) => (
                        <div className="text-red-500 text-xs mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          )}
          <div className="form-tax-custom-container flex px-6 absolute bottom-0 w-full p-6 border-t-[1px] border-[#DBE2E5]">
            <Button
              disabled={loading}
              type="button"
              className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
              onClick={() => {
                if (setEditMode) {
                  setEditMode(false);
                }
                setIsActiveAdd(false);
              }}
            >
              ยกเลิก
            </Button>
            <Button
              disabled={loading}
              type="submit"
              className="confirm-button ml-2 w-1/2"
            >
              บันทึก
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default FormAddressCustom;
