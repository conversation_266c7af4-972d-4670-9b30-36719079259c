import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import styled from 'styled-components';
import { Card } from '@/components/ui/card';
import { useRouter } from 'next/router';

const CookiesModalStyled = styled(Card)<{ $router: boolean }>`
  z-index: 1;
  width: 579px;
  position: fixed;
  right: 52px;
  bottom: 52px;
  border-radius: 30px;
  background-color: #ffffff99;
  padding: 30px;
  backdrop-filter: blur(20px);
  ${({ $router }) => $router && `border-color: #212121 !important;`};
  @media only screen and (max-width: 430px) {
    width: 90%;
    height: 330px;
    left: 5%;
    bottom: 16px;
  }
  .group-title {
    margin-bottom: 24px;
    @media only screen and (max-width: 430px) {
      margin-bottom: 8px;
    }
    .title-text {
      font-size: 20px;
      font-weight: 700;
    }
  }
  .sub-text {
    font-size: 16px;
    font-weight: 400;
  }
  .group-button {
    width: 100%;
    margin-top: 32px;
    @media only screen and (max-width: 430px) {
      margin-top: 8px;
    }
    button {
      &:first-child {
        ${({ $router }) =>
          $router &&
          `background: #FFF !important; color: #000 !important; border-color: #212121 !important;`};
      }
      &:last-child {
        ${({ $router }) =>
          $router && `background: #212121 !important; color: #fff !important;`}
      }
    }
    .reject-button {
      padding: 19px 24px 19px 24px;
      gap: 10px;
      border-radius: 8px;
      background-color: transparent;
      border: 1px solid white;
      flex: 1;
      font-size: 16px;
      font-weight: 700;
    }
    .accept-button {
      flex: 1;
      padding: 19px 24px 19px 24px;
      gap: 10px;
      border-radius: 8px;
      background-color: white;
      color: black;
      font-size: 16px;
      font-weight: 700;
    }
  }
`;

interface CookiesCardProps {
  handleCookies: () => void;
  handleClose: () => void;
}

const CookiesCard = (props: CookiesCardProps) => {
  const { handleCookies, handleClose } = props;
  const router = useRouter();
  const { pathname } = router;
  const checkRouterPath = () => {
    return (
      pathname !== '/' &&
      pathname !== '/login' &&
      pathname !== '/register' &&
      pathname !== '/welcome' &&
      pathname !== '/contact'
    );
  };
  return (
    <CookiesModalStyled $router={checkRouterPath()}>
      <div className="flex flex-col">
        <div className="group-title flex items-center">
          <Image
            priority
            className="mr-2"
            src="/icons/cookie.png"
            width={48}
            height={48}
            alt=""
          />
          <span className="title-text">Cookies & Privacy</span>
        </div>
        <span className="sub-text">
          We use cookies and similar technologies to help personalize content,
          tailor and and measure ads, and provide a better experience. By
          clicking accept, you agree to this, as outlined in our Cookies Policy.
        </span>
        <Link
          className="flex items-center my-2 text-black text-lg font-bold cursor-pointer underline"
          href="/policy"
        >
          ติดต่อสอบถาม
        </Link>
      </div>
      <div className="group-button flex w-full">
        <Button className="reject-button mr-[30px]" onClick={handleClose}>
          Reject
        </Button>
        <Button className="accept-button" onClick={handleCookies}>
          Accept
        </Button>
      </div>
    </CookiesModalStyled>
  );
};

export default CookiesCard;
