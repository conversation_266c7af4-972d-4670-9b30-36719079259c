import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import dayjs from '@/utils/dayjs';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/router';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import ProgressCustom from '@/components/customUI/ProgressCustom';
import { isEmpty } from 'lodash';
import { calculatePercentage } from '@/utils/calculator';
import { dataResult, priceDiscountVat } from '@/utils/discount';

const OrderDetailStyled = styled.div`
  margin-bottom: 100px;
  .payment-wrapper {
    width: 66.66%;
    margin: 54px auto 0 auto;
    padding-bottom: 54px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    .subheader-bold {
      font-size: 36px;
      font-weight: 700;
    }
    .subtitle {
      margin-top: 24px;
      font-size: 20px;
      font-weight: 400;
    }
    .pay-button {
      margin-top: 24px;
      width: 300px;
      background-color: #ff4f00;
      font-size: 20px;
      font-weight: 700;
      &:hover {
        background-color: #ff6b33;
      }
    }
  }
  .payment-wrapper {
    width: 80%;
    margin: 54px auto 0 auto;
    padding-bottom: 54px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    .subheader-bold {
      font-size: 36px;
      font-weight: 700;
    }
    .subtitle {
      margin-top: 24px;
      font-size: 20px;
      font-weight: 400;
    }
    .pay-button {
      margin-top: 24px;
      width: 300px;
      background-color: #ff4f00;
      font-size: 20px;
      font-weight: 700;
      &:hover {
        background-color: #ff6b33;
      }
    }
  }
  .head-content-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .content {
      font-size: 14px;
      font-weight: 400;
    }
    .content.cancel {
      color: red;
      background-color: #ffecec;
      padding: 16px;
      border-radius: 8px;
      width: 100%;
    }
    .content-bold {
      font-weight: 700;
    }
    .status {
      font-size: 20px;
      font-weight: 700;
      padding: 5px 24px;
      border-radius: 40px;
      color: #f09700;
      background-color: #fff8c0;
      @media only screen and (max-width: 430px) {
        font-size: 16px;
      }
    }
    .status.success {
      background-color: #ebfeeb;
      color: #00981b;
    }
  }
  .card-container {
    width: 66.66%;
    margin: auto;
    .content-box {
      display: flex;
      margin: 24px 0;
      position: relative;
      flex-direction: column;
      .header-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #dbe2e5;
        align-items: center;
        .text-wrapper {
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
        }
        .action-wrapper {
          display: flex;
          .delete {
            margin-left: 8px;
            cursor: pointer;
            width: 24px;
            height: 24px;
          }
          .edit {
            width: 24px;
            height: 24px;
            cursor: pointer;
          }
        }
      }
      .content-wrapper {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #dbe2e5;
        padding: 16px 0 16px 16px;
        .text-container {
          display: flex;
          flex-direction: column;
          margin: 0 24px;
          justify-content: space-between;
          .price-container {
            .price {
              font-size: 14px;
              font-weight: 700;
            }
            .unit {
              margin-left: 8px;
              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
      .content-row {
        display: flex;
        .text-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 14px;
            font-weight: 400;
          }
          .amount-container {
            .amount {
              font-size: 14px;
              font-weight: 700;
            }
            .price {
              margin-left: 8px;
              font-size: 14px;
              font-weight: 400;
            }
          }
        }
      }
      .detail-wrapper {
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .show-more-container {
          .show-more-wrapper {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #dbe2e5;
            padding: 16px;
            .special-wrapper {
              display: flex;
              flex-direction: column;
              text-align: right;
              .special-text {
                font-size: 14px;
                font-weight: 700;
              }
            }
            .head-text {
              font-size: 14px;
              font-weight: 400;
            }
            .content-text {
              font-size: 14px;
              font-weight: 700;
            }
          }
          .show-more-wrapper:last-child {
            border-bottom: 0;
          }
        }
      }
      .location-container {
        display: flex;
        flex-direction: column;
        padding: 16px 0;
        .location-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .select-location-wrapper {
            align-items: center;
            display: flex;
            .content {
              font-size: 14px;
              font-weight: 700;
              margin-right: 4px;
            }
            .primary {
              color: #ff4f00;
            }
          }
          .change-address-wrapper {
            display: flex;
            cursor: pointer;
            align-items: center;
            .address {
              font-size: 14px;
              color: #ff4f00;
            }
          }
        }
        .location-text-wrapper {
          background-color: #f5f7f8;
          border-radius: 8px;
          padding: 16px;
          margin-top: 16px;
          display: flex;
          .content-bold {
            font-size: 14px;
            font-weight: 700;
          }
          .content {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
      .location-text-wrapper {
        flex-direction: column;
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .change-location {
          color: black;
          text-decoration: underline;
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          cursor: pointer;
        }
      }
    }
    .price-wrapper {
      border-top: 1px solid #dbe2e5;
      padding: 16px 0;
      .price-text-wrapper {
        display: flex;
        justify-content: space-between;
        .caption {
          font-size: 12px;
          font-weight: 400;
        }
        .subtitle {
          font-size: 20px;
          font-weight: 700;
        }
      }
    }
  }
  .user-content-wrapper {
    .user-text-wrapper {
      margin-bottom: 40px;
      .text-head-wrapper {
        padding-bottom: 24px;
        border-bottom: 1px solid #dbe2e5;
        .subheader-bold {
          font-size: 36px;
          font-weight: 700;
        }
      }
      .text-wrapper {
        display: flex;
        flex-direction: column;
        margin-top: 32px;
        .subtitle {
          font-size: 20px;
          font-weight: 700;
        }
        .content {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
  .result-container {
    margin-bottom: 100px;
    @media only screen and (max-width: 430px) {
      margin-bottom: 24px;
    }
    .result-head-wrapper {
      border-bottom: dotted 2px #212121;
      padding-bottom: 24px;
      .subheader-bold {
        font-size: 36px;
        font-weight: 700;
      }
    }

    .result-content-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 24px 0;
      border-bottom: dotted 2px #212121;
      .content {
        font-size: 14px;
        font-weight: 400;
      }
      .content-bold {
        text-align: right;
        font-size: 14px;
        font-weight: 700;
      }
    }
    .result-footer-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 28px 0;
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
      }
      .subheader-bold {
        font-size: 36px;
        font-weight: 700;
      }
    }
  }
  .pay-content-wrapper {
    @media only screen and (max-width: 430px) {
      padding-bottom: 24px;
    }
    .pay-text-wrapper {
      margin-bottom: 40px;
      .text-head-wrapper {
        padding-bottom: 24px;
        border-bottom: 1px solid #dbe2e5;
        .subheader-bold {
          font-size: 36px;
          font-weight: 700;
        }
      }
      .text-wrapper {
        display: flex;
        flex-direction: column;
        margin-top: 32px;

        .row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #dbe2e5;
          .content {
            font-size: 12px;
            font-weight: 400;
          }
          .content-bold {
            font-size: 12px;
            font-weight: 700;
          }
        }
      }
    }
  }
  @media (max-width: 576px) {
    .head-content-wrapper {
      width: 80% !important;
    }
    .card-container {
      width: 80%;
      .content-box {
        .header-wrapper {
          display: block;
          button {
            margin-top: 0.5rem;
            width: 100%;
          }
        }
      }
      .location-container {
        .location-wrapper {
          .select-location-wrapper {
            .content {
              font-size: 12px !important;
            }
          }
          .text-transport {
            span {
              font-size: 12px !important;
            }
          }
        }
      }
      .progress-bar-action {
        font-size: 12px;
        .spot.current {
          span {
            left: 60%;
          }
        }
      }
    }
  }
  @media (max-width: 425px) {
    .head-content-wrapper {
      width: 90% !important;
      .content {
        font-size: 12px;
      }
      .status {
        font-size: 14px;
        padding: 5px 16px;
      }
    }
    .card-container {
      .content-wrapper {
        padding: 16px 0 !important;
      }
      .location-container {
        .location-wrapper {
          img {
            margin-right: 5px;
          }
        }
        .location-text-wrapper {
          padding: 16px 10px !important;
          span {
            font-size: 12px !important;
          }
        }
      }
      .result-container {
        .result-head-wrapper {
          .subheader-bold {
            font-size: 30px;
          }
        }
        .result-footer-wrapper {
          .subheader-bold {
            font-size: 30px;
          }
        }
      }
      .pay-content-wrapper {
        .text-head-wrapper {
          .subheader-bold {
            font-size: 30px;
          }
        }
      }
      .user-content-wrapper {
        .text-head-wrapper {
          padding-bottom: 10px !important;
          .subheader-bold {
            font-size: 22px;
          }
        }
        .text-wrapper {
          margin-top: 14px;
          .subtitle {
            font-size: 16px;
          }
        }
      }
      .result-container {
        .result-head-wrapper {
          padding-bottom: 10px !important;
          .subheader-bold {
            font-size: 22px;
          }
        }
        .result-content-wrapper {
          padding: 16px 0 !important;
        }
        .result-footer-wrapper {
          padding: 16px 0 !important;
          .subheader-bold {
            font-size: 22px;
          }
        }
      }
      .pay-content-wrapper {
        .text-head-wrapper {
          padding-bottom: 10px !important;

          .subheader-bold {
            font-size: 22px;
          }
        }
        .text-wrapper {
          margin-top: 16px !important;
        }
      }
    }
  }
  @media (max-width: 375px) {
    .head-content-wrapper {
      width: 94% !important;
    }
    .card-container {
      width: 94% !important;
      .progress-bar-action {
        .spot.current {
          span {
            left: 80%;
          }
        }
      }
      .location-container {
        .location-wrapper {
          display: block !important;
          .select-location-wrapper {
            padding-bottom: 0.3rem;
          }
        }
      }
    }
  }
`;

interface OrderDetailCustomProps {
  data: any;
  // setFetch: React.Dispatch<React.SetStateAction<boolean>>;
  setIsActiveAcceptModal: React.Dispatch<React.SetStateAction<boolean>>;
  setDataItem: any;
}

const OrderDetailCustom = (props: OrderDetailCustomProps) => {
  const { data, setIsActiveAcceptModal, setDataItem } = props;
  const { discounts } = data;
  const router = useRouter();
  const [totalPrice, setTotalPrice] = useState();
  const [totalSum, setTotalSum] = useState<number>();
  const [valueVat, setValueVat] = useState<number>();

  const checkKeyPayment = (key: string) => key === '2';
  const checkKeyCancel = (key: string) => key === '6';
  const checkKeyProductionSuccess = (key: string) => key === '4' || key === '5';
  const checkKeyProduction = (key: string) => key === '4';
  const checkKeyVerifyPayments = (key: string) =>
    key === '3' || key === '4' || key === '5';

  useEffect(() => {
    const total = data?.orderItems?.reduce(function (sum: number, obj: any) {
      return sum + obj.item.totalPrice;
    }, 0);
    const sumVat = Number(priceDiscountVat(total, data.discounts));
    const result = dataResult(data.discounts, total, data.shippingCost, sumVat);
    setTotalSum(result?.sumTotalPrice);
    setValueVat(sumVat);
    setTotalPrice(total);
  }, [data]);
  const mapStatus = (key: string) => {
    switch (key) {
      case '1':
        return 'รอเสนอราคา';
      case '2':
        return 'รอชำระเงิน ';
      case '3':
        return 'ตรวจสอบการชำระเงิน';
      case '4':
        return 'ดำเนินการผลิต';
      case '5':
        return 'สำเร็จ';
      case '6':
        return 'ยกเลิก';
      default:
        return -1; // Return -1 for keys that don't match any case
    }
  };

  return (
    <OrderDetailStyled>
      {checkKeyPayment(data.status.toString()) && (
        <div className="payment-wrapper">
          <span className="subheader-bold">
            {`รายการสินค้ารอชำระเงินของคุณ ${numberWithCommas(totalPrice + data.shippingCost + data.vat)} บาท`}
          </span>
          <span className="subtitle">
            สามารถกดปุ่ม “ชำระเงิน”เพื่อไปยังหน้าชำระเงิน
          </span>
          <Button
            className="pay-button"
            onClick={() => router.push(`/payment/${data.id}`)}
          >
            <Image
              className="rounded-xl"
              priority
              src="/icons/receipt_long.svg"
              alt=""
              width={24}
              height={24}
            />
            ชำระเงิน
          </Button>
        </div>
      )}
      {checkKeyCancel(data.status.toString()) ? (
        <div className="head-content-wrapper">
          <span className="content cancel">
            <span className="content-bold">ยกเลิกรายการ</span>
            {` เมื่อ 
                ${dayjs(data.createdDate).format('D MMM YYYY, H:mm')} น., ${data.description}`}
          </span>
        </div>
      ) : (
        <div className="head-content-wrapper">
          <span className="content">
            {`วันที่สั่งซื้อ: 
                ${dayjs(data.createdDate).format('D MMM YYYY, H:mm')}`}
          </span>
          <span className={`status ${data.status === 5 ? 'success' : ''}`}>
            {mapStatus(data.status.toString())}
          </span>
        </div>
      )}
      <div className="card-container">
        {data.orderItems.map((dataItem: any, index: number) => (
          <div className="content-box" key={index}>
            <div className="header-wrapper">
              <div className="text-wrapper">
                <span className="subtitle">{dataItem?.item?.model?.name}</span>
              </div>
              {checkKeyProduction(data.status.toString()) &&
                dataItem?.shippingDetail?.trackingNumber &&
                dataItem?.status === 4 && (
                  <Button
                    className="bg-white text-black border-[#DBE2E5] border-[1px] hover:bg-white hover:text-black"
                    onClick={() => {
                      setDataItem(dataItem);
                      setIsActiveAcceptModal(true);
                    }}
                  >
                    ตรวจสอบและยอมรับสินค้า
                  </Button>
                )}
            </div>
            {checkKeyProductionSuccess(data.status.toString()) && (
              <ProgressCustom
                key={index}
                isArtwork={dataItem.item.isArtwork}
                shipping={dataItem.shippingDetail.shipping}
                status={dataItem.status}
              />
            )}
            <div className="content-wrapper">
              <div className="content-row">
                <Image
                  priority
                  className="w-[80px] h-[80px] mr-4"
                  src={
                    dataItem.item.model.imageUrl
                      ? dataItem.item.model.imageUrl
                      : '/images/default-image.png'
                  }
                  alt=""
                  width={80}
                  height={80}
                />
                <div className="text-wrapper">
                  <span className="subtitle">
                    {`ขนาด ${dataItem.item?.width} x ${dataItem.item?.length} x
                    ${dataItem.item?.height} mm, ${dataItem.item?.material.name} •
                    ${dataItem.item?.material.gram}
                    แกรม`}
                  </span>
                  <div className="amount-container">
                    <span className="amount">
                      x {numberWithCommas(dataItem?.item?.amount)}
                    </span>
                    <span className="price">
                      ({numberWithCommas(dataItem?.item?.unitPrice)}/ชิ้น)
                    </span>
                  </div>
                </div>
              </div>
              <div className="price-container font-bold">
                <span className="price">
                  {numberWithCommas(dataItem.item?.totalPrice)}
                </span>
              </div>
            </div>
            <div className="detail-wrapper">
              <Accordion
                type={'single'}
                key={index}
                collapsible
                className="space-y-2"
              >
                <AccordionItem value="item-1">
                  <AccordionTrigger className="px-4 justify-start hover:no-underline">
                    <span className="content pr-2">รายละเอียดสินค้า</span>
                  </AccordionTrigger>
                  <AccordionContent asChild className="show-more-container ">
                    <div className="show-more-wrapper">
                      <span className="head-text ">ขนาด</span>
                      <span className="content-text ">
                        {dataItem.item.width} x {dataItem.item.length} x{' '}
                        {dataItem.item.height} mm
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">วัสดุ</span>
                      <span className="content-text ">
                        {dataItem.item?.material?.name} •{' '}
                        {dataItem.item?.material?.gram} แกรม
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">พิมพ์</span>
                      <span className="content-text">
                        {dataItem.item.printing === 1 ? 'ด้านหน้า' : ''}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เคลือบ</span>
                      <span className="content-text">
                        {dataItem.item.coating && dataItem.item.coating.name}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เทคนิคพิเศษ</span>
                      <span className="special-wrapper">
                        {dataItem.item.specialTechnics.length > 0
                          ? dataItem.item.specialTechnics.map(
                              (data: any, index: number) => (
                                <span
                                  className="special-text"
                                  key={index}
                                >{`${data.name} • ขนาด ${data.width}x${data.height} mm`}</span>
                              ),
                            )
                          : '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">อาร์ตเวิร์ก</span>
                      <span className="content-text ">
                        {dataItem.item.isArtwork
                          ? `ใช้บริการออกแบบ`
                          : 'ไม่ใช้อาร์ตเวิร์ก'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">
                        ลิงก์ไฟล์งานอาร์ตเวิร์ค
                      </span>
                      <span className="content-text ">
                        {dataItem.item.artworkUrl
                          ? dataItem.item.artworkUrl
                          : '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">ตัวอย่างสินค้า</span>
                      <span className="content-text ">
                        {dataItem.item.productDemo === 1
                          ? 'Soft and Online Proof'
                          : 'Mockup Proof'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">หมายเหตุ</span>
                      <span className="content-text ">
                        {dataItem.item.description
                          ? dataItem.item.description
                          : '-'}
                      </span>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
            <div className="location-container">
              <div className="location-wrapper">
                <div className="select-location-wrapper">
                  <Image
                    priority
                    className="mr-3"
                    src="/icons/share_location.png"
                    alt=""
                    width={24}
                    height={24}
                  />

                  <span className="content mr-3">การจัดส่ง :</span>
                  <span className="content primary">
                    {dataItem?.shippingDetail?.shipping === 1
                      ? 'รับด้วยตัวเอง'
                      : 'จัดส่งขนส่งเอกชน'}
                  </span>
                </div>
                {dataItem.shippingDetail?.shipping === 1 &&
                (dataItem?.status === 4 || dataItem?.status === 5) ? (
                  <div
                    className="text-transport flex items-center cursor-pointer"
                    onClick={() => {
                      router.push(
                        `/my-orders/order-detail/shipping-detail/${dataItem.id}`,
                      );
                    }}
                  >
                    <Image
                      priority
                      className="mr-3"
                      src="/icons/package.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    <span className="text-[14px] text-[#212121] underline">
                      ข้อมูลการรับสินค้า (รับเอง)
                    </span>
                  </div>
                ) : (
                  (dataItem?.status === 4 || dataItem?.status === 5) &&
                  dataItem?.shippingDetail?.deliveryDate && (
                    <div
                      className="text-transport flex items-center cursor-pointer"
                      onClick={() => {
                        router.push(
                          `/my-orders/order-detail/shipping-detail/${dataItem.id}`,
                        );
                      }}
                    >
                      <Image
                        priority
                        className="mr-3"
                        src="/icons/local_shipping.svg"
                        alt=""
                        width={24}
                        height={24}
                      />
                      <span className="text-[14px] text-[#212121] underline">
                        ข้อมูลการจัดส่ง (เอกชน)
                      </span>
                    </div>
                  )
                )}
              </div>
              {dataItem?.shippingDetail.shipping === 1 ? (
                <div className="location-text-wrapper">
                  <span className="content-bold w-2/3">
                    Luca block Co., Ltd • 065 712 7411
                  </span>
                  <span className="content">
                    6 ซอยบางแค 12 บางแค บางแค กรุงเทพมหานคร 10160, ประเทศไทย
                  </span>
                </div>
              ) : (
                <div className="location-text-wrapper">
                  <span className="content-bold w-2/3">
                    {`${dataItem.shippingDetail.recipientName} • ${dataItem.shippingDetail.phoneNumber}`}
                  </span>
                  <span className="content">
                    {`${dataItem.shippingDetail.address} ${dataItem.shippingDetail.subDistrict} ${dataItem.shippingDetail.district} 
                    ${dataItem.shippingDetail.province} ${dataItem.shippingDetail.zipCode} ประเทศไทย`}
                  </span>
                </div>
              )}
            </div>
            <div className="price-wrapper">
              <div className="price-text-wrapper">
                <span className="caption">ค่าจัดส่ง</span>
                <span className="caption">ราคารวม</span>
              </div>
              <div className="price-text-wrapper">
                <span className="subtitle">
                  {numberWithCommas(dataItem.shippingDetail.shippingCost)}
                </span>
                <span className="subtitle text-primary-main">
                  {numberWithCommas(
                    dataItem.item.totalPrice +
                      dataItem.shippingDetail.shippingCost,
                  )}
                </span>
              </div>
            </div>
          </div>
        ))}
        <div className="user-content-wrapper">
          <div className="user-text-wrapper">
            <div className="text-head-wrapper">
              <span className="subheader-bold">ข้อมูลผู้สั่งซื้อ</span>
            </div>
            <div className="text-wrapper">
              <span className="subtitle">{data.customer.fullName}</span>
              <span className="content">{`เบอร์โทรศัพท์: ${data.customer.phoneNumber}`}</span>
            </div>
          </div>
          <div className="user-text-wrapper">
            <div className="text-head-wrapper">
              <span className="subheader-bold">ข้อมูลสำหรับขอใบกำกับภาษี</span>
            </div>
            <div className="text-wrapper">
              <span className="subtitle">{data.taxDetail.taxPayerName}</span>
              <span className="content">{`${data.taxDetail.taxPayerType === 1 ? 'บุลคลธรรมดา' : 'นิติบุคคล'} • ${data.taxDetail.taxId}`}</span>
              <span className="content">{`โทรศัพท์: ${data.taxDetail.phoneNumber}`}</span>
              <span className="content">{`E-mail: ${data.taxDetail.email}`}</span>
              <span className="content">{`ที่อยู่:  ${data.taxDetail.address} ต.${data.taxDetail.subDistrict} อ.${data.taxDetail.district}
                จ.${data.taxDetail.province} ${data.taxDetail.zipCode}`}</span>
            </div>
          </div>
        </div>
        <div className="result-container">
          <div className="result-head-wrapper">
            <span className="subheader-bold">สรุปคำสั่งซื้อ</span>
          </div>
          <div className="result-content-wrapper">
            <div className="flex flex-col">
              <span className="content">ราคาสินค้าทั้งหมด</span>
              <span className="content">ภาษีมูลค่าเพิ่ม 7%</span>
              <span className="content">ค่าจัดส่ง</span>
              {!isEmpty(discounts) &&
                discounts.map((item: any, index: number) => {
                  return (
                    <span className="content" key={index}>
                      {item.title}
                    </span>
                  );
                })}
            </div>
            <div className="flex flex-col">
              <span className="content-bold">
                {numberWithCommas(totalPrice)}
              </span>
              <span className="content-bold">{numberWithCommas(valueVat)}</span>
              <span className="content-bold">
                {numberWithCommas(data.shippingCost)}
              </span>
              {!isEmpty(discounts) &&
                discounts.map((item: any, index: number) => {
                  const { percentage, maxDiscount } = item;
                  if (!totalPrice) return null;
                  const priceDiscount =
                    calculatePercentage(totalPrice, percentage) > maxDiscount
                      ? maxDiscount
                      : calculatePercentage(totalPrice, percentage);
                  return (
                    <span
                      style={{ color: '#E5200C' }}
                      className="content-bold"
                      key={index}
                    >
                      - ฿{numberWithCommas(priceDiscount)}
                    </span>
                  );
                })}
            </div>
          </div>
          <div className="result-footer-wrapper">
            <div>
              <span className="subtitle-bold">ยอดชำระเงินทั้งหมด</span>
            </div>
            <div>
              <span className="subheader-bold">
                {numberWithCommas(totalSum, 2)}
              </span>
            </div>
          </div>
        </div>
        {checkKeyVerifyPayments(data.status.toString()) &&
          data.paymentTransaction.length > 0 && (
            <div className="pay-content-wrapper ">
              <div className="pay-text-wrapper">
                <div className="text-head-wrapper">
                  <span className="subheader-bold">ข้อมูลการชำระเงิน</span>
                </div>
                <div className="text-wrapper">
                  <div className="row">
                    <span className="content">จำนวนเงินที่โอน</span>
                    <span className="content-bold">
                      {numberWithCommas(
                        data.paymentTransaction[
                          data.paymentTransaction.length - 1
                        ].paymentAmount,
                      )}
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">ช่องทางการชำระเงิน</span>
                    <span className="content-bold">
                      {
                        data.paymentTransaction[
                          data.paymentTransaction.length - 1
                        ].paymentType
                      }
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">ชื่อบัญชี</span>
                    <span className="content-bold">
                      {
                        data.paymentTransaction[
                          data.paymentTransaction.length - 1
                        ].bankAccountName
                      }
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">ธนาคาร</span>
                    <span className="content-bold">
                      {data.paymentTransaction[
                        data.paymentTransaction.length - 1
                      ].bankName
                        ? data.paymentTransaction[
                            data.paymentTransaction.length - 1
                          ].bankName
                        : '-'}
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">เลขที่บัญชี</span>
                    <span className="content-bold">
                      {
                        data.paymentTransaction[
                          data.paymentTransaction.length - 1
                        ].bankAccountNumber
                      }
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">วันเวลาโอนเงิน</span>
                    <span className="content-bold">
                      {data.paymentTransaction[
                        data.paymentTransaction.length - 1
                      ].dateTime
                        ? dayjs(
                            data.paymentTransaction[
                              data.paymentTransaction.length - 1
                            ].dateTime,
                          ).format('D MMM YYYY, H:mm')
                        : '-'}
                    </span>
                  </div>
                  <div className="row">
                    <span className="content">หมายเหตุ</span>
                    <span className="content-bold">
                      {data.paymentTransaction[
                        data.paymentTransaction.length - 1
                      ].description
                        ? data.paymentTransaction[
                            data.paymentTransaction.length - 1
                          ].description
                        : '-'}
                    </span>
                  </div>
                  <div className="row pt-2">
                    <span className="content">หลักฐานการโอนเงิน</span>
                    <span className="content-bold cursor-pointer">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Image
                            priority
                            className="rounded-[8px] w-[40px] h-[40px]"
                            src={
                              data.paymentTransaction[
                                data.paymentTransaction.length - 1
                              ]
                                ? data.paymentTransaction[
                                    data.paymentTransaction.length - 1
                                  ].slipUrl
                                : '/images/default-image.png'
                            }
                            alt=""
                            width={40}
                            height={40}
                          />
                        </DialogTrigger>
                        <DialogContent className="slip-wrapper max-w-[400px] tablet:max-w-[70%] desktop:max-w-[50%]">
                          <DialogHeader className="p-6 border-b-[1px] border-[#dbe2e5]">
                            <DialogTitle>สลิปหลักฐานการโอนเงิน</DialogTitle>
                          </DialogHeader>
                          <div className="w-full h-full max-h-[70vh] p-6 pt-0">
                            <Image
                              priority
                              className="rounded-[8px] w-full h-full object-contain"
                              alt=""
                              width={450}
                              height={550}
                              src={
                                data.paymentTransaction[
                                  data.paymentTransaction.length - 1
                                ]
                                  ? data.paymentTransaction[
                                      data.paymentTransaction.length - 1
                                    ].slipUrl
                                  : '/images/default-image.png'
                              }
                            />
                          </div>
                        </DialogContent>
                      </Dialog>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
      </div>
    </OrderDetailStyled>
  );
};

export default OrderDetailCustom;
