import React, { useEffect } from 'react';
import styled from 'styled-components';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import Image from 'next/image';
import { AddressType } from '@/types/address';

const CardAddressCustomStyled = styled.div`
  .selected {
    border-color: black;
  }
`;

interface CardAddressCustomProps {
  data: AddressType[];
  selected: number;
  setSelected: React.Dispatch<React.SetStateAction<number>>;
  setSelectedAction: React.Dispatch<React.SetStateAction<any>>;
  setIsActiveAdd: React.Dispatch<React.SetStateAction<boolean>>;
  setEditMode: React.Dispatch<React.SetStateAction<boolean>>;
}

const CardAddressCustom = ({
  data,
  selected,
  setSelected,
  setSelectedAction,
  setIsActiveAdd,
  setEditMode,
}: CardAddressCustomProps) => {
  const onSelect = (id: string) => {
    const result = data.find((item: AddressType) => {
      return item.id === parseInt(id);
    });
    setSelected(parseInt(id));
    setSelectedAction(result);
  };

  useEffect(() => {
    setSelected(data[0].id);
    setSelectedAction(data[0]);
  }, []);

  return (
    <CardAddressCustomStyled>
      <RadioGroup onValueChange={onSelect} defaultValue={String(data[0].id)}>
        {data.map((address: AddressType, index: number) => (
          <div
            className={`${selected === address.id ? 'selected' : ''} border-[1px] mb-6 border-[#DBE2E5] p-4 rounded-[8px] wrapper flex justify-between space-x-2`}
            key={index}
          >
            <RadioGroupItem value={String(address.id)} />
            <div className="w-full flex flex-col">
              <span>ชื่อ-นามสกุล: {address.name}</span>
              <span>โทรศัพท์: {address.phoneNumber}</span>
              <span>อีเมล: {address.email}</span>
              <span>
                ที่อยู่: {`${address.address} ต.${address.subDistrict}`}
              </span>
              <span>{`อ.${address.district}  จ.${address.province} ${address.zipCode}`}</span>
            </div>
            <Image
              priority
              className="self-start cursor-pointer"
              src="/icons/edit.svg"
              alt=""
              width={24}
              height={24}
              onClick={() => {
                setSelectedAction(address);
                setEditMode(true);
                setIsActiveAdd(true);
              }}
            />
          </div>
        ))}
      </RadioGroup>
    </CardAddressCustomStyled>
  );
};

export default CardAddressCustom;
