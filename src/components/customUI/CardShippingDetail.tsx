import React from 'react';
import { Card } from '@/components/ui/card';
import styled from 'styled-components';
import Image from 'next/image';
import { handleCopyToClipboard } from '@/utils/copyContent';
import { ShippingType } from '@/types/shipping';
import dayjs from '@/utils/dayjs';
import { numberWithCommas } from '@/utils/numberFormat';

const CardShippingDetailStyled = styled.div`
  //w-2/3 m-auto mt-6
  width: 66.66%;
  margin: 24px auto 80px auto;
  @media only screen and (max-width: 430px) {
    width: 90%;
  }
  .subtitle {
    font-size: 20px;
    font-weight: 700;
  }
  .head-wrapper {
    @media only screen and (max-width: 430px) {
      flex-direction: column;
    }
  }
  .text-wrapper {
    @media only screen and (max-width: 430px) {
      margin: 8px 0;
    }
    .content {
      font-size: 14px;
      font-weight: 400;
      @media only screen and (max-width: 430px) {
        word-break: break-word;
      }
      &.bold {
        font-weight: 700;
      }
    }
  }
  .address-wrap {
    width: 50%;
    margin-right: 8px;
    @media only screen and (max-width: 430px) {
      width: 100%;
      margin: 0;
      margin-bottom: 24px;
    }
  }
  .tax-wrap {
    width: 50%;
    margin-left: 8px;
    @media only screen and (max-width: 430px) {
      width: 100%;
      margin: 0;
    }
  }
  .order-container {
    margin-top: 48px;
    @media only screen and (max-width: 430px) {
      margin-top: 24px;
      padding-bottom: 24px;
    }
    .order-text-wrapper {
      margin-bottom: 40px;
      @media only screen and (max-width: 430px) {
        margin-bottom: 24px;
      }
      .text-head-wrapper {
        padding-bottom: 24px;
        border-bottom: 1px solid #dbe2e5;
        .subheader-bold {
          font-size: 36px;
          font-weight: 700;
        }
      }
      .text-wrapper {
        display: flex;
        flex-direction: column;
        margin-top: 32px;
        .subtitle {
          font-size: 20px;
          font-weight: 700;
        }
        .content {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
    .content-wrapper {
      .content-row {
        display: flex;
        .text-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
          .content {
            font-size: 14px;
            font-weight: 400;
          }
          .amount-container {
            .amount {
              font-size: 14px;
              font-weight: 700;
            }
            .price {
              font-size: 14px;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
`;

interface CardShippingDetailType {
  shippingData: ShippingType;
}

const CardShippingDetail = (props: CardShippingDetailType) => {
  const { shippingData } = props;

  return (
    shippingData && (
      <CardShippingDetailStyled>
        <Card className="head-wrapper p-6 flex justify-between mb-6">
          <div className="text-wrapper flex flex-col justify-between">
            <span className="content">เลขที่การสั่งซื้อ</span>
            <span className="subtitle">{shippingData.orderNumber}</span>
          </div>
          <div className="text-wrapper flex flex-col justify-between">
            <span className="content">
              {shippingData?.shippingDetail?.shipping === 1
                ? 'รับสินค้าได้เมื่อ'
                : 'วันเวลาจัดส่ง'}
            </span>
            <span className="subtitle">
              {dayjs(shippingData?.shippingDetail?.deliveryDate).format(
                'D MMM YYYY, H:mm',
              )}
            </span>
          </div>
          <div className="text-wrapper flex flex-col justify-between">
            <span className="content">
              {shippingData?.shippingDetail?.shipping === 1
                ? 'รับสินค้าสำเร็จเมื่อ'
                : 'Tracking Number'}
            </span>
            <span className="subtitle flex desktop:justify-center">
              {shippingData?.shippingDetail?.shipping === 1
                ? shippingData?.shippingDetail?.receivedDate
                  ? dayjs(shippingData?.shippingDetail?.receivedDate).format(
                      'D MMM YYYY, H:mm',
                    )
                  : '-'
                : shippingData?.shippingDetail?.trackingNumber}
              {shippingData?.shippingDetail?.shipping === 2 && (
                <Image
                  className="cursor-pointer ml-2"
                  src="/icons/content_copy.svg"
                  alt={''}
                  width={24}
                  height={24}
                  onClick={() => handleCopyToClipboard('TH247869620923X')}
                />
              )}
            </span>
          </div>
          <div className="text-wrapper flex flex-col justify-between">
            <span className="content">
              {shippingData?.shippingDetail?.shipping === 1
                ? 'ผู้รับสินค้า'
                : 'การจัดส่งของขนส่ง'}
            </span>
            <span className="subtitle flex desktop:justify-center">
              {shippingData?.shippingDetail?.shipping === 1
                ? shippingData?.shippingDetail?.recipientName
                  ? shippingData?.shippingDetail?.recipientName
                  : '-'
                : shippingData?.shippingDetail?.shippingName}
            </span>
          </div>
        </Card>
        {shippingData?.shippingDetail?.shipping === 1 && (
          <Card className="p-6 flex justify-between mb-6">
            <div className="text-wrapper flex flex-col justify-between">
              <span className="content bold">หมายเหตุรับสินค้า</span>
              <span className="content">
                {shippingData?.shippingDetail?.receivedDescription || '-'}
              </span>
            </div>
          </Card>
        )}
        <div className="flex flex-col tablet:flex-row">
          <Card className="address-wrap ">
            <div className="border-b-[1px] border-[#DBE2E5] p-6">
              <span className="subtitle">ข้อมูลการจัดส่ง</span>
            </div>
            <div className="text-wrapper flex flex-col p-6">
              {shippingData?.shippingDetail?.shipping === 1 ? (
                <>
                  <span className="subtitle">
                    Luca block Co., Ltd • 065 712 7411
                  </span>
                  <span className="content">Email: <EMAIL></span>
                  <span className="content">โทรศัพท์: 065 712 7411</span>
                  <span className="content">
                    ที่อยู่: 6 ซอยบางแค 12 บางแค บางแค กรุงเทพมหานคร 10160,
                    ประเทศไทย
                  </span>
                </>
              ) : (
                <>
                  <span className="subtitle">{`${shippingData.shippingDetail?.recipientName}`}</span>
                  <span className="content">{`Email: ${shippingData.shippingDetail?.email ? shippingData.shippingDetail?.email : '-'}`}</span>
                  <span className="content">{`โทรศัพท์: ${shippingData.shippingDetail?.phoneNumber}`}</span>
                  <span className="content">
                    {`ที่อยู่: ${shippingData.shippingDetail?.address} ${shippingData.shippingDetail?.subDistrict} ${shippingData.shippingDetail?.district} 
                    ${shippingData.shippingDetail?.province} ${shippingData.shippingDetail?.zipCode} ประเทศไทย`}
                  </span>
                </>
              )}
            </div>
          </Card>
          <Card className="tax-wrap ">
            <div className="border-b-[1px] border-[#DBE2E5] p-6">
              <span className="subtitle">ข้อมูลสำหรับขอใบกำกับภาษี</span>
            </div>
            <div className="text-wrapper flex flex-col p-6">
              <span className="subtitle">
                {shippingData.taxDetail?.taxPayerName}
              </span>
              <span className="content">{`${shippingData.taxDetail?.taxPayerType === 1 ? 'บุลคลธรรมดา' : 'นิติบุคคล'} • ${shippingData.taxDetail?.taxId}`}</span>
              <span className="content">{`โทรศัพท์: ${shippingData.taxDetail?.phoneNumber}`}</span>
              <span className="content">{`E-mail: ${shippingData.taxDetail?.email}`}</span>
              <span className="content">{`ที่อยู่:  ${shippingData.taxDetail?.address} ต.${shippingData.taxDetail?.subDistrict} อ.${shippingData.taxDetail?.district} 
                    จ.${shippingData.taxDetail?.province} ${shippingData.taxDetail?.zipCode}`}</span>
            </div>
          </Card>
        </div>
        <div className="order-container">
          <div className="order-text-wrapper">
            <div className="text-head-wrapper">
              <span className="subheader-bold">รายการสินค้า</span>
            </div>
          </div>
          <div className="content-wrapper">
            <div className="content-row">
              <Image
                priority
                className="mr-4 rounded-lg"
                src={
                  shippingData.item?.model.imageUrl
                    ? shippingData.item?.model.imageUrl
                    : '/images/default-image.png'
                }
                alt=""
                width={92}
                height={92}
              />
              <div className="text-wrapper">
                <div className="flex flex-col">
                  <span className="subtitle">
                    {shippingData.item?.model.name}
                  </span>
                  <span className="content">
                    {`ขนาด ${shippingData.item?.width} x ${shippingData.item?.length} x 
                        ${shippingData.item?.height} mm, ${shippingData.item?.material.name} • 
                        ${shippingData.item?.material.gram} 
                        แกรม ...`}
                  </span>
                </div>
                <div className="amount-container">
                  <span className="amount">
                    x {numberWithCommas(shippingData.item?.amount)}
                  </span>
                  <span className="price">
                    ({numberWithCommas(shippingData.item?.unitPrice)}/ชิ้น)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardShippingDetailStyled>
    )
  );
};

export default CardShippingDetail;
