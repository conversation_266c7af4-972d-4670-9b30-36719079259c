import React from 'react';
import styled from 'styled-components';

const FooterStyle = styled.div`
  .zone-footer {
    font-size: 12px;
    background-color: #f5f5f5;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    //position: absolute;
    //bottom: 0;
    //left: 0;
    width: 100%;
    > span {
      color: #9e9e9e;
      &:nth-child(2) {
        color: #212121 !important;
      }
    }
  }
  @media (max-width: 768px) {
    .zone-footer {
      padding: 0.6rem 3rem;
      flex-wrap: wrap;
      justify-content: center;
      span {
        &:first-child {
          order: 1;
          padding-top: 0.2rem;
        }
      }
    }
  }
  @media (max-width: 576px) {
    .zone-footer {
      padding: 0.6rem 1rem;
      flex-wrap: wrap;
      justify-content: center;
      span {
        &:first-child {
          order: 1;
          padding-top: 0.2rem;
        }
      }
    }
  }
  @media (min-width: 426px) {
    .zone-footer {
      .text-footer-mobile {
        display: none;
      }
    }
  }
  @media (max-width: 425px) {
    .zone-footer {
      > span {
        display: none;
      }
      .text-footer-mobile {
        text-align: center;
        span {
          color: #9e9e9e !important;
          &.text-dark {
            color: #212121 !important;
          }
        }
      }
    }
  }
`;
const Footer = () => {
  return (
    <FooterStyle>
      <div className={'zone-footer'}>
        <span>
          © 2024 © 2023 Digiboxs. All rights reserved. • Cookie • Privacy
          Policy
        </span>
        <span>Call Center +66 657127411 • Email <EMAIL></span>
        <div className={'text-footer-mobile'}>
          <div>
            <span className={'text-dark'}>Call Center +66 657127411</span>
          </div>
          <div>
            <span className={'text-dark'}>Email <EMAIL></span>
          </div>
          <div>
            <span>Cookie • Privacy Policy</span>
          </div>
          <div>
            <span>© 2024 © 2023 Digiboxs. All rights reserved.</span>
          </div>
        </div>
      </div>
    </FooterStyle>
  );
};

export default Footer;
