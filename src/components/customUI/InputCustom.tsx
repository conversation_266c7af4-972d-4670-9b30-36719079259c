import React from 'react';
import { Input } from '@/components/ui/input';

interface InputCustomProps {
  type: string;
  min?: number;
  value: number;
  label: string;
  classNameLabel?: string;
  classNameInput?: string;
  onChange: (event?: any) => void;
  onBlur?: (event?: any) => void;
  name: string;
  setSelected?: React.Dispatch<React.SetStateAction<any>>;
}

const InputCustom = (props: InputCustomProps) => {
  const {
    type,
    min,
    label,
    classNameInput,
    classNameLabel,
    value,
    name,
    onChange,
    onBlur,
  } = props;

  return (
    <div className="flex flex-col mt-6">
      <div className="flex justify-center items-center">
        <label
          className={`h-full py-2 px-3 border-[#EEEEEE] border-t-[1px] border-b-[1px] border-l-[1px] border-r-[1px] rounded-l-[8px] ${
            label === 'W'
              ? 'text-primary-main'
              : label === 'L'
                ? 'text-secondary-main'
                : 'text-success-dark'
          } ${classNameLabel}`}
        >
          {label}
        </label>
        <Input
          name={name}
          value={value}
          onChange={onChange}
          className={`h-full border-1-[#EEEEEE] border-l-0 rounded-r-[8px] rounded-l-[0] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0 ${classNameInput}`}
          placeholder={'0.00'}
          type={type}
          min={min}
          defaultValue={value}
          onBlur={onBlur}
        />
      </div>
      <label className="text-xs text-gray-400 mt-2 ">
        {label === 'W'
          ? 'Width: Min 20 mm'
          : label === 'L'
            ? 'Length: Min 20 mm'
            : 'Height: Min 20 mm '}
      </label>
    </div>
  );
};

export default InputCustom;
