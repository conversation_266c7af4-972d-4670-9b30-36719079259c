import styled from 'styled-components';

const ModalCouponStyle = styled.div`
  .zone-modal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    background: #fff;
    color: #212121;
    border-radius: 16px;
    header {
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #dbe2e5;
      .div-empty {
        width: 40px;
      }
      h2 {
        font-size: 20px !important;
        font-weight: 700;
      }
      button {
        min-width: unset !important;
        min-height: unset !important;
        color: #212121;
      }
    }
    .child-card-coupon {
      &.isHide {
        filter: grayscale(1);
      }
    }
    > .detail {
      font-size: 14px;
      padding: 1rem 2rem 3rem 2rem;
      display: grid;
      gap: 20px;
      > div {
        //margin-top: 1rem;
        label {
          font-weight: 700;
        }
      }
    }
  }
  @media (max-width: 768px) {
    .zone-modal {
      width: 100%;
      height: 100%;
      border-radius: 0;
      top: 0;
      left: 0;
      transform: unset;
      header {
        padding: 1rem 0.5rem;
        h2 {
          font-size: 18px !important;
        }
      }
      > .detail {
        gap: 40px;
        padding: 2rem;
      }
    }
  }
  @media (max-width: 425px) {
    .zone-modal {
      header {
        padding: 1rem 0.5rem;
        h2 {
          font-size: 18px !important;
        }
      }
      > .detail {
        gap: 40px;
        padding: 1rem;
      }
    }
  }
`;

export default ModalCouponStyle;
