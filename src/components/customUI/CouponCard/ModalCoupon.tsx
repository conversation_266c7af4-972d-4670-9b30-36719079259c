import React from 'react';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import Button from '@mui/material/Button';
import ModalCouponStyle from '@/components/customUI/CouponCard/ModalCouponStyle';
import CloseIcon from '@mui/icons-material/Close';
import CouponCard from '@/components/customUI/CouponCard/CouponCard';
import CouponStyle from '@/styles/CouponStyle';
import dayjs from 'dayjs';
import 'dayjs/locale/th';

type Props = {
  discountId: number;
  isShow: boolean;
  onClose: (value: boolean) => void;
  name: string;
  description: string;
  remainingRights: number;
  discountCategory?: any;
  discountCode?: string;
  startDate?: any;
  endDate?: string;
  isCollected?: boolean;
  fetchDataDiscount?: () => void;
  isUse?: boolean;
  isHide?: boolean;
};

const ModalCoupon = ({
  discountId,
  isShow,
  onClose,
  name,
  description,
  remainingRights,
  discountCategory,
  discountCode,
  startDate,
  endDate,
  isCollected,
  fetchDataDiscount,
  isUse,
  isHide,
}: Props) => {
  dayjs.locale('th');
  return (
    <Modal open={isShow} onClose={() => onClose(false)}>
      <ModalCouponStyle>
        <Box className={'zone-modal'}>
          <header>
            <div className={'div-empty'} />
            <h2>เงื่อนไขคูปองส่วนลดสินค้า</h2>
            <Button variant="text" onClick={() => onClose(false)}>
              <CloseIcon />
            </Button>
          </header>
          <CouponStyle>
            <div className={`child-card-coupon inModal ${isHide && 'isHide'}`}>
              <CouponCard
                discountId={discountId}
                name={name}
                description={description}
                remainingRights={remainingRights}
                discountCategory={discountCategory}
                isCollected={isCollected}
                fetchDataDiscount={fetchDataDiscount}
                isUse={isUse}
                isHide={isHide}
              />
            </div>
          </CouponStyle>
          <div className={'detail'}>
            <div className={'timeLimit'}>
              <label>เวลาที่ใช้ได้</label>
              <p>{`${dayjs(startDate).isValid() ? dayjs(startDate).format('D MMM YYYY, HH:mm') : ''} - ${dayjs(endDate).isValid() ? dayjs(endDate).format('D MMM YYYY, HH:mm') : ''}`}</p>
            </div>
            <div className="coupon-detail">
              <label>คูปอง</label>
              <p>{name}</p>
            </div>
            <div className="pass-coupon">
              <label>รหัสคูปอง</label>
              <p>{discountCode}</p>
            </div>
            <div className="coupon-detail">
              <label>รายละเอียด</label>
              <p>{description || '-'}</p>
            </div>
          </div>
        </Box>
      </ModalCouponStyle>
    </Modal>
  );
};

export default ModalCoupon;
