import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid2';
import Image from 'next/image';
import ModalCoupon from '@/components/customUI/CouponCard/ModalCoupon';
import Button from '@mui/material/Button';
import { getCookie } from 'cookies-next';
import { toast } from 'sonner';
import { useRouter } from 'next/router';
import apiDiscount from '@/services/discount';
import { isUndefined } from 'lodash';
import { useSelector } from 'react-redux';
import { numberWithCommas } from '@/utils/numberFormat';
import { orderSelector } from '@/store/reducers/backup/orderSlice';

type CouponCardProps = {
  discountId: number;
  name: string;
  description: string;
  remainingRights: number;
  isShowModal?: boolean;
  discountCategory?: any;
  discountCode?: string;
  startDate?: string;
  endDate?: string;
  isCollected?: boolean;
  fetchDataDiscount?: () => void;
  isUse?: boolean;
  formRadio?: React.ReactNode;
  totalPrice?: number;
  minPrice?: number;
  isHide?: boolean;
};
const CouponCard = ({
  discountId,
  name,
  description,
  remainingRights,
  isShowModal,
  discountCategory,
  discountCode,
  startDate,
  endDate,
  isCollected,
  fetchDataDiscount,
  isUse,
  formRadio,
  totalPrice,
  minPrice,
  isHide,
}: CouponCardProps) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const [collected, setCollected] = useState<boolean>(false);
  const token = getCookie('access_token');
  const router = useRouter();
  const { orders } = useSelector(orderSelector);

  const onPickCoupon = async () => {
    if (token) {
      const payload = {
        discountId: Number(discountId),
      };
      const res = await apiDiscount.retrieveDiscount(payload);
      if (res.status) {
        if (fetchDataDiscount) {
          fetchDataDiscount();
        }
        toast.success('เก็บคูปองเรียบร้อย', {
          position: 'top-right',
          action: {
            label: 'คูปองของฉัน',
            onClick: () => router.push('/my-coupon'),
          },
        });
      }
      setCollected(true);
    } else {
      toast.warning('กรุณา Login', {
        position: 'top-right',
        action: {
          label: 'Login',
          onClick: () => router.push('/login'),
        },
      });
    }
  };
  const unUseShippingCoupon = () => {
    if (discountCategory.id === 2 && orders.shippingCost === 0) {
      return true;
    }
  };
  const checkMinPriceDiscount = () => {
    if (!isUndefined(totalPrice) && !isUndefined(minPrice)) {
      return totalPrice <= minPrice;
    }
  };
  return (
    <div
      className={`card-coupon type-coupon-${discountCategory?.id} ${checkMinPriceDiscount() ? 'isMinPriceDiscount' : ''} ${unUseShippingCoupon() && 'isUnUseShippingCoupon'}`}
    >
      <div className={'dot'}></div>
      <Box sx={{ flexGrow: 1 }} className={'h-full'}>
        <Grid container spacing={0} className={'h-full'}>
          <Grid size={3} className="box-ic-type">
            <div>
              <Image
                className={'img-marquee'}
                priority
                src={discountCategory?.categoryImage}
                alt={'icon marquee'}
                width={24}
                height={24}
              />
              <p>{discountCategory?.name}</p>
            </div>
          </Grid>
          <Grid size={9} className={'h-full'}>
            <div className="box-detail-coupon h-full">
              <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid size={9}>
                    <div className={'detail'}>
                      <div className={'box-name'}>
                        <p>{name}</p>
                        <span>{description}</span>
                      </div>
                      <div className={'tag-limit'}>
                        <Image
                          className={'img-marquee'}
                          priority
                          src="/images/bolt.svg"
                          alt={'icon marquee'}
                          width={12}
                          height={12}
                        />
                        <span>เหลือ {remainingRights} สิทธิ์</span>
                      </div>
                    </div>
                  </Grid>
                  <Grid size={3}>
                    <div className={'box-btn'}>
                      {collected || isCollected ? (
                        <Image
                          className={'img-marquee'}
                          priority
                          src={
                            discountCategory?.id === 1
                              ? `/images/Collected.svg`
                              : `/images/Collected2.svg`
                          }
                          alt={'icon marquee'}
                          width={45}
                          height={45}
                        />
                      ) : isUse ? (
                        formRadio && formRadio
                      ) : isHide ? (
                        <div />
                      ) : (
                        <Button variant={'contained'} onClick={onPickCoupon}>
                          เก็บ
                        </Button>
                      )}

                      {isShowModal && (
                        <span onClick={() => setIsShow(true)}>เงื่อนไข</span>
                      )}
                    </div>
                  </Grid>
                </Grid>
              </Box>
            </div>
          </Grid>
          <div className={'text-minPrice'}>
            {unUseShippingCoupon() && (
              <p>ไม่สามารถใช้ได้ เพราะไม่มีค่าจัดส่ง</p>
            )}
          </div>
          {checkMinPriceDiscount() && (
            <div className={'text-minPrice'}>
              {/* {unUseShippingCoupon() ? ( */}
              {/*  <p>ไม่สามารถใช้ได้ เพราะไม่มีค่าจัดส่ง</p> */}
              <p>
                สั่งซื้อเพิ่มอีก ฿
                {!isUndefined(totalPrice) &&
                  !isUndefined(minPrice) &&
                  numberWithCommas(minPrice - totalPrice)}{' '}
                เพื่อใช้คูปองนี้{' '}
              </p>
              {/* <p> */}
              {/*  สั่งซื้อเพิ่มอีก ฿ */}
              {/*  {!isUndefined(totalPrice) && */}
              {/*    !isUndefined(minPrice) && */}
              {/*    numberWithCommas(minPrice - totalPrice)}{' '} */}
              {/*  เพื่อใช้คูปองนี้{' '} */}
              {/* </p> */}
            </div>
          )}
        </Grid>
      </Box>
      {isShowModal && (
        <ModalCoupon
          discountId={Number(discountId)}
          isShow={isShow}
          onClose={(value: boolean) => setIsShow(value)}
          name={name}
          description={description}
          remainingRights={remainingRights}
          discountCategory={discountCategory}
          discountCode={discountCode}
          startDate={startDate}
          endDate={endDate}
          isCollected={collected || isCollected}
          fetchDataDiscount={fetchDataDiscount}
          isUse={isUse}
          isHide={isHide}
        />
      )}
    </div>
  );
};

export default CouponCard;
