import React, { useState, useEffect } from 'react';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import apiDiscount from '@/services/discount';
import Grid from '@mui/material/Grid2';
import CouponCard from '@/components/customUI/CouponCard/CouponCard';
import { isEmpty } from 'lodash';
import Box from '@mui/material/Box';
import { Field } from 'formik';
import { useSelector } from 'react-redux';
import { orderSelector } from '@/store/reducers/backup/orderSlice';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { discountAll } from '@/utils/discount';

type CouponRadioProps = {
  categoryId: number;
  name: string;
  setFieldValue: (name: string, value: number) => void;
  discountId?: number;
};
const CouponRadio = ({
  categoryId,
  name,
  setFieldValue,
  discountId,
}: CouponRadioProps) => {
  const { orders } = useSelector(orderSelector);
  const [value, setValue] = useState(0);
  const [data, setData] = useState<any>([]);

  const getUserDiscountList = async () => {
    const res = await apiDiscount.getUserDiscountList({
      page: 0,
      size: 100,
      ascending: true,
      categoryId: categoryId,
    });
    if (res.status) {
      const allData = discountAll(res.data?.content);
      setData(allData);
    }
  };
  const getDiscountById = async (discountId: number) => {
    const res = await apiDiscount.getDiscountById(discountId);
    if (res.status) {
      return res.data;
    }
  };
  const handleChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setFieldValue(
      name,
      await getDiscountById(Number((event.target as HTMLInputElement).value)),
    );
    setValue(Number((event.target as HTMLInputElement).value));
  };
  useEffect(() => {
    getUserDiscountList();
  }, []);
  useEffect(() => {
    if (discountId) {
      const hasValue = async () => {
        setFieldValue(name, await getDiscountById(Number(discountId)));
        setValue(Number(discountId));
      };
      hasValue();
    }
  }, [discountId]);
  return (
    <div className="box-form-control">
      <Field name={name}>
        {({ field }: any) => (
          <RadioGroup
            aria-labelledby="demo-controlled-radio-buttons-group"
            name={field.name}
            value={value}
            onChange={handleChange}
          >
            <Box sx={{ flexGrow: 1 }}>
              <Grid container spacing={2} className={'box-row'}>
                {!isEmpty(data) &&
                  data?.map((item: any, index: number) => {
                    const { totalPrice } = orders;
                    const { minPrice } = item.discount;
                    return (
                      <Grid
                        size={{ xs: 12 }}
                        className={'child-card-coupon'}
                        key={index}
                      >
                        <CouponCard
                          discountId={item.discount.id}
                          name={item.discount.title}
                          discountCategory={item.discount.discountCategory}
                          description={item.discount.description}
                          remainingRights={item.discount.maxUsage}
                          isShowModal={true}
                          isUse={true}
                          formRadio={
                            <FormControlLabel
                              value={item.discount.id}
                              control={
                                <Radio checkedIcon={<CheckCircleIcon />} />
                              }
                              label={''}
                            />
                          }
                          totalPrice={totalPrice}
                          minPrice={minPrice}
                        />
                      </Grid>
                    );
                  })}
              </Grid>
            </Box>
          </RadioGroup>
        )}
      </Field>
    </div>
  );
};

export default CouponRadio;
