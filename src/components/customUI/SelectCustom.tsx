import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Image from 'next/image';

interface SelectCustomProps {
  data: any[];
  selected: string;
  setSelected: React.Dispatch<React.SetStateAction<string>>;
  setSelectedAction: React.Dispatch<React.SetStateAction<any>>;
  name: string;
}

const SelectCustom = (props: SelectCustomProps) => {
  const { data, setSelected, selected, setSelectedAction, name } = props;

  const onSelect = (value: any) => {
    const result = data.find((x: any) => {
      return x.id === parseInt(value);
    });
    setSelected(value);
    setSelectedAction(result);
  };

  return (
    <div className="focus-visible:ring-0 focus-visible:ring-offset-0 mt-6">
      <Select name={name} onValueChange={onSelect} defaultValue={selected}>
        <SelectTrigger className="w-full h-[80px] focus:ring-offset-0 focus:ring-offset focus:ring-neutral-0 focus-visible:ring-0 focus-visible:ring-offset-0">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {data.map((data: any, index: number) => (
            <SelectItem
              className="flex flex-row"
              key={index}
              value={data.id.toString()}
            >
              <div className="flex justify-center align-middle items-center">
                <Image
                  priority
                  className="mr-[24px]"
                  src={
                    data?.imageUrl
                      ? data?.imageUrl
                      : '/images/default-image.png'
                  }
                  alt=""
                  width={56}
                  height={56}
                />
                <label className="text-sm font-bold">{data.name}</label>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default SelectCustom;
