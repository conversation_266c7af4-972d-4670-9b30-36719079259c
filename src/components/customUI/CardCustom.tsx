import React, { Fragment } from 'react';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { isUndefined } from 'lodash';

interface CardCustomProps {
  data: any;
  selected: any;
  onClick: (key: string) => void;
  disabled?: boolean;
}

const CardCustom = (props: CardCustomProps) => {
  const { data, selected, onClick, disabled = false } = props;
  const isDiscount = false;
  return (
    <Fragment>
      {!isUndefined(data.imageUrl) ? (
        !isUndefined(data.modelCode) ? (
          <div className={`flex flex-col justify-center items-center`}>
            <Card
              className={`${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} rounded-[16px] ${selected === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
              onClick={() => (disabled ? '' : onClick(data.id))}
            >
              <div className="flex justify-center items-center rounded-[16px] align-middle">
                <Image
                  priority
                  className="rounded-[16px]"
                  src={
                    data?.imageUrl
                      ? data?.imageUrl
                      : '/images/default-image.png'
                  }
                  alt=""
                  width={140}
                  height={140}
                />
              </div>
            </Card>
            <label
              className={`mt-4 text-xs font-bold ${disabled ? 'opacity-50' : ''} ${selected === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
            >
              {data.name}
            </label>
          </div>
        ) : (
          // todo Render long text with image box
          <Card
            className={`p-3 cursor-pointer ${selected === data.id ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
            onClick={() => onClick(data.id)}
          >
            <div className="flex items-center align-middle cursor-pointer">
              <Image
                priority
                className="rounded-full mr-[10px]"
                src={
                  data?.imageUrl ? data?.imageUrl : '/images/default-image.png'
                }
                alt=""
                width={56}
                height={56}
              />
              <div className="flex flex-col p-4 py-0">
                <label className="text-sm font-bold cursor-pointer">
                  {data.name}
                </label>
                {data.gram ? (
                  <label className="text-sm font-bold cursor-pointer">
                    {data.gram}
                    {data.gram ? ' แกรม' : ''}
                  </label>
                ) : (
                  <label className="text-xs cursor-pointer">
                    {data.detail}
                  </label>
                )}
              </div>
            </div>
          </Card>
        )
      ) : (
        <Card
          className={`p-6 cursor-pointer ${selected === data.amount ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
          onClick={() => onClick(data.amount)}
        >
          <div className="flex flex-col justify-center items-center align-middle cursor-pointer">
            <label className="text-sm font-bold cursor-pointer">
              {data.amount} ชิ้น
            </label>
            <label className="text-sm cursor-pointer">
              {data.price} บาท/ชิ้น
            </label>
            {isDiscount && (
              <div className="box-has-discount">
                <div className="value-discount">-5%</div>
                <span className={'old-price'}>฿{data.price}</span>
              </div>
            )}
          </div>
        </Card>
      )}
    </Fragment>
  );
};

export default CardCustom;
