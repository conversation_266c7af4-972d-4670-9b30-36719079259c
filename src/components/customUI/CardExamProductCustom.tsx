import React from 'react';
import { Card } from '@/components/ui/card';
import { numberWithCommas } from '@/utils/numberFormat';
import Image from 'next/image';

interface dimensionValueType {
  w: number;
  h: number;
  l: number;
}

interface CardData {
  id: number;
  name?: string | undefined;
  imageUrl?: string;
  detail?: string;
  dimension?: string;
  dimensionValue?: dimensionValueType;
  size?: string;
  h?: number;
  w?: number;
  modelCode?: string;
  gram?: string;
  productId?: string;
  createdDate?: string;
  modifiedDate?: string;
  materialPrice?: [];
  price?: string;
}

interface CardExamProductProps {
  data: CardData;
  selected: any;
  setSelected?: React.Dispatch<React.SetStateAction<number>>;
  setSelectedAction?: React.Dispatch<React.SetStateAction<CardData>>;
  isDelete?: boolean;
  detailActive?: boolean;
  selectExamProduct?: () => void;
  onClick: (examId: number) => void;
}

const CardExamProductCustom = (props: CardExamProductProps) => {
  const { data, selected, onClick } = props;

  const check = (id: number) => {
    return id === selected;
  };

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <Card
        className={`w-full p-3 cursor-pointer ${check(data.id) ? 'border-secondary-main bg-secondary-bg text-secondary-main' : ''}`}
        onClick={() => onClick(data.id)}
      >
        <div className="flex items-center justify-between align-middle">
          <div className="flex items-center justify-center">
            <Image
              priority
              className="mr-[10px] cursor-pointer"
              src={
                data?.imageUrl ? data?.imageUrl : '/images/default-image.png'
              }
              alt=""
              width={56}
              height={56}
            />
            <div className="flex flex-col cursor-pointer">
              <label className="text-sm font-bold cursor-pointer">
                {data.name}
              </label>
              <label className="text-xs cursor-pointer">{data.detail}</label>
            </div>
          </div>
          <div className="">
            <span className="text-sm font-bold text-secondary-main">
              {data.id === 1 ? 'ฟรี' : numberWithCommas(data.price)}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CardExamProductCustom;
