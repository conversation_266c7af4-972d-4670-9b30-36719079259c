import React from 'react';

import * as yup from 'yup';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import styled from 'styled-components';
import { Card } from '@/components/ui/card';

const UserCardCustomStyled = styled(Card)`
  box-shadow: 0 0 16px 0 rgba(33, 33, 33, 0.05);
  padding: 16px;
  margin-top: 24px;
  height: fit-content;
  .header-wrapper {
    .subtitle {
      font-size: 20px;
      font-weight: 700;
      color: black;
    }
  }
  .content-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .content-wrapper {
      width: 100%;
      //padding: 64px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 16px;
      .content {
        font-weight: 400;
        font-size: 14px;
        color: #bdbdbd;
      }
      .card-wrapper {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding: 16px;
        .text-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
          .content {
            font-size: 14px;
            font-weight: 400;
            color: #212121;
          }
        }
      }
    }
  }
  .change-user-detail-wrapper {
    background-color: black;
    color: white;
    width: fit-content;
    border-radius: 8px;
    padding: 8px 16px;
    display: flex;
    margin: 16px 0;
    align-items: center;
    cursor: pointer;
    .content {
      margin-left: 8px;
      font-size: 14px !important;
      font-weight: 700 !important;
      color: white !important;
    }
  }
  .user-container {
  }
`;

interface UserCardCustomType {
  editData?: any;
  editMode: boolean;
  setEditMode: React.Dispatch<React.SetStateAction<boolean>>;
  submit: (values: any) => void;
  loading: boolean;
  isActive: boolean;
  setIsActive: React.Dispatch<React.SetStateAction<boolean>>;
}

const UserCardCustom = (props: UserCardCustomType) => {
  const {
    editData,
    editMode,
    setEditMode,
    submit,
    loading,
    setIsActive,
    isActive,
  } = props;

  const userSchema = yup.object().shape({
    firstname: yup.string().required('กรุณากรอก ชื่อ'),
    lastname: yup.string().required('กรุณากรอก นามสกุล'),
    phoneNumber: yup
      .string()
      .required('กรุณากรอก หมายเลขโทรศัพท์')
      .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
      .min(9, 'กรุณากรอก หมายเลขโทรศัพท์ ให้ครบ 10 หลัก')
      .max(10, 'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก'),
  });

  return (
    <UserCardCustomStyled>
      <div className="header-wrapper">
        <span className="subtitle">ข้อมูลผู้สั่งซื้อ</span>
      </div>
      <div className="content-container">
        <div className="content-wrapper">
          {editData.phoneNumber ? (
            <Card className="card-wrapper">
              <div className="text-wrapper">
                <span className="subtitle">{editData.name}</span>
                <span className="content">
                  โทรศัพท์: {editData.phoneNumber}
                </span>
              </div>
              <Image
                priority
                className="self-start cursor-pointer"
                src="/icons/edit.svg"
                alt=""
                width={24}
                height={24}
                onClick={() => {
                  setEditMode(true);
                  setIsActive(true);
                }}
              />
            </Card>
          ) : (
            <div className="mt-6 flex flex-col items-center">
              <Image
                priority
                className=""
                src="/icons/person.svg"
                alt=""
                width={40}
                height={40}
              />
              <span className="content">
                คุณยังไม่มีข้อมูลผู้สั่งซื้อเพิ่มข้อมูลผู้สั่งซื้อ
              </span>
              <div
                className="change-user-detail-wrapper"
                onClick={() => setIsActive(true)}
              >
                <Image
                  priority
                  className=""
                  src="/icons/add-white.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                <span className="content">เพิ่มข้อมูล</span>
              </div>
            </div>
          )}
        </div>
        <Dialog
          open={isActive}
          onOpenChange={(e) => {
            setEditMode(false);
            setIsActive(e);
          }}
        >
          {/* <DialogTrigger asChild> */}

          {/* </DialogTrigger> */}
          <DialogContent
            className="user-container"
            onInteractOutside={(e) => {
              e.preventDefault();
            }}
          >
            <DialogHeader className="user-header-wrapper">
              <DialogTitle>
                {editMode ? 'แก้ไขข้อมูลผู้สั่งซื้อ' : 'เพิ่มข้อมูลผู้สั่งซื้อ'}
              </DialogTitle>
              <DialogTitle></DialogTitle>
            </DialogHeader>
            <Formik
              initialValues={
                editMode
                  ? {
                      firstname: editData?.name?.split(' ')[0],
                      lastname: editData?.name?.split(' ')[1],
                      phoneNumber: editData?.phoneNumber,
                    }
                  : {
                      firstname: editData?.name.split(' ')[0],
                      lastname: editData?.name.split(' ')[1],
                      phoneNumber: '',
                    }
              }
              validationSchema={userSchema}
              onSubmit={(data: any) => {
                submit(data);
              }}
            >
              {(formik) => (
                <Form className="form-user-custom-container">
                  {/* <h3 className="text-head px-6 pt-6">ผู้รับ</h3> */}
                  <div className="flex flex-row text-start my-6 px-6">
                    <div className="mr-2 w-1/2">
                      <label className="text-bold text-m">ชื่อ</label>
                      <Field name="firstname">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุชื่อจริง'}
                              onChange={field.onChange}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    <div className="ml-2 w-1/2">
                      <label className="text-bold text-m">นามสกุล</label>
                      <Field name="lastname">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุนามสกุล'}
                              onChange={field.onChange}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                  </div>
                  <div className="flex-row text-start px-6">
                    <label className="text-bold text-m">โทรศัพท์</label>
                    <Field name="phoneNumber">
                      {({ field }: any) => (
                        <div>
                          <Input
                            disabled={loading}
                            name={field.name}
                            className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                            placeholder={'ระบุหมายเลขโทรศัพท์'}
                            onChange={(e: any) => {
                              const newValue = e.target.value;
                              if (/^\d*$/.test(newValue)) {
                                formik.setFieldValue(field.name, newValue);
                              }
                            }}
                            value={field.value}
                          />
                          <ErrorMessage name={field.name}>
                            {(msg) => <div className="text-red-500">{msg}</div>}
                          </ErrorMessage>
                        </div>
                      )}
                    </Field>
                  </div>
                  <div className="flex px-6 p-6">
                    <Button
                      type="button"
                      disabled={loading}
                      className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
                      onClick={() => {
                        setEditMode(false);
                        setIsActive(false);
                      }}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      disabled={loading}
                      type="submit"
                      className="confirm-button ml-2 w-1/2"
                    >
                      บันทึก
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>
      </div>
    </UserCardCustomStyled>
  );
};

export default UserCardCustom;
