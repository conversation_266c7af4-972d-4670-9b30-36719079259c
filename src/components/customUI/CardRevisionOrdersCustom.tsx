import React, { Fragment, useState } from 'react';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CardOrdersCustomsStyle } from '@/styles/orders.styled';
import { AddressType } from '@/types/address';
import CardAddressCustom from '@/components/customUI/CardAddressCustom';
import FormAddressCustom from '@/components/customUI/FormAddressCustom';
import { isEmpty, isUndefined } from 'lodash';
import {
  OrderItemSpecialTechnicsType,
  OrderItemType,
} from '@/store/type/order';

interface CardRevisionOrdersCustomProps {
  data: OrderItemType;
  dataAddress: AddressType[];
  editMode: any;
  setEditMode: React.Dispatch<React.SetStateAction<boolean>>;
  handleSelectShipping: (id: number, shippingId: number) => void;
  handleSelectAddress: (address: any) => void;
  handleSubmitAddress: (id: number, values: any) => void;
  isActiveModal: boolean;
  setIsActiveModal: React.Dispatch<React.SetStateAction<boolean>>;
  isActiveAdd: boolean;
  setIsActiveAdd: React.Dispatch<React.SetStateAction<boolean>>;
  loading: boolean;
  validateAddress: boolean;
  selectedItem: number;
  setSelectedItem: React.Dispatch<React.SetStateAction<number>>;
}

const CardRevisionOrdersCustom = ({
  data,
  dataAddress,
  editMode,
  setEditMode,
  loading,
  handleSelectShipping,
  handleSelectAddress,
  handleSubmitAddress,
  isActiveModal,
  setIsActiveModal,
  isActiveAdd,
  setIsActiveAdd,
  validateAddress,
  setSelectedItem,
}: CardRevisionOrdersCustomProps) => {
  const [addressAction, setAddressAction] = useState<any>();
  const [address, setAddress] = useState(
    dataAddress.length > 0 ? dataAddress[0].id : 1,
  );

  return (
    !isEmpty(data) &&
    !isUndefined(data) && (
      <CardOrdersCustomsStyle className="card-box">
        <div className="header-wrapper">
          <div className="text-wrapper">
            <span className="subtitle">{data.model.name}</span>
          </div>
        </div>
        <div className="content-wrapper">
          <Image
            priority
            className="w-[80px] h-[80px]"
            src={
              data.model.imageUrl
                ? data.model.imageUrl
                : '/images/default-image.png'
            }
            alt=""
            width={80}
            height={80}
          />
          <div className="flex w-full justify-between">
            <div className="text-container">
              <div className="text-wrapper">
                <span className="subtitle">
                  ขนาด {data.width} x {data.length} x {data.height} mm,
                  กระดาษอาร์ตการ์ด 1 หน้า • 300 แกรม
                </span>
              </div>
              <div className="amount-container">
                <span className="amount">
                  x {numberWithCommas(data.material.amount, 0)}
                </span>
                <span className="price">
                  ({numberWithCommas(data.unitPrice)}/ชิ้น)
                </span>
              </div>
            </div>
            <div className="price-container font-bold">
              <span className="price">{numberWithCommas(data.totalPrice)}</span>
            </div>
          </div>
        </div>
        <div className="detail-wrapper">
          <Accordion
            type={'single'}
            key={data.id}
            collapsible
            className="space-y-2"
          >
            <AccordionItem value="item-1">
              <AccordionTrigger className="px-4 justify-center hover:no-underline">
                <span className="content pr-2">รายละเอียดสินค้า</span>
              </AccordionTrigger>
              <AccordionContent asChild className="show-more-container ">
                <div className="show-more-wrapper">
                  <span className="head-text ">ขนาด</span>
                  <span className="content-text ">
                    {data.width} x {data.length} x {data.height} mm
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">วัสดุ</span>
                  <span className="content-text ">
                    {data.material.name} • {data.material.gram} แกรม
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">พิมพ์</span>
                  <span className="content-text">
                    {data.printing.id === 1 ? 'ด้านหน้า' : ''}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">เคลือบ</span>
                  <span className="content-text">
                    {data.coating && data.coating.name}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">เทคนิคพิเศษ</span>
                  <span className="special-wrapper">
                    {data.specialTechnics &&
                      data.specialTechnics.map(
                        (data: OrderItemSpecialTechnicsType, index: number) => (
                          <span
                            className="special-text"
                            key={index}
                          >{`${data.name} • ขนาด ${data.width}x${data.height} mm`}</span>
                        ),
                      )}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">อาร์ตเวิร์ก</span>
                  <span className="content-text ">
                    {data.artwork.status
                      ? `ใช้บริการออกแบบ`
                      : 'ไม่ใช้อาร์ตเวิร์ก'}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">ลิงก์ไฟล์งานอาร์ตเวิร์ค</span>
                  <span className="content-text ">
                    {data.artwork.url ? data.artwork.url : '-'}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">ตัวอย่างสินค้า</span>
                  <span className="content-text ">
                    {data.productDemo.id === 1
                      ? 'Soft and Online Proof'
                      : 'Mockup Proof'}
                  </span>
                </div>
                <div className="show-more-wrapper">
                  <span className="head-text ">หมายเหตุ</span>
                  <span className="content-text ">
                    {data.description ? data.description : '-'}
                  </span>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <div className="location-container">
          <div className="location-wrapper">
            <div className="select-location-wrapper">
              <Image
                priority
                className="mr-3"
                src="/icons/share_location.png"
                alt=""
                width={24}
                height={24}
              />

              <span className="content mr-3">การจัดส่ง :</span>
              <span className="content primary">
                {data.shipping && data.shipping === 1
                  ? 'รับด้วยตัวเอง'
                  : 'จัดส่งขนส่งเอกชน'}
              </span>
              <DropdownMenu>
                <DropdownMenuTrigger
                  className="cursor-pointer [&[data-state=open]>img]:rotate-180"
                  asChild
                >
                  <div>
                    <Image
                      priority
                      className="shrink-0 transition-transform duration-200"
                      src="/icons/arrow_drop_down.png"
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuItem
                    onClick={() => {
                      handleSelectShipping(data.id, 1);
                    }}
                    className="cursor-pointer"
                  >
                    รับด้วยตัวเอง
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      handleSelectShipping(data.id, 2);
                    }}
                    className="cursor-pointer"
                  >
                    จัดส่งขนส่งเอกชน
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            {data.shipping === 2 && (
              <Dialog
                open={isActiveModal}
                onOpenChange={(e) => {
                  setIsActiveModal(e);
                  setIsActiveAdd(false);
                  setSelectedItem(data.id);
                }}
              >
                {data.address && data.address.length > 0 ? (
                  ''
                ) : (
                  <DialogTrigger asChild>
                    <div
                      className="change-address-wrapper"
                      onClick={() => {
                        setSelectedItem(data.id);
                      }}
                    >
                      <span className="address">เพิ่มที่อยู่</span>
                      <Image
                        priority
                        className=""
                        src="/icons/add-primary.svg"
                        alt=""
                        width={24}
                        height={24}
                      />
                    </div>
                  </DialogTrigger>
                )}
                <DialogContent
                  className="address-container"
                  onInteractOutside={(e) => {
                    e.preventDefault();
                  }}
                >
                  <DialogHeader className="address-header-wrapper">
                    <DialogTitle>
                      {!isActiveAdd
                        ? 'ข้อมูลการจัดส่ง'
                        : editMode
                          ? 'แก้ไขข้อมูลการจัดส่งสินค้า'
                          : 'เพิ่มข้อมูลการจัดส่งสินค้า'}
                    </DialogTitle>
                    <DialogTitle>
                      {!isActiveAdd ? (
                        ''
                      ) : (
                        <Image
                          priority
                          className="absolute top-4 left-4 cursor-pointer"
                          src="/icons/arrow_back.png"
                          alt=""
                          width={16}
                          height={16}
                          onClick={() => {
                            setEditMode(false);
                            setIsActiveAdd(false);
                          }}
                        />
                      )}
                    </DialogTitle>
                  </DialogHeader>
                  {!isActiveAdd ? (
                    dataAddress.length > 0 ? (
                      <Fragment>
                        <div className="address-have-content-wrapper">
                          <CardAddressCustom
                            data={dataAddress}
                            selected={address}
                            setSelected={setAddress}
                            setSelectedAction={setAddressAction}
                            setIsActiveAdd={setIsActiveAdd}
                            setEditMode={setEditMode}
                          />
                          <Button
                            className="add-address-button"
                            onClick={() => setIsActiveAdd(true)}
                          >
                            <Image
                              priority
                              className=""
                              src="/icons/add-white.svg"
                              alt=""
                              width={16}
                              height={16}
                            />
                            <span className="text-white">เพิ่มข้อมูล</span>
                          </Button>
                        </div>
                        <DialogFooter className="fixed w-full bottom-0 flex flex-row p-4 border-t-[1px] border-[#DBE2E5]">
                          <Button
                            disabled={loading}
                            type="button"
                            className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
                            onClick={() => setIsActiveModal(false)}
                          >
                            ยกเลิก
                          </Button>
                          <Button
                            disabled={loading}
                            type="submit"
                            className="confirm-button ml-2 w-1/2"
                            onClick={() => {
                              handleSelectAddress(addressAction);
                              setIsActiveModal(false);
                            }}
                          >
                            บันทึก
                          </Button>
                        </DialogFooter>
                      </Fragment>
                    ) : (
                      <div className="flex flex-col items-center">
                        <div className="address-content-wrapper">
                          <Image
                            priority
                            className=""
                            src="/icons/add_location.png"
                            alt=""
                            width={40}
                            height={40}
                          />
                          <span className="content">
                            คุณยังไม่มีข้อมูลที่อยู่จัดส่งสินค้าเพิ่มข้อมูลที่อยู่จัดส่งสินค้า
                          </span>
                          <Button
                            className="add-address-button"
                            onClick={() => setIsActiveAdd(true)}
                          >
                            <Image
                              priority
                              className=""
                              src="/icons/add-white.svg"
                              alt=""
                              width={16}
                              height={16}
                            />
                            <span className="text-white">เพิ่มข้อมูล</span>
                          </Button>
                        </div>
                      </div>
                    )
                  ) : (
                    <Fragment>
                      <div className="address-add-wrapper">
                        <FormAddressCustom
                          submit={(values: any) =>
                            handleSubmitAddress(addressAction.id, values)
                          }
                          loading={loading}
                          setIsActiveAdd={setIsActiveAdd}
                          editData={addressAction}
                          editMode={editMode}
                          setEditMode={setEditMode}
                        />
                      </div>
                    </Fragment>
                  )}
                </DialogContent>
              </Dialog>
            )}
          </div>
          {data.shipping === 1 ? (
            <div className="location-text-wrapper">
              <span className="content w-2/3">
                Luca block Co., Ltd • ************ 6 ซอยบางแค 12 บางแค บางแค
                กรุงเทพมหานคร 10160, ประเทศไทย
              </span>
            </div>
          ) : data.address && data.address.length > 0 ? (
            <div className="location-text-wrapper">
              <span className="content w-2/3">
                {`${data.recipientName} • ${data.phoneNumber} ${data.email} ${data.address} ${data.subDistrict} ${data.district} ${data.province} ${data.zipCode} ประเทศไทย`}
              </span>
              {data.shipping === 2 && (
                <span
                  className="change-location w-1/3 text-right"
                  onClick={() => {
                    setSelectedItem(data.id);
                    setIsActiveModal(true);
                  }}
                >
                  เปลี่ยนที่อยู่
                </span>
              )}
            </div>
          ) : (
            !validateAddress && (
              <div className="flex flex-col mt-4">
                <span className="subtitle text-red-500 flex justify-center bg-[#FFECEC] p-2 rounded-lg items-center">
                  <Image
                    priority
                    className="mr-2"
                    src="/icons/error-red.svg"
                    alt=""
                    width={40}
                    height={40}
                  />
                  กรุณาเลือกที่อยู่จัดส่ง
                </span>
              </div>
            )
          )}
        </div>

        <div className="price-wrapper">
          <div className="price-text-wrapper">
            <span className="caption">ค่าจัดส่ง</span>
            <span className="caption">ราคารวม</span>
          </div>
          <div className="price-text-wrapper">
            <span className="subtitle">
              {numberWithCommas(data.shippingCost, 2)}
            </span>
            <span className="subtitle">
              {numberWithCommas(data.totalPrice + (data.shippingCost || 0), 2)}
            </span>
          </div>
        </div>
      </CardOrdersCustomsStyle>
    )
  );
};

export default CardRevisionOrdersCustom;
