import React, { Fragment } from 'react';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useRouter } from 'next/router';
import { useSearchParams } from 'next/navigation';
import { isEmpty, isUndefined } from 'lodash';
import { ReQuotationItemType } from '@/store/type/reQuotation';

interface CardCartCustomProps {
  cartData: ReQuotationItemType[];
  setSelectedItem: React.Dispatch<React.SetStateAction<any>>;
  setIsActiveDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const CardCartCustom = ({
  cartData,
  setSelectedItem,
  setIsActiveDeleteModal,
}: CardCartCustomProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('revisionId');

  return (
    <Fragment>
      {!isEmpty(cartData) ? (
        cartData.map((data: ReQuotationItemType, index: number) => (
          <Card className="card-box" key={index}>
            <div className="header-wrapper">
              <div className="text-wrapper">
                <span className="subtitle">{data.model.name}</span>
              </div>
              <div className="action-wrapper">
                <Image
                  priority
                  className="edit"
                  src="/icons/edit.png"
                  alt=""
                  width={24}
                  height={24}
                  onClick={() =>
                    router.push(
                      `/my-orders/re-quotation/customize/${data.id}?revisionId=${id}`,
                    )
                  }
                />
                <Image
                  priority
                  className="delete"
                  src="/icons/delete-gray.png"
                  alt=""
                  width={24}
                  height={24}
                  onClick={() => {
                    setSelectedItem(data);
                    setIsActiveDeleteModal(true);
                  }}
                />
              </div>
            </div>
            <div className="content-wrapper">
              <Image
                priority
                src={data.model.imageUrl}
                alt=""
                width={80}
                height={80}
              />
              <div className="text-container">
                <div className="text-wrapper">
                  <span className="subtitle">
                    ขนาด {data.width} x {data.length} x{data.height} mm,
                    {data.material.name} •{data.material.gram} แกรม
                  </span>
                  <span className="subtitle">
                    จำนวน {numberWithCommas(data.material.amount)} ชิ้น (
                    {numberWithCommas(data.unitPrice)}/ชิ้น)
                  </span>
                </div>
                <div className="price-container">
                  <span className="price">
                    {numberWithCommas(data.totalPrice)}
                  </span>
                  <span className="unit">บาท</span>
                </div>
              </div>
            </div>
            <div className="footer-wrapper">
              <Accordion
                type={'single'}
                key={index}
                collapsible
                className="space-y-2"
              >
                <AccordionItem value="item-1">
                  <AccordionTrigger className="px-4 justify-center hover:no-underline">
                    <span className="content pr-2">รายละเอียดสินค้า</span>
                  </AccordionTrigger>

                  <AccordionContent asChild className="show-more-container ">
                    <div className="show-more-wrapper">
                      <span className="head-text ">ขนาด</span>
                      <span className="content-text ">
                        {data.width} x {data.length}
                        {data.height} mm
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">วัสดุ</span>
                      <span className="content-text ">
                        {data.material.name} •{data.material.gram} แกรม
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">พิมพ์</span>
                      <span className="content-text">
                        {data.printing.id === 1 ? 'ด้านหน้า' : ''}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เคลือบ</span>
                      <span className="content-text">
                        {data.coating && data.coating.name}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เทคนิคพิเศษ</span>
                      <span className="special-wrapper">
                        {!isEmpty(data.specialTechnics) &&
                        !isUndefined(data.specialTechnics)
                          ? data.specialTechnics.map(
                              (data: any, index: number) => (
                                <span
                                  className="special-text"
                                  key={index}
                                >{`${data.name} • ขนาด ${data.width}x${data.height} mm`}</span>
                              ),
                            )
                          : '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">อาร์ตเวิร์ก</span>
                      <span className="content-text ">
                        {data.artwork.status
                          ? `ใช้บริการออกแบบ`
                          : 'ไม่ใช้อาร์ตเวิร์ก'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">
                        ลิงก์ไฟล์งานอาร์ตเวิร์ค
                      </span>
                      <span className="content-text ">
                        {data.artwork.url || '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">ตัวอย่างสินค้า</span>
                      <span className="content-text ">
                        {data.productDemo.id === 1
                          ? 'Soft and Online Proof'
                          : 'Mockup Proof'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">หมายเหตุ</span>
                      <span className="content-text ">
                        {data.description || '-'}
                      </span>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </Card>
        ))
      ) : (
        <div className="flex items-center justify-center border-[1px] border-[#DBE2E5] rounded-[16px] h-[200px]">
          <span className="text-[#BDBDBD]">ยังไม่มีสินค้าในรถเข็น</span>
        </div>
      )}
    </Fragment>
  );
};

export default CardCartCustom;
