import React, { useEffect, useState } from 'react';

import * as yup from 'yup';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import styled from 'styled-components';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import apiThaiAddress from '@/services/thaiAddress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { isEmpty } from 'lodash';
import { ReloadIcon } from '@radix-ui/react-icons';

const TaxCardCustomStyled = styled(Card)`
  box-shadow: 0 0 16px 0 rgba(33, 33, 33, 0.05);
  padding: 16px;
  margin-top: 24px;
  .header-wrapper {
    .subtitle {
      font-size: 20px;
      font-weight: 700;
      color: black;
    }
  }
  .content-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .content-wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 16px;
      .selected {
        border-color: black;
      }
      .content {
        font-weight: 400;
        font-size: 14px;
        color: #bdbdbd;
      }
    }
  }
  .change-tax-detail-wrapper {
    background-color: black;
    border-radius: 8px;
    color: white;
    width: fit-content;
    padding: 8px 16px;
    display: flex;
    margin: 16px 0;
    align-items: center;
    cursor: pointer;
    .content {
      margin-left: 8px;
      font-size: 14px !important;
      font-weight: 700 !important;
      color: white !important;
    }
  }
  .tax-container {
  }
`;

interface TaxCardCustomType {
  editData: any;
  editMode: boolean;
  setEditMode: React.Dispatch<React.SetStateAction<boolean>>;
  submit: (values: any) => void;
  loading: boolean;
  isActive: boolean;
  setIsActive: React.Dispatch<React.SetStateAction<boolean>>;
  setSelected?: React.Dispatch<React.SetStateAction<number>>;
  setSelectedAction?: React.Dispatch<React.SetStateAction<any>>;
}

const TaxCardCustom = (props: TaxCardCustomType) => {
  const {
    editData,
    editMode,
    setEditMode,
    submit,
    loading,
    setIsActive,
    isActive,
    setSelectedAction,
  } = props;

  const [arraySubDistrict, setArraySubDistrict] = useState<any[]>([]);
  const [taxType, setTaxType] = useState('1');
  const [selectedTax, setSelectedTax] = useState(editData[0]?.id);
  const [errorZipcode, setErrorZipcode] = useState('');

  const handleFindZipcode = async (formik: any, keyword: string) => {
    const resZipcode = await apiThaiAddress.getZipcode(parseInt(keyword));
    if (resZipcode.status) {
      formik.setFieldValue('province', resZipcode?.data.provinceId.name);
      formik.setFieldValue('district', resZipcode?.data.districtId.name);
      setErrorZipcode('');
      const resAddress = await apiThaiAddress.getSubDistrict(
        resZipcode?.data.districtId.id,
      );
      if (resAddress.status) {
        setArraySubDistrict(resAddress?.data);
        formik.setFieldValue('subDistrict', resAddress.data[0]?.name);
      }
    } else {
      setErrorZipcode('รหัสไปรษณีย์นี้ ไม่มีในระบบ');
    }
  };

  const searchZipcode = async (formik: any, event: any) => {
    const newValue = event.target.value;
    if (/^\d*$/.test(newValue)) {
      formik.setFieldValue('zipCode', newValue);
    }
    if (newValue.length === 5) {
      await handleFindZipcode(formik, newValue);
    } else {
      formik.setFieldValue('province', null);
      formik.setFieldValue('district', null);
      formik.setFieldValue('subDistrict', null);
    }
  };

  const TaxPersonSchema = yup.object().shape({
    taxPayerType: yup.string().required('กรุณาเลือก ประเภทผู้เสียภาษี'),
    taxId: yup
      .string()
      .test('taxId', 'กรุณากรอก ให้ครบ 13 ตัว', (val: any) => val.length === 13)
      .required('กรุณากรอก เลขประจําตัวผู้เสียภาษีอากร'),
    firstname: yup.string().required('กรุณากรอก ชื่อ'),
    lastname: yup.string().required('กรุณากรอก นามสกุล'),
    phoneNumber: yup
      .string()
      .required('กรุณากรอก หมายเลขโทรศัพท์')
      .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
      .test(
        'len',
        'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก',
        (val: any) => val.length === 10,
      ),
    email: yup
      .string()
      .email('กรุณากรอก อีเมล ให้ถูกต้อง')
      .required('กรุณากรอก อีเมล'),
    address: yup.string().required('กรุณากรอก ที่อยู่'),
    zipCode: yup
      .string()
      .required('กรุณากรอก รหัสไปรษณีย์')
      .matches(/^[0-9]+$/, 'กรุณากรอก รหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
      .min(4, 'กรุณากรอก รหัสไปรษณีย์ ให้ครบ 5 หลัก')
      .max(5, 'กรุณากรอก รหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
    province: yup.string().required('กรุณากรอก จังหวัด'),
    district: yup.string().required('กรุณากรอก อำเภอ'),
    subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
  });

  const TaxCorporateSchema = yup.object().shape({
    taxPayerType: yup.string().required('กรุณาเลือก ประเภทผู้เสียภาษี'),
    taxId: yup
      .string()
      .test('len', 'กรุณากรอก ให้ครบ 13 ตัว', (val: any) => val.length === 13)
      .required('กรุณากรอก เลขประจําตัวผู้เสียภาษีอากร'),
    taxPayerName: yup.string().required('กรุณากรอก ชื่อบริษัท'),
    phoneNumber: yup
      .string()
      .required('กรุณากรอก หมายเลขโทรศัพท์')
      .matches(/^[0-9]+$/, 'กรุณากรอก หมายเลขโทรศัพท์ เป็นตัวเลขเท่านั้น')
      .test(
        'len',
        'กรุณากรอก หมายเลขโทรศัพท์ ไม่เกิน 10 หลัก',
        (val: any) => val.length === 10,
      ),
    email: yup
      .string()
      .email('กรุณากรอก อีเมล ให้ถูกต้อง')
      .required('กรุณากรอก อีเมล'),
    address: yup.string().required('กรุณากรอก ที่อยู่'),
    zipCode: yup
      .string()
      .required('กรุณากรอกรหัสไปรษณีย์')
      .matches(/^[0-9]+$/, 'กรุณากรอกรหัสไปรษณีย์ เป็นตัวเลขเท่านั้น')
      .min(4, 'กรุณากรอกรหัสไปรษณีย์ ให้ครบ 5 หลัก')
      .max(5, 'กรุณากรอกรหัสไปรษณีย์ ไม่เกิน 5 หลัก'),
    province: yup.string().required('กรุณากรอก จังหวัด'),
    district: yup.string().required('กรุณากรอก อำเภอ'),
    subDistrict: yup.string().required('กรุณากรอก ตำบล/แขวง'),
  });

  const onSelect = (value: any) => {
    const result = editData.find((x: any) => {
      return x.id === parseInt(value);
    });
    setSelectedTax(value);
    if (setSelectedAction) {
      setSelectedAction(result);
    }
  };

  useEffect(() => {
    if (editData.length > 0) {
      setTaxType(editData[0]?.taxPayerType.toString());
      setSelectedTax(editData[0]?.id);
    }
    if (setSelectedAction) {
      setSelectedAction(editData[0]);
    }
  }, []);

  useEffect(() => {
    if (editMode) {
      apiThaiAddress
        .getZipcode(parseInt(selectedTax?.zipCode))
        .then((resZipcode: any) => {
          if (resZipcode.status) {
            apiThaiAddress
              .getSubDistrict(resZipcode?.data?.districtId?.id)
              .then((res: any) => {
                if (res.status) {
                  setArraySubDistrict(res?.data);
                }
              });
          }
        });
    }
  }, []);

  return (
    <TaxCardCustomStyled>
      <div className="header-wrapper">
        <span className="subtitle">ข้อมูลสำหรับขอใบกำกับภาษี</span>
      </div>
      <div className="content-container">
        <div className="content-wrapper">
          {editData.length > 0 ? (
            <div className="w-full ">
              <RadioGroup
                onValueChange={onSelect}
                defaultValue={editData[0]?.id}
              >
                {editData?.map((address: any, index: number) => (
                  <div
                    className={`${selectedTax === address.id ? 'selected' : ''} border-[1px] mb-6 border-[#DBE2E5] p-4 rounded-[8px] wrapper flex justify-between space-x-2`}
                    key={index}
                  >
                    <RadioGroupItem value={address.id} />
                    <div className="w-full flex flex-col">
                      <span className="text-[20px] font-bold">{`${address.taxPayerType === 1 ? 'บุคคลธรรมดา' : 'นิติบุคคล'} • ${address.name}`}</span>
                      <span className="text-[14px]">{`โทรศัพท์: ${address.phoneNumber}`}</span>
                      <span className="text-[14px]">{`อีเมล: ${address.email}`}</span>
                      <span className="text-[14px]">{`ที่อยู่: ${address.address} ต.${address.subDistrict}`}</span>
                      <span className="text-[14px]">{`อ.${address.district}  จ.${address.province} ${address.zipCode}`}</span>
                    </div>
                    <Image
                      priority
                      className="self-start cursor-pointer"
                      src="/icons/edit.svg"
                      alt=""
                      width={24}
                      height={24}
                      onClick={() => {
                        if (setSelectedAction) {
                          setSelectedTax(address);
                        }
                        setEditMode(true);
                        setIsActive(true);
                      }}
                    />
                  </div>
                ))}
              </RadioGroup>
              <div
                className="change-tax-detail-wrapper !p-2 !m-0 !rounded-[8px]"
                onClick={() => setIsActive(true)}
              >
                <Image
                  priority
                  className=""
                  src="/icons/add-white.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                <span className="content">เพิ่มที่อยู่ใบกำกับภาษี</span>
              </div>
            </div>
          ) : (
            <div className="mt-6 flex flex-col items-center">
              <Image
                priority
                className=""
                src="/icons/contract.svg"
                alt=""
                width={40}
                height={40}
              />
              <span className="content">
                คุณยังไม่มีข้อมูลสำหรับขอใบกำกับภาษี
              </span>
              <div
                className="change-tax-detail-wrapper"
                onClick={() => setIsActive(true)}
              >
                <Image
                  priority
                  className=""
                  src="/icons/add-white.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                <span className="content">เพิ่มที่อยู่ใบกำกับภาษี</span>
              </div>
            </div>
          )}
        </div>
        <Dialog
          open={isActive}
          onOpenChange={(e) => {
            setEditMode(false);
            setTaxType('1');
            setIsActive(e);
          }}
        >
          <DialogContent
            className="tax-container"
            onInteractOutside={(e) => {
              e.preventDefault();
            }}
          >
            <DialogHeader className="tax-header-wrapper">
              <DialogTitle>
                {editMode ? 'แก้ไขข้อมูลใบกำกับภาษี' : 'เพิ่มข้อมูลใบกำกับภาษี'}
              </DialogTitle>
              <DialogTitle></DialogTitle>
            </DialogHeader>
            <div className="tax-add-wrapper">
              <Formik
                initialValues={
                  editMode
                    ? {
                        isTax: selectedTax.isTax,
                        taxPayerType: selectedTax.taxPayerType?.toString(),
                        taxPayerName: selectedTax.name,
                        taxId: selectedTax.taxId,
                        firstname: selectedTax.name.split(' ')[0],
                        lastname: selectedTax.name.split(' ')[1],
                        phoneNumber: selectedTax.phoneNumber,
                        email: selectedTax.email,
                        address: selectedTax.address,
                        zipCode: selectedTax.zipCode,
                        province: selectedTax.province,
                        district: selectedTax.district,
                        subDistrict: selectedTax.subDistrict,
                      }
                    : {
                        isTax: true,
                        taxPayerType: '1',
                        taxPayerName: '',
                        taxId: '',
                        firstname: '',
                        lastname: '',
                        phoneNumber: '',
                        email: '',
                        address: '',
                        zipCode: '',
                        province: '',
                        district: '',
                        subDistrict: '',
                      }
                }
                validationSchema={
                  taxType === '1' ? TaxPersonSchema : TaxCorporateSchema
                }
                onSubmit={(data: any) => {
                  setTaxType('1');
                  submit(data);
                }}
                enableReinitialize={true}
              >
                {(formik) => (
                  <Form className="form-tax-custom-container">
                    <h3 className="text-head px-6 pt-6">ข้อมูลส่วนตัว</h3>
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">
                        ประเภทผู้เสียภาษี
                      </label>
                      <Field name="taxPayerType">
                        {({ field }: any) => (
                          <div className="mt-[14px]">
                            <Select
                              name={field.name}
                              onValueChange={(event: any) => {
                                setTaxType(event);
                                formik.setFieldValue(field.name, event);
                              }}
                              value={field.value}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="เลือกตำบล/แขวง" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={'1'}>บุคคลธรรมดา</SelectItem>
                                <SelectItem value={'2'}>นิติบุคคล</SelectItem>
                              </SelectContent>
                            </Select>
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">
                        เลขประจําตัวผู้เสียภาษีอากร
                      </label>
                      <Field name="taxId">
                        {({ field }: any) => (
                          <div>
                            <Input
                              type={'number'}
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุเลขประจําตัวผู้เสียภาษีอากร'}
                              onChange={field.onChange}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    {formik.values.taxPayerType === '1' ? (
                      <div className="flex flex-row text-start mb-8 px-6">
                        <div className="mr-2 w-1/2">
                          <label className="text-bold text-m">ชื่อ</label>
                          <Field name="firstname">
                            {({ field }: any) => (
                              <div>
                                <Input
                                  disabled={loading}
                                  name={field.name}
                                  className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                  placeholder={'ระบุชื่อจริง'}
                                  onChange={field.onChange}
                                  value={field.value}
                                />
                                <ErrorMessage name={field.name}>
                                  {(msg) => (
                                    <div className="text-red-500">{msg}</div>
                                  )}
                                </ErrorMessage>
                              </div>
                            )}
                          </Field>
                        </div>
                        <div className="ml-2 w-1/2">
                          <label className="text-bold text-m">นามสกุล</label>
                          <Field name="lastname">
                            {({ field }: any) => (
                              <div>
                                <Input
                                  disabled={loading}
                                  name={field.name}
                                  className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                  placeholder={'ระบุนามสกุล'}
                                  onChange={field.onChange}
                                  value={field.value}
                                />
                                <ErrorMessage name={field.name}>
                                  {(msg) => (
                                    <div className="text-red-500">{msg}</div>
                                  )}
                                </ErrorMessage>
                              </div>
                            )}
                          </Field>
                        </div>
                      </div>
                    ) : (
                      <div className="flex-row text-start mb-8 px-6">
                        <label className="text-bold text-m">ชื่อบริษัท</label>
                        <Field name="taxPayerName">
                          {({ field }: any) => (
                            <div>
                              <Input
                                disabled={loading}
                                name={field.name}
                                className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                placeholder={'ระบุชื่อบริษัท'}
                                onChange={field.onChange}
                                value={field.value}
                              />
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                    )}
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">โทรศัพท์</label>
                      <Field name="phoneNumber">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุหมายเลขโทรศัพท์'}
                              onChange={(e: any) => {
                                const newValue = e.target.value;
                                if (/^\d*$/.test(newValue)) {
                                  formik.setFieldValue(field.name, newValue);
                                }
                              }}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">อีเมล</label>
                      <Field name="email">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุอีเมล'}
                              onChange={field.onChange}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    <h3 className="text-head px-6">ที่อยู่</h3>
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">ที่อยู่</label>
                      <Field name="address">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ'}
                              onChange={field.onChange}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                          </div>
                        )}
                      </Field>
                    </div>
                    <div className="flex-row text-start mb-8 px-6">
                      <label className="text-bold text-m">รหัสไปรษณีย์</label>
                      <Field name="zipCode">
                        {({ field }: any) => (
                          <div>
                            <Input
                              disabled={loading}
                              name={field.name}
                              className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                              placeholder={'ระบุเลขรหัสไปรษณีย์'}
                              onChange={(e) => searchZipcode(formik, e)}
                              value={field.value}
                            />
                            <ErrorMessage name={field.name}>
                              {(msg) => (
                                <div className="text-red-500">{msg}</div>
                              )}
                            </ErrorMessage>
                            {errorZipcode.length > 0 ? (
                              <div className="text-red-500">{errorZipcode}</div>
                            ) : (
                              ''
                            )}
                          </div>
                        )}
                      </Field>
                    </div>
                    {/* {keyword.length === 5 || editMode ? ( */}
                    {/*  <> */}
                    {!isEmpty(formik.values.province) && (
                      <div className="flex-row text-start mb-8 px-6">
                        <label className="text-bold text-m">จังหวัด</label>
                        <Field name="province">
                          {({ field }: any) => (
                            <div>
                              <Input
                                disabled={true}
                                name={field.name}
                                className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                onChange={field.onChange}
                                value={field.value}
                              />
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                    )}
                    {!isEmpty(formik.values.district) && (
                      <div className="flex-row text-start mb-8 px-6">
                        <label className="text-bold text-m">อำเภอ</label>
                        <Field name="district">
                          {({ field }: any) => (
                            <div>
                              <Input
                                disabled={true}
                                name={field.name}
                                className="mt-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                onChange={field.onChange}
                                value={field.value}
                              />
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                    )}
                    {!isEmpty(formik.values.subDistrict) && (
                      <div className="flex-row text-start mb-8 px-6">
                        <label className="text-bold text-m">ตำบล/แขวง</label>
                        <Field name="subDistrict">
                          {({ field }: any) => (
                            <div>
                              <Select
                                name={field.name}
                                onValueChange={(event: any) =>
                                  formik.setFieldValue(field.name, event)
                                }
                                value={field.value}
                                // defaultValue={field.value}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="เลือกตำบล/แขวง" />
                                </SelectTrigger>
                                <SelectContent>
                                  {arraySubDistrict.map(
                                    (subDistrictData: any, index) => (
                                      <SelectItem
                                        key={index}
                                        value={subDistrictData.name}
                                      >
                                        {subDistrictData.name}
                                      </SelectItem>
                                    ),
                                  )}
                                </SelectContent>
                              </Select>
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                    )}
                    <div className="flex px-6 absolute bottom-0 w-full p-6 border-t-[1px] border-[#DBE2E5]">
                      <Button
                        disabled={loading}
                        type="button"
                        className="cancel-button mr-2 w-1/2 bg-[#F5F7F8] hover:bg-[#F5F7F8] text-black"
                        onClick={() => {
                          setTaxType('1');
                          setEditMode(false);
                          setIsActive(false);
                        }}
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        disabled={loading}
                        type="submit"
                        className="confirm-button ml-2 w-1/2"
                      >
                        {loading ? (
                          <>
                            <ReloadIcon className="h-2 w-2 animate-spin" />
                            กำลังดำเนินการ
                          </>
                        ) : (
                          'บันทึก'
                        )}
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </TaxCardCustomStyled>
  );
};

export default TaxCardCustom;
