import React, { useEffect } from 'react';
import styled from 'styled-components';
import { Progress } from '@/components/ui/progress';
import Image from 'next/image';
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

const ProgressCustomStyled = styled.div`
  margin: 16px 0 32px 0;
  height: 24px;
  width: 100%;
  background-color: #bdbdbd;
  border-radius: 99px;
  position: relative;
  .progress-bar {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .progress-bar-action {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    transform: translate(-50%, -50%);
    align-items: center;
    -moz-border-radius-topright: 99px;
    -moz-border-radius-bottomright: 99px;
    .spot {
      padding: 4px;
      border-radius: 99px;
      position: relative;
      &.end {
        width: 8px;
        height: 8px;
        cursor: pointer;
        padding: 8px;
        background-color: white;
      }
      &.current,
      &.pass {
        cursor: pointer;
        background-color: #ff4f00;
      }
      span {
        position: absolute;
        left: 50%;
        top: 65%;
        transform: translate(-50%, 50%);
        width: max-content;
        color: #ff4f00;
      }
    }
  }
`;

interface ProgressCustomType {
  isArtwork: boolean;
  shipping: number;
  status: number;
}

const ProgressCustom = (props: ProgressCustomType) => {
  const { isArtwork, shipping, status } = props;
  const limit = isArtwork ? (shipping === 1 ? 2 : 3) : shipping === 1 ? 1 : 4;

  useEffect(() => {}, []);

  const stepList = [
    [2, 2, 51, 51, 100],
    [2, 34.6, 67.3, 67.3, 100],
    [2, 26.5, 51, 75.5, 100],
    [2, 34.6, 34.6, 67.3, 100],
  ];

  const statusText = [
    {
      step: 1,
      statusArr: [
        { id: 1, text: 'เตรียมข้อมูล' },
        { id: 3, text: 'กำลังออกแบบ' },
        { id: 5, text: 'สำเร็จ' },
      ],
    },
    {
      step: 2,
      statusArr: [
        { id: 1, text: 'เตรียมข้อมูล' },
        { id: 2, text: 'กำลังออกแบบ' },
        { id: 3, text: 'กำลังผลิต' },
        { id: 5, text: 'สำเร็จ' },
      ],
    },
    {
      step: 3,
      statusArr: [
        { id: 1, text: 'เตรียมข้อมูล' },
        { id: 2, text: 'กำลังออกแบบ' },
        { id: 3, text: 'กำลังผลิต' },
        { id: 4, text: 'กำลังจัดส่ง' },
        { id: 5, text: 'สำเร็จ' },
      ],
    },
    {
      step: 4,
      statusArr: [
        { id: 1, text: 'เตรียมข้อมูล' },
        { id: 3, text: 'กำลังผลิต' },
        { id: 4, text: 'กำลังจัดส่ง' },
        { id: 5, text: 'สำเร็จ' },
      ],
    },
  ];

  function renderProgress() {
    return statusText[limit - 1].statusArr.map((data: any, index) => {
      if (data.id === status) {
        return (
          <div key={index} className="spot current">
            <Image
              priority
              src="/icons/progress-current.svg"
              alt=""
              width={16}
              height={16}
            />
            <span>{data.text}</span>
          </div>
        );
      }
      if (data.id < status) {
        return (
          <div key={index} className="spot pass">
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Image
                    priority
                    src="/icons/progress-pass.svg"
                    alt=""
                    width={16}
                    height={16}
                  />
                </TooltipTrigger>
                <TooltipContent side={'bottom'} sideOffset={8}>
                  <p>{data.text}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        );
      }
      return (
        <div key={index} className="spot limit">
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-white rounded-full flex items-center w-[16px] h-[16px] justify-center cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent side={'bottom'} sideOffset={8}>
                <p>{data.text}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    });
  }

  return (
    <ProgressCustomStyled>
      <Progress
        className="progress-bar"
        value={stepList[limit - 1][status - 1]}
      />
      <div className="progress-bar-action">{renderProgress()}</div>
    </ProgressCustomStyled>
  );
};

export default ProgressCustom;
