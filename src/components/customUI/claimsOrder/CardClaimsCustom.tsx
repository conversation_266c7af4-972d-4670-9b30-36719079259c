import React, { Fragment } from 'react';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

import dayjs from '@/utils/dayjs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import Player from 'next-video/player';
import styled from 'styled-components';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import { OrderClaimTypeState } from '@/store/type/orderClaim';

interface CardClaimsCustomProps {
  dataClaimsDetail: any;
  setSelectedItem?: React.Dispatch<React.SetStateAction<any>>;
  setIsActiveDeleteModal?: React.Dispatch<React.SetStateAction<boolean>>;
  setDataItem: React.Dispatch<React.SetStateAction<any>>;
  setIsActiveAcceptModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const CardClaimsCustomStyle = styled(Card)`
  .image-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    @media only screen and (max-width: 430px) {
      margin-top: 4px;
    }
  }

  .status {
    font-size: 12px;
    font-weight: 700;
    padding: 4px 12px;
    border-radius: 40px;
    color: #f09700;
    background-color: #fff8c0;
    height: 24px;
    display: flex;
    align-items: center;
    @media only screen and (max-width: 430px) {
      width: fit-content;
      margin-bottom: 4px;
      //align-self: end;
    }
  }
  .status.success {
    background-color: #ebfeeb;
    color: #00981b;
  }
  .status.fail {
    background-color: #ffecec;
    color: #e5200c;
  }
  .issue-wrapper {
    display: flex;
    align-items: start;
    flex-direction: column;
    padding: 16px;
    border-bottom: 1px solid #dbe2e5;
    .issue-head {
      font-size: 14px;
      font-weight: 700;
    }
    .issue-title {
      font-size: 14px;
      font-weight: 400;
    }
    .issue-des {
      font-size: 14px;
      font-weight: 400;
      background-color: #f5f7f8;
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
    }
  }
  .deliver-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #dbe2e5;
    .select-deliver-wrapper {
      align-items: center;
      display: flex;

      .content {
        font-size: 14px;
        font-weight: 700;
        margin-right: 4px;
      }

      .primary {
        color: #ff4f00;
      }
    }

    .change-address-wrapper {
      display: flex;
      cursor: pointer;
      align-items: center;

      .address {
        font-size: 14px;
        color: #ff4f00;
      }
    }

    .location-text-wrapper {
      background-color: #f5f7f8;
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
      display: flex;
    }
  }
`;

const CardClaimsCustom = ({
  dataClaimsDetail,
  setDataItem,
  setIsActiveAcceptModal,
}: CardClaimsCustomProps) => {
  const router = useRouter();
  const mapStatus = (key: string) => {
    switch (key) {
      case '1':
        return 'รอดำเนินการ';
      case '2':
        return 'อนุมัติการเคลม ';
      case '3':
        return 'กำลังดำเนินการ';
      case '4':
        return 'ผลิตเสร็จ';
      case '5':
        return 'สำเร็จ';
      case '6':
        return 'ไม่อนุมัติ';
      default:
        return -1; // Return -1 for keys that don't match any case
    }
  };

  return (
    <Fragment>
      {!isEmpty(dataClaimsDetail) ? (
        dataClaimsDetail.map((data: OrderClaimTypeState, index: number) => (
          <CardClaimsCustomStyle className="card-box" key={index}>
            <div className="content-wrapper">
              <div className="content-box">
                <Image
                  className="image-wrapper"
                  priority
                  src={data.item.model.imageUrl}
                  alt=""
                  width={80}
                  height={80}
                />
                <div className="text-container">
                  <div className="text-wrapper">
                    <span
                      className={`status ${data.status === 5 ? 'success' : ''} !block tablet:!hidden`}
                    >
                      {mapStatus(data.status.toString())}
                    </span>
                    <span className="subtitle-bold justify-between">
                      {`${data.item.model.name} • ${data.orderNumber}`}
                    </span>
                    <span className="time">
                      วันที่แจ้งเคลม:{' '}
                      {dayjs(data.createdDate).format('D MMM YYYY, H:mm')}
                    </span>
                    <span className="content-bold">
                      <span className="content">จำนวนที่เคลม: </span>
                      {numberWithCommas(data.amount, 0)} ชิ้น
                    </span>
                  </div>
                  <span
                    className={`status ${data.status === 5 ? 'success' : data.status === 6 ? 'fail' : ''} !hidden tablet:!flex`}
                  >
                    {mapStatus(data.status.toString())}
                  </span>
                </div>
              </div>
              {data.auditDescription && (
                <span className="text-red-500 bg-[#FFECEC] p-2 rounded-md font-bold text-sm">
                  หมายเหตุ:
                  <span className="text-red-500 font-normal text-sm">
                    {data.auditDescription}
                  </span>
                </span>
              )}
            </div>
            <div className="media-wrapper">
              <span className="content-bold">รูปภาพและวีดีโอ</span>
              <div className="media-list-wrapper grid grid-cols-4 tablet:grid-cols-6 desktop:flex">
                {data.images.map((media: any, index: number) => (
                  <Dialog key={index}>
                    <DialogTrigger asChild>
                      {media.url.split('.').pop() === 'mp4' ||
                      media.url.split('.').pop() === 'mov' ||
                      media.url.split('.').pop() === 'hevc' ? (
                        <div key={index} className="media-box">
                          <Player
                            src={media.url}
                            controls={false}
                            className="w-full h-full p-1 rounded-[16px]"
                          />
                        </div>
                      ) : (
                        <div key={index} className="media-box">
                          <Image
                            key={index}
                            className="w-[80px] tablet:w-[80px] h-[80px] tablet:h-[80px] p-1 rounded-[8px]"
                            priority
                            src={media.url}
                            alt=""
                            width={80}
                            height={80}
                          />
                        </div>
                      )}
                    </DialogTrigger>
                    <DialogContent className="slip-wrapper max-w-[400px] tablet:max-w-[70%] desktop:max-w-[50%]">
                      <DialogHeader className="p-6 border-b-[1px] border-[#dbe2e5]">
                        <DialogTitle>
                          {media.url.split('.').pop() === 'mp4' ||
                          media.url.split('.').pop() === 'mov' ||
                          media.url.split('.').pop() === 'hevc'
                            ? 'วีดีโอ'
                            : 'รูปภาพ'}
                        </DialogTitle>
                      </DialogHeader>
                      <div className="w-full h-full pt-0">
                        {media.url.split('.').pop() === 'mp4' ||
                        media.url.split('.').pop() === 'mov' ||
                        media.url.split('.').pop() === 'hevc' ? (
                          <div
                            key={index}
                            className="w-full h-full max-h-[600px] relative"
                          >
                            <Player
                              src={media.url}
                              className="image-wrapper w-full h-full object-contain rounded-[8px] p-4 pt-0"
                            />
                          </div>
                        ) : (
                          <div
                            key={index}
                            className="w-full h-full max-h-[600px] relative"
                          >
                            <Image
                              key={index}
                              className="image-wrapper w-full h-full object-contain rounded-[8px] p-4 pt-0"
                              priority
                              src={media.url}
                              alt=""
                              width={80}
                              height={80}
                              unoptimized
                            />
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                ))}
              </div>
              <span className="content">
                หมายเหตุ:{' '}
                <span className="content-bold">{data.description} </span>
              </span>
            </div>
            {data.claimProblem && (
              <div className="issue-wrapper">
                <div className="issue-head mb-[2px]">
                  ปัญหา และสาเหตุที่เกิดขึ้น
                </div>
                <div className="issue-title">
                  {data.claimProblem} • {data.claimCause}
                </div>
                <div className="issue-des">
                  บันทึกการตรวจสอบ : {data.auditDetail}
                </div>
              </div>
            )}
            <div className="deliver-wrapper">
              <div className="select-deliver-wrapper">
                <Image
                  priority
                  className="mr-3"
                  src="/icons/share_location.png"
                  alt=""
                  width={24}
                  height={24}
                />

                <span className="content mr-3">การจัดส่ง :</span>
                <span className="content primary">
                  {data.shippingDetail.shipping === 1
                    ? 'รับด้วยตัวเอง'
                    : 'จัดส่งขนส่งเอกชน'}
                </span>
              </div>
              <div className="w-fit flex">
                {data.shippingDetail.shipping === 1 &&
                (data.status === 4 || data.status === 5) ? (
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => {
                      router.push(`/claims-order/shipping-detail/${data.id}`);
                    }}
                  >
                    <Image
                      priority
                      className="mr-3"
                      src="/icons/package.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    <span className="text-[14px] text-[#212121] underline">
                      ข้อมูลการรับสินค้า (รับเอง)
                    </span>
                  </div>
                ) : (
                  (data.status === 4 || data.status === 5) &&
                  data.shippingDetail.trackingNumber && (
                    <Fragment>
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => {
                          router.push(
                            `/claims-order/shipping-detail/${data.id}`,
                          );
                        }}
                      >
                        <Image
                          priority
                          className="mr-3"
                          src="/icons/local_shipping.svg"
                          alt=""
                          width={24}
                          height={24}
                        />
                        <span className="text-[14px] text-[#212121] underline">
                          ข้อมูลการจัดส่ง (เอกชน)
                        </span>
                      </div>
                      {data.shippingDetail.trackingNumber &&
                        data.status === 4 &&
                        data.shippingDetail.isConfirmReceipt && (
                          <Button
                            className="bg-white text-black border-[#DBE2E5] border-[1px] hover:bg-white hover:text-black ml-4"
                            onClick={() => {
                              setDataItem(data);
                              setIsActiveAcceptModal(true);
                            }}
                          >
                            ตรวจสอบและยอมรับสินค้า
                          </Button>
                        )}
                    </Fragment>
                  )
                )}
              </div>
            </div>
            <div className="footer-wrapper">
              <Accordion
                type={'single'}
                key={index}
                collapsible
                className="space-y-2"
              >
                <AccordionItem value="item-1">
                  <AccordionTrigger className="px-4 justify-center hover:no-underline">
                    <span className="content pr-2">รายละเอียดสินค้า</span>
                  </AccordionTrigger>
                  <AccordionContent asChild className="show-more-container ">
                    <div className="show-more-wrapper">
                      <span className="head-text ">ขนาด</span>
                      <span className="content-text ">
                        {data.item.width} x {data.item.length} x{' '}
                        {data.item.height} mm
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">วัสดุ</span>
                      <span className="content-text ">
                        {data.item.material.name} • {data.item.material.gram}{' '}
                        แกรม
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">พิมพ์</span>
                      <span className="content-text">
                        {data.item.printing === 1 ? 'ด้านหน้า' : ''}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เคลือบ</span>
                      <span className="content-text">
                        {data.item.coating && data.item.coating.name}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">เทคนิคพิเศษ</span>
                      <span className="special-wrapper">
                        {!isEmpty(data.item.specialTechnics)
                          ? data.item.specialTechnics.map(
                              (data: any, index: number) => (
                                <span
                                  className="special-text"
                                  key={index}
                                >{`${data.name} • ขนาด ${data.width}x${data.height} mm`}</span>
                              ),
                            )
                          : '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">อาร์ตเวิร์ก</span>
                      <span className="content-text ">
                        {data.item.isArtwork
                          ? `ใช้บริการออกแบบ`
                          : 'ไม่ใช้อาร์ตเวิร์ก'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">
                        ลิงก์ไฟล์งานอาร์ตเวิร์ค
                      </span>
                      <span className="content-text ">
                        {data.item.artworkUrl || '-'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">ตัวอย่างสินค้า</span>
                      <span className="content-text ">
                        {data.item.productDemo === 1
                          ? 'Soft and Online Proof'
                          : 'Mockup Proof'}
                      </span>
                    </div>
                    <div className="show-more-wrapper">
                      <span className="head-text ">หมายเหตุ</span>
                      <span className="content-text ">
                        {data.item.description || '-'}
                      </span>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </CardClaimsCustomStyle>
        ))
      ) : (
        <div className="flex bg-[#F5F5F5] flex-col items-center justify-center border-[1px] border-[#DBE2E5] rounded-[16px] h-[200px]">
          <Image
            priority
            src="/icons/edit_document.svg"
            alt=""
            width={40}
            height={40}
          />
          <span className="mt-4 text-[#BDBDBD]">ไม่มีรายการแจ้งเคลมสินค้า</span>
        </div>
      )}
    </Fragment>
  );
};

export default CardClaimsCustom;
