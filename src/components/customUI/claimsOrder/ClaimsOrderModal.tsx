import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import * as yup from 'yup';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import styled from 'styled-components';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import apiClaimsOrder from '@/services/claimsOrder';
import { ReloadIcon } from '@radix-ui/react-icons';
import Player from 'next-video/player';

const ClaimsOrderModalStyle = styled(Dialog)``;

interface ClaimsOrderModalProps {
  handleSubmitAddress: (value: any) => void;
  open: boolean;
  handleClose: (value: any) => void;
  loading: boolean;
  data: any;
}

const claimsSchema = yup.object().shape({
  orderNumber: yup.string().required('กรุณาเลือก คำสั่งซื้อ'),
  orderItemId: yup.string().required('กรุณาเลือก สินค้า'),
  amountItem: yup.number(),
  amount: yup
    .number()
    .min(1, 'กรุณากรอก จำนวนสินค้าอย่างน้อย 1 ชิ้น')
    .max(
      yup.ref('amountItem'),
      `กรุณากรอก จำนวนสินค้าไม่เกินจำนวนสินค้าทั้งหมด`,
    )
    .required('กรุณากรอก จำนวนสินค้า'),
  files: yup.array().min(1, 'กรุณาอัปโหลดรูปหรือวิดีโอ'),
  description: yup.string().required('กรุณากรอก หมายเหตุ'),
});

const ClaimsOrderModal = (props: ClaimsOrderModalProps) => {
  const { handleSubmitAddress, open, handleClose, loading, data } = props;
  const [selected, setSelected] = useState('');
  const [orderItems, setOrderItems] = useState([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileInputVideoRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<any>([]);
  const [selectedVideo, setSelectedVideo] = useState<any>([]);
  const [loadingUpload, setLoadingUpload] = useState(false);

  const handleFileUpload = () => {
    // Trigger the click event of the file input element
    if (fileInputRef.current) {
      fileInputRef?.current?.click();
    }
  };

  const handleFileVideoUpload = () => {
    // Trigger the click event of the file input element
    if (fileInputVideoRef.current) {
      fileInputVideoRef?.current?.click();
    }
  };

  const handleFileChange = async (event: any, formik: any) => {
    const file = event.target.files[0];
    setLoadingUpload(true);
    if (file) {
      // const imageUrl = URL.createObjectURL(file);
      const fileType = file.type;
      const maxSizeInMB = 8; // 8MB
      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      if (
        fileType !== 'image/png' &&
        fileType !== 'image/jpeg' &&
        fileType !== 'image/svg+xml'
      ) {
        toast.error('กรุณาอัปโหลดรูปภาพไฟล์นามสกุล PNG, JPEG หรือ SVG.');
        setLoadingUpload(false);
        return;
      }
      if (file.size > maxSizeInBytes) {
        toast.error('กรุณาอัปโหลดรูปภาพที่มีขนาดไม่เกิน 8mb.');
        setLoadingUpload(false);
        return;
      }
      const formData = new FormData();
      formData.append('file', file);

      const responseUpload = await apiClaimsOrder.uploadFile(formData);
      if (responseUpload.status) {
        const arrayData = [
          ...selectedImage,
          ...selectedVideo,
          responseUpload.data,
        ];
        formik.setFieldValue('files', arrayData);
        setSelectedImage([...selectedImage, responseUpload.data]);
        setLoadingUpload(false);
      } else {
        toast.error(responseUpload.error);
      }
    }
  };

  const handleFileVideoChange = async (event: any, formik: any) => {
    const file = event.target.files[0];
    setLoadingUpload(true);
    if (file) {
      const fileType = file.name.split('.')[1];
      const maxSizeInMB = 400; // 400MB
      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      if (fileType !== 'mov' && fileType !== 'hevc' && fileType !== 'mp4') {
        toast.error('กรุณาอัปโหลดวีดีโอที่มีไฟล์นามสกุล MP4, MOV หรือ HEVC.');
        setLoadingUpload(false);
        return;
      }
      if (file.size > maxSizeInBytes) {
        toast.error('กรุณาอัปโหลดวีดีโอที่มีขนาดไฟล์ไม่เกิน 400mb.');
        setLoadingUpload(false);
        return;
      }
      const formData = new FormData();
      formData.append('file', file);
      const responseUpload = await apiClaimsOrder.uploadFile(formData);
      if (responseUpload.status) {
        const arrayData = [...selectedImage, responseUpload.data];
        formik.setFieldValue('files', arrayData);
        setSelectedVideo([responseUpload.data]);
        setLoadingUpload(false);
      } else {
        toast.error(responseUpload.error);
      }
    }
  };

  const handleDeleteFile = (file: string, image: boolean, formik: any) => {
    if (image) {
      const arrayData = [...selectedImage];
      const filteredArray = arrayData.filter((item: any) => item !== file);
      setSelectedImage(filteredArray);
      const arrayForm = [...selectedVideo, filteredArray];
      formik.setFieldValue('files', arrayForm);
    } else {
      const arrayData = [...selectedVideo];
      const filteredArray = arrayData.filter((item: any) => item !== file);
      setSelectedVideo(filteredArray);
      const arrayForm = [...selectedImage, filteredArray];
      formik.setFieldValue('files', arrayForm);
    }
  };

  const handleSelectOrderNumber = (orderId: any, formik: any) => {
    const findOrder = data.find(
      (orderItem: any) => orderItem.id === parseInt(orderId),
    );
    setSelected(findOrder.orderItems[0].id.toString());
    formik.setFieldValue('orderItemId', findOrder.orderItems[0].id.toString());
    formik.setFieldValue('amountItem', findOrder.orderItems[0].item.amount);
    setOrderItems(findOrder.orderItems);
  };

  useEffect(() => {
    if (data.length > 0) {
      setSelected(data[0]?.orderItems[0].id.toString());
      setOrderItems(data[0].orderItems);
      if (!open) {
        setSelected(data[0]?.orderItems[0].id.toString());
        setOrderItems(data[0].orderItems);
        setSelectedImage([]);
        setSelectedVideo([]);
      }
    }
  }, [data, open]);

  return (
    data.length > 0 && (
      <ClaimsOrderModalStyle
        open={open}
        onOpenChange={(value: any) => handleClose(value)}
      >
        <DialogContent className="claims-container max-w-[640px] max-h-[700px] overflow-y-scroll">
          <DialogHeader className="border-b-[1px] flex">
            <DialogTitle className="text-center p-4">
              แจ้งเคลมสินค้า
            </DialogTitle>
          </DialogHeader>
          <Formik
            initialValues={{
              orderNumber: data ? data[0].id?.toString() : '',
              orderItemId:
                data.length > 0 ? data[0].orderItems[0].id.toString() : '',
              amountItem: data ? data[0].orderItems[0].item.amount : '',
              amount: 0,
              files: [],
              description: '',
            }}
            validationSchema={claimsSchema}
            onSubmit={(values) => handleSubmitAddress(values)}
            enableReinitialize={true}
          >
            {(formik) => (
              <Form className="form-claims-container h-full relative">
                {/* <h3 className="text-[20px] font-bold px-6 mb-4">ข้อมูลส่วนตัว</h3> */}
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m font-bold">
                    รายการสั่งซื้อ
                  </label>
                  <Field name="orderNumber">
                    {({ field }: any) => (
                      <div className="mt-[14px]">
                        <Select
                          name={field.name}
                          onValueChange={(event: any) => {
                            formik.setFieldValue(field.name, event);
                            handleSelectOrderNumber(event, formik);
                          }}
                          value={field.value}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="เลือกรายการสั่งซื้อ" />
                          </SelectTrigger>
                          <SelectContent>
                            {data.map((item: any, index: number) => (
                              <SelectItem
                                key={index}
                                value={item.id.toString()}
                              >
                                {item.orderNumber}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <ErrorMessage name={field.name}>
                          {(msg) => <div className="text-red-500">{msg}</div>}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
                <div className="flex-row text-start mb-8 px-6">
                  <div className="w-full flex justify-between">
                    <span className="text-bold text-m font-bold">
                      รายการสินค้า
                    </span>
                    <span className="text-bold text-sm">
                      {orderItems.length} ชิ้น
                    </span>
                  </div>
                  <Field name="orderItemId">
                    {({ field }: any) => (
                      <div className="mt-[14px]">
                        <RadioGroup
                          className="gap-4 radio grid-cols-1 border-[1px] border-[#DBE2E5] p-4 rounded-[8px] max-h-[280px] overflow-y-scroll"
                          onValueChange={(event: any) => {
                            setSelected(event);
                            formik.setFieldValue(field.name, event);
                          }}
                          value={selected}
                        >
                          {orderItems?.map((data: any, index: number) => (
                            <div
                              className={`radio-item ${selected === data.id.toString() ? 'selected' : ''} rounded-[8px] flex justify-start space-x-2`}
                              key={index}
                            >
                              <RadioGroupItem
                                className="w-[24px] h-[24px]"
                                value={data.id.toString()}
                              />
                              <div className="content-wrapper flex-1">
                                <Image
                                  className="mr-4"
                                  src={data.item.model?.imageUrl}
                                  alt=""
                                  width={80}
                                  height={80}
                                  unoptimized
                                />
                                <div className="text-container">
                                  <div className="text-wrapper">
                                    <div className="flex flex-col">
                                      <span className="subtitle-bold">
                                        {data.item.model?.name}
                                      </span>
                                      <span className="subtitle truncate w-10/12">
                                        {`ขนาด ${data.item?.width} x ${data.item?.length} x
                                      ${data.item?.height} mm, ${data.item?.material.name} •
                                      ${data.item?.material.gram}
                                      แกรม`}
                                      </span>
                                    </div>
                                    <span className="subtitle">
                                      <span className="subtitle-bold">
                                        x{' '}
                                        {numberWithCommas(data.item?.amount, 0)}
                                      </span>{' '}
                                      ({numberWithCommas(data.item?.unitPrice)}
                                      /ชิ้น)
                                    </span>
                                  </div>
                                  <div className="price-container">
                                    <span className="price font-bold text-sm">
                                      {numberWithCommas(data.item.totalPrice)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </RadioGroup>
                        <ErrorMessage name={field.name}>
                          {(msg) => <div className="text-red-500">{msg}</div>}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m font-bold">
                    จำนวนที่ต้องการเคลม
                  </label>
                  <Field name="amount">
                    {({ field }: any) => (
                      <div>
                        <div className="flex items-center mt-[14px] border-[1px] border-neutral-200 pr-2 rounded-[6px]">
                          <Input
                            disabled={loading}
                            name={field.name}
                            type={'text'}
                            min={1}
                            className="focus-visible:ring-0 focus-visible:ring-offset-0 outline-0 pl-2 border-0"
                            placeholder={'0'}
                            onChange={(e: any) => {
                              let { value } = e.target;
                              value = value.replace(/[^0-9]/g, '');
                              value = value.replace(/^0+/, '');
                              formik.setFieldValue(field.name, value);
                            }}
                            onBlur={() => {
                              if (formik.values.amount === 0) {
                                formik.setFieldValue(field.name, 0);
                              }
                            }}
                            value={field.value}
                          />
                          <span className={''}>ชิ้น</span>
                        </div>
                        <ErrorMessage name={field.name}>
                          {(msg) => <div className="text-red-500">{msg}</div>}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m font-bold">
                    รูปภาพ/วีดีโอ
                  </label>
                  <Field name="files">
                    {({ field }: any) => (
                      <div className="upload-content grid grid-cols-3 tablet:grid-cols-4 mt-[14px] justify-items-center gap-4">
                        <Input
                          name={field.name}
                          className="hidden"
                          ref={fileInputRef}
                          onChange={(values: any) =>
                            handleFileChange(values, formik)
                          }
                          type={'file'}
                        />
                        <Input
                          name={field.name}
                          className="hidden"
                          ref={fileInputVideoRef}
                          onChange={(values: any) =>
                            handleFileVideoChange(values, formik)
                          }
                          type={'file'}
                        />
                        {selectedImage.length > 0 &&
                          selectedImage.map((image: any, index: number) => (
                            <div
                              key={index}
                              className="w-[120px] h-[120px] max-h-[120px] relative border-[#DBE2E5] border-[1px] rounded"
                            >
                              <Image
                                className="image-wrapper w-full h-full object-contain"
                                src={image}
                                alt="Selected"
                                width={100}
                                height={100}
                              />
                              <Image
                                className="image-wrapper w-[24px] h-[24px] absolute top-1 right-1 cursor-pointer"
                                src="/icons/cancel-black.svg"
                                alt="Selected"
                                onClick={() =>
                                  handleDeleteFile(image, true, formik)
                                }
                                width={24}
                                height={24}
                              />
                            </div>
                          ))}
                        {selectedImage.length < 6 && (
                          <Button
                            disabled={loadingUpload}
                            type={'button'}
                            className="upload-button"
                            onClick={handleFileUpload}
                          >
                            {loadingUpload ? (
                              <>
                                <ReloadIcon className="h-6 w-6 mb-2 animate-spin" />
                                กำลังดำเนินการ
                              </>
                            ) : (
                              <>
                                <Image
                                  priority
                                  src="/icons/photo-camera-gray.svg"
                                  alt=""
                                  width={24}
                                  height={24}
                                />
                                <span className="text-[#BDBDBD] my-[2px]">
                                  เพิ่มรูปภาพ
                                </span>
                                <span className="text-[#BDBDBD]">
                                  {`${selectedImage.length}/6`}
                                </span>
                              </>
                            )}
                          </Button>
                        )}
                        {/* //Todo video section */}
                        {selectedVideo.length !== 1 && (
                          <Button
                            disabled={loadingUpload}
                            type={'button'}
                            className="upload-button"
                            onClick={handleFileVideoUpload}
                          >
                            {loadingUpload ? (
                              <>
                                <ReloadIcon className="h-6 w-6 mb-2 animate-spin" />
                                กำลังดำเนินการ
                              </>
                            ) : (
                              <>
                                <Image
                                  priority
                                  src="/icons/videocam-gray.svg"
                                  alt=""
                                  width={24}
                                  height={24}
                                />
                                <span className="text-[#BDBDBD] my-[2px]">
                                  เพิ่มวีดีโอ
                                </span>
                                <span className="text-[#BDBDBD]">{`${selectedVideo.length}/1`}</span>
                                <div />
                              </>
                            )}
                          </Button>
                        )}
                        {selectedVideo.length > 0 &&
                          selectedVideo.map((video: any, index: number) => (
                            <div
                              key={index}
                              className="w-[120px] h-[120px] max-h-[120px] relative border-[#DBE2E5] border-[1px] rounded"
                            >
                              <Player
                                src={video.url}
                                controls={false}
                                className="w-full h-full "
                              />
                              <Image
                                className="image-wrapper w-[24px] h-[24px] absolute top-1 right-1 cursor-pointer"
                                src="/icons/cancel-black.svg"
                                alt="Selected"
                                onClick={() =>
                                  handleDeleteFile(video, false, formik)
                                }
                                width={24}
                                height={24}
                              />
                              <div className="w-full bg-[#21212199] content-center h-[32px] absolute bottom-0">
                                <Image
                                  className="image-wrapper w-[24px] h-[24px] cursor-pointer ml-2"
                                  src="/icons/videocam.svg"
                                  alt="Selected"
                                  width={24}
                                  height={24}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </Field>
                  <ErrorMessage name="files">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                  <div className="flex flex-col mt-4">
                    <label className="text-bold text-xs text-[#bdbdbd] mb-2">
                      *รูปภาพต้องเป็นไฟล์ PNG, JPEG หรือ SVG และ ขนาดไฟล์ไม่เกิน
                      8mb
                    </label>
                    <label className="text-bold text-xs text-[#bdbdbd]">
                      *วีดีโอต้องเป็นไฟล์ MP4, MOV หรือ HEVC และ ขนาดไฟล์ไม่เกิน
                      400mb
                    </label>
                  </div>
                </div>
                <div className="flex-row text-start mb-8 px-6">
                  <label className="text-bold text-m font-bold">หมายเหตุ</label>
                  <Field name="description">
                    {({ field }: any) => (
                      <div>
                        <Textarea
                          name={field.name}
                          value={field.value}
                          placeholder="ตัวอย่างเช่น ต้องการสร้างรายการสินค้าเพิ่ม"
                          className="mt-[14px] resize-none border-1-[#EEEEEE] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                          onChange={field.onChange}
                        />
                        <ErrorMessage name={field.name}>
                          {(msg) => <div className="text-red-500">{msg}</div>}
                        </ErrorMessage>
                      </div>
                    )}
                  </Field>
                </div>
                <DialogFooter className="flex flex-row p-6 w-full bg-white">
                  <Button
                    disabled={loading}
                    type={'button'}
                    className="w-1/2 bg-[#F5F5F5] text-black me-2 hover:bg-[#F5F5F5aa] hover:text-black"
                    onClick={() => {
                      setSelectedImage([]);
                      setSelectedVideo([]);
                      handleClose(false);
                    }}
                  >
                    ยกเลิก
                  </Button>
                  <Button
                    disabled={loading}
                    type={'submit'}
                    className="w-1/2 ml-2"
                  >
                    {loading ? (
                      <>
                        <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                        กำลังดำเนินการ
                      </>
                    ) : (
                      'บันทึก'
                    )}
                  </Button>
                </DialogFooter>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </ClaimsOrderModalStyle>
    )
  );
};

export default ClaimsOrderModal;
