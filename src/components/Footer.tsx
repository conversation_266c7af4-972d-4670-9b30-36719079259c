import React from 'react';
import Link from 'next/link';
import styled from 'styled-components';
import Image from 'next/image';

const FooterStyle = styled.footer`
  height: 740px;
  overflow-y: hidden;
  @media only screen and (max-width: 431px) {
    height: 100%;
  }
  .group-footer-head {
    @media only screen and (max-width: 431px) {
      label {
        font-size: 20px;
      }
    }
  }
  .group-footer-text {
    @media only screen and (max-width: 431px) {
      align-items: center;
      flex-direction: column;
    }
    .footer-row {
      display: flex;
      @media only screen and (max-width: 431px) {
        flex-direction: row;
        margin-bottom: 24px;
      }
    }
  }
`;

const Footer = () => {
  return (
    <FooterStyle className="bg-bg bg-center">
      <div
        className="flex justify-center items-center text-center h-[64px]
       text-white font-thin border-gray-700 border-y-[1px]"
      >
        <label className="text-white text-m">Call Center +66 657127411</label>
      </div>
      <div className="group-footer-head flex flex-col text-center my-16">
        <label className="text-white font-bold text-3xl mb-4">
          PRODUCE YOUR PACKAGING
        </label>
        <label className="text-white font-bold text-3xl">
          FROM THE FACTORY
        </label>
      </div>
      <Image
        priority
        className="desktop:h-[220px] desktop:w-[194px] h-[136px] w-[120px] desktop:my-[64px] mb-[40px] mx-auto"
        src="/icons/digibox-icon-gray.png"
        alt=""
        width={194}
        height={220}
      />
      <div className="flex justify-center desktop:mb-16 mb-[40px]">
        <div className="w-10 h-10 ">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full"
            src="/icons/facebook-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 mx-[40px]">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full"
            src="/icons/youtube-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 mr-[40px]">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full"
            src="/icons/line-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 ">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full"
            src="/icons/tiktok-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
      </div>
      <div className="group-footer-text flex justify-center">
        <label className="text-xs text-[#685B7E]">
          © 2024 © 2023 Digiboxs. All rights reserved.
        </label>
        <div className="footer-row">
          <label className="text-xs text-[#685B7E] mx-2 desktop:block hidden">
            •
          </label>
          <label className="text-xs text-[#685B7E]">Cookie</label>
          <label className="text-xs text-[#685B7E] mx-2"> • </label>
          <Link className="text-xs text-[#685B7E]" href={'/policy'}>
            Privacy Policy
          </Link>
        </div>
      </div>
    </FooterStyle>
  );
};

export default Footer;
