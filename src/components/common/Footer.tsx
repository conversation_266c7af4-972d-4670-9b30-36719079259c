import React from 'react';
import styled from 'styled-components';
import Grid from '@mui/material/Grid2';
import Box from '@mui/material/Box';
import Image from 'next/image';
import FooterMenuList from '@/components/common/FooterMenuList';

const FooterStyle = styled.div`
  padding: 2rem 4rem;
  background: #000;
  color: #fff;
  &.paddingUp {
    padding-bottom: 8rem;
  }
  .box-row-logo {
    margin-top: 2rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
  }
  .footer-bottom {
    border-top: 1px solid #757575;
    padding-top: 1rem;
    margin-top: 2rem;
    .policy-list {
      display: flex;
      align-items: center;
      gap: 1.2rem;
      font-size: 14px;
      li {
        text-decoration: underline;
      }
    }
    .reserved {
      text-align: right;
      font-size: 14px;
    }
  }
  @media (max-width: 1200px) {
    padding: 2rem;
    p {
      br {
        display: none;
      }
    }
  }
  @media (max-width: 900px) {
    &.paddingUp {
      padding-bottom: 12rem;
    }
    .footer-bottom {
      .policy-list {
        justify-content: center;
      }
      .reserved {
        text-align: center;
        font-size: 14px;
      }
    }
  }
  @media (max-width: 575.98px) {
    padding: 1rem;
    &.paddingUp {
      padding-bottom: 10rem;
    }
    .box-row-logo {
      gap: 1rem;
      img {
        width: 50px !important;
        &:last-child {
          width: 140px !important;
        }
      }
    }
  }
  @media (max-width: 375px) {
    padding: 1rem;
    .box-row-logo {
      gap: 1rem;
      img {
        width: 40px !important;
        &:last-child {
          width: 120px !important;
        }
      }
    }
  }
`;
const Footer = () => {
  return (
    <FooterStyle>
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={{ xl: 3, xs: 2 }}>
          <Grid size={{ md: 3.5, xs: 12 }}>
            <div>
              <Image
                src={'/images/new_logo_footer.svg'}
                alt={'new logo footer'}
                width={200}
                height={200}
              />
              <p className={'mt-[1rem]'}>
                ผลิตจากโรงพิมพ์ระบบอัตโนมัติ In-Houseไม่ต้องใช้ทีมกราฟิก <br />
                ไม่ต้องรอเซลล์สั่งผลิตตั้งแต่ 1 ใบ จนถึงระดับองค์กร
              </p>
              <div className={'box-row-logo'}>
                <Image
                  src={'/images/logo_footer_in_house_1.svg'}
                  alt={'new logo footer'}
                  width={60}
                  height={60}
                />
                <Image
                  src={'/images/logo_footer_in_house_2.svg'}
                  alt={'new logo footer'}
                  width={60}
                  height={60}
                />
                <Image
                  src={'/images/logo_footer_in_house_3.svg'}
                  alt={'new logo footer'}
                  width={180}
                  height={70}
                />
              </div>
            </div>
          </Grid>
          <Grid size={{ md: 8.5, xs: 12 }}>
            <Box sx={{ flexGrow: 1 }}>
              <Grid container spacing={{ xl: 3, xs: 2 }}>
                <Grid size={{ xl: 3, xs: 6 }}>
                  <FooterMenuList
                    title={'ติดต่อเรา'}
                    menuList={[
                      'เวลาทำการ: 9:30am - 6:30pm',
                      'โทร. 02  339  5436',
                      'LINE: @Digiboxs',
                      'อีเมล: <EMAIL>',
                    ]}
                  />
                </Grid>
                <Grid size={{ xl: 3, xs: 6 }}>
                  <FooterMenuList
                    title={'เกี่ยวกับเรา'}
                    menuList={[
                      'โรงพิมพ์ของเรา',
                      'มาตรฐาน ISO 9001',
                      'มาตรฐาน ISO 9100',
                      'มาตรฐาน GMP',
                    ]}
                    isDisc
                  />
                </Grid>
                <Grid size={{ xl: 3, xs: 6 }}>
                  <FooterMenuList
                    title={'สินค้า'}
                    menuList={[
                      'นามบัตร',
                      'บรรจุภัณฑ์',
                      'สติ๊กเกอร์',
                      'ใบปลิว/แผ่นพับ',
                    ]}
                    isDisc
                  />
                </Grid>
                <Grid size={{ xl: 3, xs: 6 }}>
                  <FooterMenuList
                    title={'การช่วยเหลือ'}
                    menuList={[
                      'วิธีการสั่งซื้อ',
                      'คำถามที่พบบ่อย',
                      'Dieline templates',
                      'Virtual 3D models',
                    ]}
                    isDisc
                  />
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <div className="footer-bottom">
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={{ xl: 3, xs: 2 }}>
            <Grid size={{ md: 3.5, xs: 12 }}>
              <div
                className={
                  'flex items-center justify-start lg:justify-start  gap-[1rem]'
                }
              >
                {[...Array(4)].map((_, i) => (
                  <Image
                    key={i}
                    src={`/images/icon_footer_${i + 1}.svg`}
                    alt={'icon footer'}
                    width={25}
                    height={25}
                  />
                ))}
              </div>
            </Grid>
            <Grid size={{ md: 8.5, xs: 12 }}>
              <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={{ xl: 3, xs: 2 }}>
                  <Grid size={{ md: 6, xs: 12 }}>
                    <ul className={'policy-list'}>
                      <li>Cookie</li>
                      <li>Privacy Policy</li>
                      <li>Terms of service</li>
                    </ul>
                  </Grid>
                  <Grid size={{ md: 6, xs: 12 }}>
                    <p className={'reserved'}>
                      © 2025 Digiboxs. All rights reserved.
                    </p>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </div>
    </FooterStyle>
  );
};

export default Footer;
