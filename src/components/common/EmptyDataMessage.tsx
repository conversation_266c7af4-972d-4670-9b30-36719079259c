import { AnimatePresence, motion } from 'motion/react';
import React from 'react';
import styled from 'styled-components';

const EmptyDataMessageStyle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  width: 100%;
  background: #fafafa;
  color: #bdbdbd;
  height: 6rem;
  font-size: 14px;
`;
type Props = {
  message: string;
  height?: string;
  isToggled?: boolean;
};
const EmptyDataMessage = ({ message, height, isToggled }: Props) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={isToggled ? 'show' : 'empty'}
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: -10, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <EmptyDataMessageStyle
          {...(height ? { style: { height: height } } : {})}
        >
          {message}
        </EmptyDataMessageStyle>
      </motion.div>
    </AnimatePresence>
  );
};

export default EmptyDataMessage;
