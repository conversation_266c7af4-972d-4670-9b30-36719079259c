import { motion } from 'motion/react';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

const TabValueStyle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  border-radius: 40px;
  padding: 0.3rem;
  background: #eee;
  margin: auto auto 1rem;
  &.min-992-d-none {
    @media (min-width: 992px) {
      display: none;
    }
  }
  > div {
    border-radius: 40px;
    padding: 1rem;
    width: 100%;
    cursor: pointer;
    height: 36px;
    &.active {
      position: relative;
      span {
        position: absolute;
        z-index: 10;
        color: #121212;
        font-weight: bold;
      }
    }
    &.disabled {
      pointer-events: none;
    }
    span {
      color: #616161;
    }
  }
  @media (max-width: 425px) {
    > div {
      > span {
        font-size: 12px;
      }
    }
  }
`;
type Props = {
  className?: string;
  tabs: {
    value: number;
    label: string;
    disabled?: boolean;
  }[];
  tabValue: string;
  setTabValue: (value: string) => void;
  maxWidth?: string;
  layoutIdKey: string;
};
const TabValue = ({
  tabs,
  tabValue,
  setTabValue,
  maxWidth,
  className,
  layoutIdKey,
}: Props) => {
  const [selectedTab, setSelectedTab] = useState<number>();
  useEffect(() => {
    setSelectedTab(Number(tabValue));
  }, [tabValue]);
  return (
    <TabValueStyle
      {...(className ? { className } : {})}
      style={maxWidth ? { maxWidth: maxWidth } : {}}
    >
      {tabs.map((item) => (
        <motion.div
          className={`${item.value === selectedTab ? 'active' : ''} ${item.disabled ? 'disabled' : ''}`}
          key={item.label}
          initial={false}
          animate={{
            backgroundColor: item.value === selectedTab ? '#fff' : '#eee',
          }}
          style={tab}
          onClick={() => {
            setSelectedTab(item.value);
            setTabValue(String(item.value));
          }}
        >
          <span>{item.label}</span>
          {item.value === selectedTab ? (
            <motion.div
              style={underline}
              layoutId={layoutIdKey}
              id={`underline-${item.label}`}
              transition={{ duration: 0.2 }}
            />
          ) : null}
        </motion.div>
      ))}
    </TabValueStyle>
  );
};
const tabsStyles: React.CSSProperties = {
  listStyle: 'none',
  padding: 0,
  margin: 0,
  fontWeight: 500,
  fontSize: 14,
  textAlign: 'center',
};

const tab: React.CSSProperties = {
  ...tabsStyles,
  borderRadius: 40,
  width: '100%',
  padding: '10px 15px',
  position: 'relative',
  background: 'white',
  cursor: 'pointer',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flex: 1,
  minWidth: 0,
  userSelect: 'none',
  color: '#0f1115',
};
const underline: React.CSSProperties = {
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: 36,
  borderRadius: 40,
  background: '#fff',
  zIndex: 5,
};
export default TabValue;
