import React from 'react';

type Props = {
  message: string;
  heightSize?: string;
};
const NotFoundData = ({ message, heightSize }: Props) => {
  return (
    <div
      className={'w-full flex items-center justify-center text-[#eee]'}
      {...(heightSize
        ? { style: { height: heightSize } }
        : { style: { height: '190px' } })}
    >
      {message}
    </div>
  );
};

export default NotFoundData;
