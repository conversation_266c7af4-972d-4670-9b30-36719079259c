import React from 'react';
import Dialog from '@mui/material/Dialog';
import styled from 'styled-components';
import { Button, Slide } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';

import { TransitionProps } from '@mui/material/transitions';
import { useWindowSize } from 'usehooks-ts';

const ModalAppCustomStyle = styled.div`
  min-width: 600px;
  position: relative;
  header {
    position: sticky;
    top: 0;
    padding: 16px;
    z-index: 20;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
      font-size: 22px;
      font-weight: 700;
    }
    span {
      font-size: 22px;
    }
    button {
      min-width: unset;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      svg {
        color: #212121;
      }
    }
  }
  .content {
    padding: 0 16px 16px 16px;
  }
  @media (max-width: 768.98px) {
    min-width: 100%;
  }
  @media (max-width: 575.98px) {
    .content {
      position: relative;
      min-height: 100vh;
    }
  }

  @media (max-width: 425px) {
    header {
      padding: 0.8rem 0.7rem;
      h2 {
        font-size: 20px;
      }
    }
  }
`;
const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="left" ref={ref} {...props} />;
});
type Props = {
  open: boolean;
  handleClose: () => void;
  children: React.ReactNode;
  title: string;
  value?: string;
};
const ModalAppCustom = ({
  open,
  handleClose,
  children,
  title,
  value,
}: Props) => {
  const windowSize = useWindowSize();
  return (
    <Dialog
      className={'modal-app-custom'}
      open={open}
      onClose={handleClose}
      {...(windowSize.width < 768.98
        ? { slots: { transition: Transition } }
        : {})}
    >
      <ModalAppCustomStyle>
        <header>
          <div className={'flex items-center justify-start gap-1'}>
            <h2>{title}</h2>
            {value && <span>{value}</span>}
          </div>
          <Button onClick={handleClose}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <div className={'content'}>{children}</div>
      </ModalAppCustomStyle>
    </Dialog>
  );
};

export default ModalAppCustom;
