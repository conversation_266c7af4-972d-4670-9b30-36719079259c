import React from 'react';
import TextField from '@mui/material/TextField';
import styled from 'styled-components';

const TextFieldInputStyle = styled.div`
  width: 100%;
  .MuiFormControl-root {
    .MuiInputBase-root {
      background: #fff;
      border-radius: 10px;
      &:hover {
        fieldset {
          border-color: #eee;
        }
      }
      &.Mui-focused {
        fieldset {
          border-color: #eee;
        }
      }
    }
    fieldset {
      border-color: #eee;
    }
  }
`;
type Props = {
  placeholder?: string;
};
const TextFieldInput = ({ placeholder }: Props) => {
  return (
    <TextFieldInputStyle>
      <TextField
        variant="outlined"
        placeholder={placeholder}
        size="small"
        className={'w-full placeholder:text-[14px]'}
      />
    </TextFieldInputStyle>
  );
};

export default TextFieldInput;
