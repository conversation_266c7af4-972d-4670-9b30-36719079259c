import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import getConfig from 'next/config';
import Button from '@mui/material/Button';
import styled from 'styled-components';
import * as motion from 'motion/react-client';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
import { useSelector } from 'react-redux';
import { dataAddCartSelector } from '@/store/reducers/cartSlice';
import { AnimatePresence } from 'motion/react';
import { useRouter } from 'next/router';

const { publicRuntimeConfig } = getConfig();

const NavbarStyle = styled.div`
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;

  nav {
    padding: 0.8rem;
    background: #000;
    color: #fff;
    min-width: 100%;
    display: flex;
    align-items: center;
    .menu-in-mobile {
      display: none;
    }
  }
  .logo {
    display: flex;
    align-items: center;
    column-gap: 10px;
    width: 20%;
    .logo-link {
      display: flex;
      align-items: center;
      column-gap: 8px;
    }
    span {
      font-size: 1.6rem;
      font-weight: 700;
    }
  }
  .menu {
    text-align: center;
    width: 60%;
    ul {
      color: #9e9e9e;
      display: flex;
      column-gap: 30px;
      font-size: 14px;
      justify-content: center;
      li {
        a {
          transition: 0.3s;
          color: #fff !important;
          &:hover {
            background: linear-gradient(
              94.56deg,
              #00fffb -30.81%,
              #0050ff 62.3%,
              #02277a 157.23%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
  .btn {
    width: 20%;
    text-align: right;
    .btn-icon {
      position: relative;
      min-width: unset;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      color: #ddd;
      .box-amount {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 18px;
        height: 18px;
        text-align: center;
        font-size: 10px;
        border-radius: 50%;
        background: #d32f2f;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .btn-production {
      background: #fff;
      padding: 8px 24px;
      border-radius: 8px;
      font-size: 16px;
      width: 120px;
      color: #212121;
      font-weight: 700;
      &:active {
        opacity: 0.8;
      }
    }
  }
  @keyframes topLineCross {
    0% {
      transform: translateY(-5px);
    }
    50% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(0px) rotate(45deg);
    }
  }

  @keyframes bottomLineCross {
    0% {
      transform: translateY(5px);
    }
    50% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(0px) rotate(-45deg);
    }
  }

  @keyframes topLineBurger {
    0% {
      transform: translateY(0px) rotate(45deg);
    }
    50% {
      transform: rotate(0deg);
    }
    100% {
      transform: translateY(-5px) rotate(0deg);
    }
  }

  @keyframes bottomLineBurger {
    0% {
      transform: translateY(0px) rotate(-45deg);
    }
    50% {
      transform: rotate(0deg);
    }
    100% {
      transform: translateY(5px) rotate(0deg);
    }
  }
  @media (min-width: 1270px) {
    nav {
      .box-nav-mobile {
        display: none;
      }
    }
  }
  @media (max-width: 1269.98px) {
    nav {
      .logo,
      .menu,
      .btn {
        display: none;
      }
      position: relative;
      .menu-in-mobile {
        position: fixed;
        top: 0;
        left: 1300px;
        width: 100%;
        height: 100%;
        background: #000;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-y: auto;
        header {
          margin-top: 4rem;
        }
        .btn-sidebar {
          margin-top: 1rem;
          background: #000;
          //border-top: 1px solid #616161;
          width: 100%;
          padding: 1rem;
          //.btn-production {
          //  width: 100%;
          //  background: #601feb;
          //  color: #fff;
          //  border-radius: 20px;
          //  height: 40px;
          //}
          button {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 6px;
            width: 100%;
            background: #fff !important;
            color: #0050ff !important;
            font-weight: 600;
            font-size: 18px;
          }
        }
        &.showMenu {
          left: 0;
        }
        header {
          padding-bottom: 20px;
          img {
            margin: auto;
            padding-bottom: 1rem;
          }
          span {
            font-size: 2rem;
            font-weight: 700;
          }
        }
        .nav-body {
          text-align: center;
          font-size: 20px;
          display: flex;
          flex-direction: column;
          gap: 20px;
          width: 100%;
          span {
            color: #0050ff;
          }
          ul {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
        }
      }
      .box-nav-mobile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .logo-center {
          img {
            margin-right: auto;
          }
        }
        .menu-left {
          display: flex;
          align-items: center;
          justify-content: end;
          gap: 0.8rem;
          .burger-menu {
            position: relative;
            background: none;
            width: 40px;
            height: 40px;
            padding: 0;
            opacity: 1;
            outline: none;
            z-index: 9999;
            background: #ffffff1a;
            border: 1px solid #424242;
            border-radius: 8px;
            &:hover {
              opacity: 1;
            }
            span {
              display: block;
              width: 100%;
              height: 1px;
              position: absolute;
              top: 0;
              left: 0;
              transform: translateY(22px);
              transition:
                transform 0.5s ease,
                border-color 0.5s ease 0.3s;
              &:before {
                background: #b9e901;
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                border-radius: 20px;
                transform: translateY(-5px);
                animation-name: topLineBurger;
                animation-duration: 0.4s;
                animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
                animation-fill-mode: forwards;
              }
              &:after {
                background: #b9e901;
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                border-radius: 20px;
                transform: translateY(5px);
                animation-name: bottomLineBurger;
                animation-duration: 0.4s;
                animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
                animation-fill-mode: forwards;
              }
            }
            &.opened {
              span {
                border-color: transparent;
                transition: border-color 0.3s ease;
                &:before {
                  animation-name: topLineCross;
                  animation-duration: 0.4s;
                  animation-timing-function: cubic-bezier(
                    0.645,
                    0.045,
                    0.355,
                    1
                  );
                  animation-fill-mode: forwards;
                }
                &:after {
                  animation-name: bottomLineCross;
                  animation-duration: 0.4s;
                  animation-timing-function: cubic-bezier(
                    0.645,
                    0.045,
                    0.355,
                    1
                  );
                  animation-fill-mode: forwards;
                }
              }
            }
          }
        }
      }
    }
  }
  @media (max-width: 575.98px) {
    nav {
      padding: 0.8rem 0.5rem;
    }
  }
  @media (max-width: 425px) {
    nav {
      .box-nav-mobile {
        .logo-center {
          img {
            width: 150px;
          }
        }
        .menu-left {
          img {
            width: 25px !important;
          }
          .burger-menu {
            width: 35px;
            height: 35px;
            svg {
              font-size: 22px;
            }
          }
        }
      }
    }
  }
  @media (max-width: 375px) {
    nav {
      .box-nav-mobile {
        .logo-center {
          img {
            width: 120px;
          }
        }
      }
    }
  }
`;

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const { cart } = useSelector(dataAddCartSelector);
  const router = useRouter();
  const goToNewPage = () => {
    window.location.href = `${publicRuntimeConfig.LINK_REDIRECT_URL}/customize`;
  };
  const menuList = [
    {
      name: 'สินค้า',
      pathName: '/product',
    },
    {
      name: 'คูปอง',
      pathName: '/coupon',
    },
    {
      name: 'เกี่ยวกับเรา',
      pathName: '/',
    },
    {
      name: 'บทความ',
      pathName: '/blog',
    },
    {
      name: 'โปรโมชั่น',
      pathName: '/',
    },
    {
      name: 'คูปองส่วนลด',
      pathName: '/',
    },
    {
      name: 'สินค้าลดราคา',
      pathName: '/',
    },
    {
      name: 'ดาวน์โหลด',
      pathName: '/',
    },
    {
      name: '3D models',
      pathName: '/',
    },
  ];
  const menuIcon = [
    {
      imageUrl: '/icons/search.svg',
    },
    {
      imageUrl: '/icons/icon_shopping_cart.svg',
      amountCart: cart.length,
      pathname: '/cart',
    },
    {
      imageUrl: '/icons/account_circle.svg',
    },
  ];

  return (
    <NavbarStyle>
      <nav>
        <div className={'logo'}>
          <Link href={'/'}>
            <div className={'logo-link'}>
              <Image
                src="/images/new_logo.svg"
                alt={'logo'}
                width={170}
                height={35}
              />
            </div>
          </Link>
        </div>
        <div className={'menu'}>
          <ul>
            {menuList.map((item: any, index: number) => (
              <li key={index}>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.8 }}
                >
                  <Link href={item.pathName}>{item.name}</Link>
                </motion.div>
              </li>
            ))}
          </ul>
        </div>
        <div className={'btn flex items-center justify-end gap-[5px]'}>
          {menuIcon.map((item: any, index: number) => {
            return (
              <Button
                className={'btn-icon'}
                variant={'text'}
                key={index}
                onClick={() => router.push(item.pathname || '/')}
              >
                <Image
                  src={item.imageUrl}
                  alt={'icon'}
                  width={28}
                  height={28}
                />
                {item.amountCart > 0 && (
                  <AnimatePresence mode="wait">
                    <motion.div
                      className={'box-amount'}
                      key={
                        item.amountCart > 0 ? `add-${item.amountCart}` : 'empty'
                      }
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -10, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.amountCart}
                    </motion.div>
                  </AnimatePresence>
                )}
              </Button>
            );
          })}
        </div>
        <div className={'box-nav-mobile'}>
          <div className={'logo-center'}>
            <Link href={'/'}>
              <Image
                src="/images/new_logo.svg"
                alt={'logo'}
                width={170}
                height={35}
              />
            </Link>
          </div>
          <div className={'menu-left'}>
            {menuIcon.map((item: any, index: number) => (
              <div key={index}>
                <Link href={`${item.pathname || '/'}`}>
                  <Image
                    src={item.imageUrl}
                    alt={'logo'}
                    width={30}
                    height={30}
                  />
                </Link>
              </div>
            ))}
            <button
              className={`burger-menu ${isOpen ? 'opened' : ''}`}
              onClick={() => setIsOpen(!isOpen)}
            >
              <MenuRoundedIcon />
            </button>
          </div>
        </div>
        <div className={`menu-in-mobile ${isOpen ? 'showMenu' : ''}`}>
          <div className={'w-full text-center'}>
            <header>
              <Link href={'/'} onClick={() => setIsOpen(!isOpen)}>
                <Image
                  src="/images/new_logo.svg"
                  alt={'logo'}
                  width={200}
                  height={200}
                />
              </Link>
            </header>
            <div className="nav-body">
              <span>MENU</span>
              <ul>
                {menuList.map((item: any, index: number) => (
                  <li key={index}>
                    <Link
                      href={item.pathName}
                      onClick={() => setIsOpen(!isOpen)}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className={'btn-sidebar'}>
            {/* <button className={'btn-production'} onClick={goToNewPage}> */}
            {/*  สั่งผลิต */}
            {/* </button> */}
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.8 }}>
              <Button
                onClick={goToNewPage}
                variant={'contained'}
                startIcon={<div style={{ width: '40px', height: '20px' }} />}
                endIcon={<ArrowForwardRoundedIcon />}
              >
                สั่งผลิต
              </Button>
            </motion.div>
          </div>
        </div>
      </nav>
    </NavbarStyle>
  );
}
