import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'motion/react';
import { isEmpty } from 'lodash';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';

const TabCategoryStyle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  margin: auto auto 1rem;
  > div {
    //border-radius: 8px;
    //padding: 1rem;
    //width: 100%;
    //cursor: pointer;
    .btn-tab {
      height: 36px;
      &.active {
        position: relative;
        span {
          position: absolute;
          z-index: 10;
          color: #fff;
        }
      }
    }

    span {
      color: #000;
    }
  }
`;
type Props = {
  tabs: any[];
  tabValue: string;
  setTabValue: (value: string) => void;
  layoutIdKey: string;
};
const TabCategory = ({ tabs, tabValue, setTabValue, layoutIdKey }: Props) => {
  const [selectedTab, setSelectedTab] = useState(Number(tabValue));
  return (
    !isEmpty(tabs) && (
      <TabCategoryStyle>
        <Swiper
          slidesPerView={3.2}
          spaceBetween={10}
          className="mySwiper"
          breakpoints={{
            425: {
              slidesPerView: 3.8,
            },
            576: {
              slidesPerView: 4.8,
            },
          }}
        >
          {tabs?.map((item) => (
            <SwiperSlide key={item.label}>
              <motion.div
                className={`${item.value === selectedTab ? 'active' : ''} btn-tab`}
                initial={false}
                animate={{
                  backgroundColor:
                    item.value === selectedTab ? '#000' : '#F5F5F5',
                }}
                style={tab}
                onClick={() => {
                  setSelectedTab(item.value);
                  setTabValue(String(item.value));
                }}
              >
                <span className={'truncate'}>{item.label}</span>
                {item.value === selectedTab ? (
                  <motion.div
                    style={underline}
                    layoutId={layoutIdKey}
                    id={`${item.label}-underline`}
                  />
                ) : null}
              </motion.div>
            </SwiperSlide>
          ))}
        </Swiper>
      </TabCategoryStyle>
    )
  );
};
const tabsStyles: React.CSSProperties = {
  listStyle: 'none',
  padding: 0,
  margin: 0,
  fontWeight: 500,
  fontSize: 14,
  textAlign: 'center',
};

const tab: React.CSSProperties = {
  ...tabsStyles,
  borderRadius: 8,
  minWidth: '85px',
  padding: '10px 5px',
  position: 'relative',
  background: '#fff',
  cursor: 'pointer',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  // flex: 1,
  userSelect: 'none',
  color: '#0f1115',
};
const underline: React.CSSProperties = {
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: 36,
  borderRadius: 8,
  background: '#000',
  zIndex: 5,
};
export default TabCategory;
