import React from 'react';
import styled from 'styled-components';

const FooterMenuListStyle = styled.div`
  h3 {
    font-size: 24px;
    font-weight: 600;
    color: #0050ff;
  }
  ul {
    padding-top: 1.4rem;
    display: flex;
    flex-direction: column;
    gap: 1.3rem;
    li {
      font-size: 14px;
      &.dot-list {
        margin-left: 1.2rem;
        list-style: disc !important;
      }
    }
  }
  @media (max-width: 575.98px) {
    h3 {
      font-size: 20px;
    }
    ul {
      li {
        font-size: 14px;
      }
    }
  }
`;
type Props = {
  title: string;
  menuList: string[];
  isDisc?: boolean;
};
const FooterMenuList = ({ title, menuList, isDisc }: Props) => {
  return (
    <FooterMenuListStyle>
      <h3>{title}</h3>
      <ul>
        {menuList.map((menuName: string, index: number) => (
          <li key={index} className={isDisc ? 'dot-list' : ''}>
            {menuName}
          </li>
        ))}
      </ul>
    </FooterMenuListStyle>
  );
};

export default FooterMenuList;
