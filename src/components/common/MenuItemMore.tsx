import React from 'react';
import { Button } from '@/components/ui/button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { motion } from 'motion/react';

type Props = {
  idButton: string;
  idMenu: string;
  name: React.ReactNode;
  menuItems?: { name: string; icon: React.ReactNode; onAction: () => void }[];
};
const MenuItemMore = ({ idButton, idMenu, name, menuItems }: Props) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <div>
      <motion.div whileHover={{ scale: 1 }} whileTap={{ scale: 0.8 }}>
        <Button
          id={idButton}
          aria-controls={open ? idMenu : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          className={'p-2 rounded-[8px]'}
          variant={'outline'}
        >
          {name}
        </Button>
      </motion.div>
      <Menu
        id={idMenu}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          list: {
            'aria-labelledby': idButton,
          },
        }}
      >
        {menuItems?.map((item, index) => (
          <MenuItem
            onClick={() => {
              item.onAction();
              handleClose();
            }}
            key={index}
            className={'box-menu-more'}
          >
            <div className={'flex items-center gap-2'}>
              <div>{item.icon}</div>
              <div>{item.name}</div>
            </div>
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default MenuItemMore;
