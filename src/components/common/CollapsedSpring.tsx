import React from 'react';
import { AnimatePresence, motion } from 'motion/react';

type Props = {
  isToggled: boolean;
  children?: React.ReactNode;
};
const CollapsedSpring = ({ isToggled, children }: Props) => {
  return (
    <AnimatePresence initial={false}>
      {isToggled && (
        <motion.div
          key={isToggled ? 'open' : 'hide'}
          initial="collapsed"
          animate="open"
          exit="collapsed"
          variants={{
            open: {
              opacity: 1,
              height: 'auto',
            },
            collapsed: {
              opacity: 0,
              height: 0,
              transition: {
                damping: 20,
              },
            },
          }}
          transition={{
            type: 'spring',
            damping: 12,
            duration: 0.4,
            ease: 'easeInOut',
          }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CollapsedSpring;
