import React from 'react';
import Button from '@mui/material/Button';
import { motion } from 'motion/react';
import styled, { css } from 'styled-components';
import { isUndefined } from 'lodash';
import { FormikProps } from 'formik';
import { DataFormDetailType } from '@/store/type/cart';

const ButtonCustomStyle = styled.div<{
  widthFull?: boolean;
  onlyIcon: boolean;
}>`
  ${({ widthFull }) =>
    widthFull
      ? css`
          width: 100%;
        `
      : css`{width: fit-content;}`};
  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  button {
    text-transform: unset !important;
    border-radius: 8px;
    padding: 0.4rem 0.8rem;
    ${({ onlyIcon }) =>
      onlyIcon
        ? css`
            padding: unset;
            min-width: unset;
            width: 40px;
            height: 40px;
            > span {
              margin: auto;
            }
          `
        : css`{}`};

    &.font-bold {
      font-weight: 700;
    }
    &.rounded-full {
      border-radius: 9999px;
    }
    &.primary {
      border: 1px solid #0050ff !important;
      color: #fff;
      background: #0050ff;
    }
    &.secondary {
      background: #fff;
      border: 1px solid #eeeeee !important;
      color: #000;
      &:hover {
        background-color: #fff;
      }
    }
    &.tertiary {
      background: #000;
      border: 1px solid #000 !important;
      color: #fff;
    }
    &.cancel {
      background: #d32f2f !important;
      border: 1px solid #d32f2f !important;
      svg {
        color: #fff;
        font-size: 24px;
      }
    }
    &.confirm {
      background: #8bc34a !important;
      border: 1px solid #8bc34a !important;
      svg {
        color: #fff;
        font-size: 24px;
      }
    }
    &.btn-add-to-cart {
      width: 180px;
      height: 40px;
      font-size: 16px;
    }
    &.text-0050ff {
      color: #0050ff;
    }
  }
  label {
    border-radius: 8px;
    &.secondary {
      background: #fff;
      border: 1px solid #eeeeee !important;
      color: #000;
      &:hover {
        background-color: #fff;
      }
    }
    &.rounded-full {
      border-radius: 9999px;
    }
  }
  @media (max-width: 575.98px) {
    button {
      font-size: 14px !important;
      svg {
        font-size: 16px !important;
      }
    }
  }
  @media (max-width: 375px) {
    button {
      font-size: 12px !important;
      svg {
        font-size: 14px !important;
      }
    }
  }
`;
type Props = {
  formik?: FormikProps<DataFormDetailType>;
  typeFileUpload?: boolean;
  name?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  btnType?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'text'
    | 'cancel'
    | 'confirm';
  classList?: string;
  theme?: string;
  onAction?: () => void;
  widthFull?: boolean;
  type?: 'button' | 'submit';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
};
const ButtonCustom = ({
  formik,
  typeFileUpload,
  name,
  startIcon,
  endIcon,
  btnType,
  classList,
  theme,
  onAction,
  widthFull,
  type,
  size,
  disabled,
}: Props) => {
  const variantType = (
    type: 'primary' | 'secondary' | 'tertiary' | 'text' | 'cancel' | 'confirm',
  ) => {
    switch (type) {
      case 'primary':
        return 'contained';
      case 'secondary':
        return 'outlined';
      case 'text':
        return 'text';
      default:
        return 'outlined';
    }
  };
  return (
    <ButtonCustomStyle
      className={`${btnType || ''} ${disabled ? 'disabled' : ''}`}
      widthFull={widthFull}
      onlyIcon={isUndefined(name)}
    >
      <motion.div whileHover={{ scale: 1 }} whileTap={{ scale: 0.8 }}>
        <Button
          className={`${classList || ''} ${btnType || ''} ${widthFull ? 'w-full' : ''}`}
          {...(type ? { type: type } : {})}
          {...(onAction ? { onClick: () => onAction() } : {})}
          {...(theme ? { style: { color: theme } } : {})}
          {...(btnType ? { variant: variantType(btnType) } : '')}
          {...(startIcon ? { startIcon: startIcon } : '')}
          {...(endIcon ? { endIcon: endIcon } : '')}
          {...(typeFileUpload
            ? { component: 'label', role: undefined, tabIndex: -1 }
            : {})}
          {...(size ? { size: size } : {})}
        >
          {name && name}
          {typeFileUpload && (
            <VisuallyHiddenInput
              type="file"
              onChange={(event) => {
                if (formik) {
                  formik.setFieldValue(
                    'artwork.file',
                    event.target.files ? event.target.files[0] : null,
                  );
                }
              }}
              multiple
            />
          )}
        </Button>
      </motion.div>
    </ButtonCustomStyle>
  );
};
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});
export default ButtonCustom;
