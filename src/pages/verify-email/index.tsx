import styled from 'styled-components';

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/router';
import { useSearchParams } from 'next/navigation';

const VerifyEmailStyle = styled.div`
  display: flex;
  flex-direction: column;
  text-align: center;
  height: calc(100vh - 80px);
  justify-content: center;
  width: 681px;
  .container-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 40px;
    .sub-header {
      color: white;
      font-size: 36px;
      font-weight: 700;
    }
    .sub-title {
      span {
        color: white;
        font-size: 20px;
        font-weight: 400;
      }
      .sub-title-email {
        color: white;
        font-size: 20px;
        font-weight: 700;
        margin: 0 4px;
      }
    }
    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      width: 100%;

      span {
        color: white;
        font-size: 14px;
        font-weight: 400;
      }
    }
    .confirm-button {
      background: #ff4f00;
      width: 200px;
      height: 40px;
      padding: 8px 24px;
      border-radius: 8px;
    }
  }
  @media screen and (max-width: 991px) {
    .container-box {
      padding: 0 1rem;
    }
  }
  @media screen and (max-width: 547px) {
    .container-box {
      .sub-title {
        display: flex;
        flex-direction: column;
      }
      .content {
        display: inline;
      }
    }
  }
  @media screen and (max-width: 465px) {
    .container-box {
      .sub-header {
        font-size: 24px;
      }
      .sub-title {
        display: flex;
        flex-direction: column;
      }
      .content {
        display: inline;
      }
    }
  }
  @media screen and (max-width: 425px) {
    .container-box {
      .sub-title {
        span {
          font-size: 18px !important;
        }
      }
      .content {
        span {
          br {
            display: none;
          }
        }
      }
    }
  }
  @media screen and (max-width: 375px) {
    .container-box {
      .content {
        span {
          font-size: 12px !important;
        }
      }
    }
  }
`;

const verifyEmailPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const email = searchParams.get('email');
  return (
    <VerifyEmailStyle>
      <div className="container-box">
        <Image
          priority
          className=""
          src="/icons/email.png"
          alt=""
          height={80}
          width={80}
        />
        <span className="sub-header">ยืนยันที่อยู่อีเมลของคุณ</span>
        <div className="sub-title">
          <span>กรุณายืนยันว่า</span>
          <span className="sub-title-email">{email}</span>
          <span>เป็นที่อยู่อีเมลของคุณ</span>
        </div>
        <div className="content">
          <span>
            การยืนยันอีเมลของคุณทำให้คุณสามารถเข้าถึง Digibox ได้มากขึ้น <br />{' '}
            คลิกปุ่มด้านล่างเพื่อเข้าร่วมกับเรา
          </span>
        </div>
        <Button className="confirm-button" onClick={() => router.push('/')}>
          ยืนยันอีเมล
        </Button>
      </div>
    </VerifyEmailStyle>
  );
};

export default verifyEmailPage;
