import React, { useEffect, useRef, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as yup from 'yup';
import { Textarea } from '@/components/ui/textarea';

import 'react-datetime-picker/dist/DateTimePicker.css';
import 'react-calendar/dist/Calendar.css';
import 'react-clock/dist/Clock.css';
import DateTimePicker from 'react-datetime-picker';
import apiOrder from '@/services/order';
import { OrderType } from '@/types/order';
import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import { numberWithCommas } from '@/utils/numberFormat';
import { handleCopyToClipboard } from '@/utils/copyContent';
import CustomCalendarIcon from '@/components/customUI/CustomCalendarIcon';

const scaleUpAnimate = keyframes`
  from {
    transform: scale(0.6) translateY(0);
    opacity: 0;
  }
  20% {
    transform: scale(0.6) translateY(0);
    opacity: 0.1;
  }
  60% {
    transform: scale(1) translateY(0px);
    opacity: 1;
  }
  to {
    transform: scale(1.5) translateY(0px);
    opacity: 0;
  }
`;

const PaymentStyle = styled.div`
  width: 100%;
  padding-top: 80px;
  .header-container {
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    position: relative;
    .subheader {
      font-size: 36px;
      font-weight: 700;
      color: #212121;
    }
    .btn-close {
      position: absolute;
      right: 30px;
    }
  }
  .order-info-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40%;
    margin: 48px auto;
    .subheader-bold {
      font-size: 36px;
      font-weight: 700;
    }
    .caption {
      font-size: 12px;
      font-weight: 400;
    }
    .order-price-wrapper {
      padding: 32px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      border-top: 2px dotted #212121;
      border-bottom: 2px dotted #212121;
      .content {
        font-size: 14px;
        font-weight: 400;
      }
      .content-bold {
        font-size: 14px;
        font-weight: 700;
      }
    }
    .total-price-wrapper {
      padding: 38px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      border-bottom: 1px solid #212121;
      .content-bold {
        font-size: 14px;
        font-weight: 700;
      }
      .subtitle {
        font-size: 20px;
        font-weight: 700;
      }
    }
    .payment-wrapper {
      width: 100%;
      margin-top: 48px;
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
        margin: 32px 0;
      }
      .button-payment {
        width: 50%;
        border: 1px solid #dbe2e5;
        background-color: white;
        color: black;
        &:hover {
          background-color: white;
          color: black;
        }
      }
      .button-payment.selected {
        width: 50%;
        border: 1px solid #605dec !important;
        background-color: #fafaff;
        color: #605dec;
        &:hover {
          background-color: #7c7cf0;
          color: white;
        }
      }
      .promptpay-wrapper {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        .promptpay-head-wrapper {
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          padding: 14px;
          display: flex;
          justify-content: center;
          background-color: #00437a;
        }
        .promptpay-content-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          padding: 24px;
          .promptpay-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            .subtitle-bold {
              font-size: 20px;
              font-weight: 700;
              color: #605dec;
              margin: 24px 0 0 0;
            }
            .caption {
              font-size: 12px;
              font-weight: 400;
              color: #bdbdbd;
            }
          }
        }
        .promptpay-footer-wrapper {
          background-color: #f5f7f8;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 24px;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          .box-row {
            display: flex;
            align-items: center;
            button {
              padding: 5px 10px;
              height: 28px;
            }
          }
          .content {
            font-size: 14px;
            font-weight: 400;
          }
          .content-bold {
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
      .bank-wrapper {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        padding: 24px;
        display: flex;
        .bank-content-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 20px;
            font-weight: 400;
          }
          .subtitle-bold {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
          }
        }
      }
    }
    .upload-wrapper {
      width: 100%;
      margin-bottom: 36px;
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
        margin: 32px 0;
      }
      .upload-content {
        padding: 16px;
        border: 2px dotted #dbe2e5;
        border-spacing: 2px;
        border-radius: 8px;
        .image-wrapper {
          border-radius: 8px;
          margin-bottom: 16px;
          max-height: inherit;
        }
        .upload-placeholder {
          display: flex;
          justify-content: center;
          margin: 40px 0;
          font-size: 12px;
          font-weight: 400;
          color: #bdbdbd;
        }
        .upload-button {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
    .formdata-wrapper {
      width: 100%;
      .confirm-button {
        width: 100%;
        background-color: #605dec;
        font-size: 20px;
        font-weight: 700;
      }
    }
  }
  .react-datetime-picker {
    height: 40px;
    .react-datetime-picker__wrapper {
      border: 1px solid #dbe2e5;
      border-radius: 6px;
      .react-datetime-picker__inputGroup {
        padding: 0 8px;
        font-size: 14px;
      }
    }
  }
  .success-container {
    width: 100%;
    justify-content: center;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 80px);
    position: relative;
    .flare {
      position: absolute;
      transform: translate(-50%, -50%);
      animation: ${scaleUpAnimate} 1s linear infinite;
    }
    .success-wrapper {
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      .subtitle {
        font-size: 20px;
        font-weight: 700;
      }
      .subheader {
        margin-top: 16px;
        font-size: 36px;
        font-weight: 700;
      }
      .content {
        font-size: 14px;
        font-weight: 400;
      }
      .content-bold {
        font-size: 14px;
        font-weight: 700;
      }
      .button {
        width: 345px;
        margin-top: 48px;
        background-color: #ff4f00;
        padding: 16px;
        border-radius: 8px;
        font-size: 20px;
        font-weight: 700;
        :hover {
          background-color: #ff6b33;
        }
      }
    }
  }
  @media (min-width: 426px) {
    .success-wrapper {
      .content {
        br {
          display: none;
        }
      }
    }
  }
  @media (max-width: 991px) {
    .order-info-wrapper {
      width: 90%;
    }
  }
  @media (max-width: 576px) {
    .promptpay-wrapper {
      .promptpay-footer-wrapper {
        padding: 16px !important;
        display: block !important;
        .box-acc-number {
          border: none;
          margin: 0;
          padding: 0;
        }
      }
    }
  }
  @media (max-width: 425px) {
    .success-wrapper {
      .content {
        text-align: center !important;
      }
      .button {
        &.w-100 {
          width: 100% !important;
        }
      }
    }
  }
  @media (max-width: 375px) {
    .order-info-wrapper {
      .subheader-bold {
        font-size: 30px;
      }
    }
    .success-wrapper {
      .content {
        br {
          display: none;
        }
      }
    }
  }
`;

const PaymentPage = () => {
  const router = useRouter();
  const { id } = router.query;
  dayjs.extend(buddhistEra);
  dayjs.locale('th');
  const [paymentData, setPaymentData] = useState<OrderType>();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [selectedPayment, setSelectedPayment] = useState(1);
  const [loading, setLoading] = useState(false);
  const [totalPrice, setTotalPrice] = useState(0);
  const [success, setSuccess] = useState(false);

  const paymentSchema = yup.object().shape({
    amount: yup
      .number()
      .min(1, 'กรุณากรอกจำนวนเงินที่โอนขั้นต่ำ 1 บาท')
      .positive('ไม่สามารถกรอกจำนวนเงินที่โอนติดลบได้')
      .required('กรุณากรอก จำนวนเงินที่โอน'),
    image: yup.string().required('กรุณาอัปโหลดหลักฐานการโอนเงิน'),
    // date: yup.string().required('กรุณากรอก วันที่'),
    // time: yup.string().required('กรุณากรอก เวลา'),
  });

  const handleFileUpload = () => {
    // Trigger the click event of the file input element
    if (fileInputRef.current) {
      fileInputRef?.current?.click();
    }
  };

  const handleFileChange = (event: any, formik: any) => {
    const file = event.target.files[0];

    if (file) {
      const imageUrl = URL.createObjectURL(file);
      const fileType = file.type;
      const maxSize = 800000; // 5MB
      if (fileType !== 'image/png' && fileType !== 'image/jpeg') {
        toast.error('Please upload an image of type PNG or JPEG.');
        return;
      }
      if (file.size > maxSize) {
        toast.error('File size exceeds 800KB. Please upload a smaller file.');
        return;
      }
      formik.setFieldValue('image', file);
      setSelectedImage(imageUrl);
    }
  };

  function handleDownload(imageUrl: string) {
    // Create a virtual anchor element
    const anchor = document.createElement('a');
    anchor.href = imageUrl;
    anchor.download = 'promptpay.png'; // Set the file name for download
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
  }

  const handleSubmitPayment = async (values: any) => {
    if (typeof id === 'string') {
      setLoading(true);

      const payload = {
        orderId: parseInt(id),
        amount: values.amount,
        dateTime: dayjs(values.date).toISOString(),
        description: values.description,
        paymentType: selectedPayment === 1 ? 'Prompt Pay' : 'โอนบัญชีธนาคาร',
        bankName: selectedPayment === 1 ? 'ธนาคารกสิกรไทย' : 'ธนาคารกสิกรไทย',
        bankAccountName:
          selectedPayment === 1
            ? 'บริษัท ลูก้าบล็อก จำกัด'
            : 'บริษัท ลูก้าบล็อก จำกัด',
        bankAccountNumber:
          selectedPayment === 1 ? '088-1-88988-1 ' : '265-4-156358',
      };
      const formData = new FormData();
      formData.append('paymentTransactionRequest', JSON.stringify(payload));
      formData.append('file', values.image);
      const resPayment = await apiOrder.createOrderPayment(formData);
      if (resPayment.status) {
        setSuccess(true);
        setLoading(false);
      }
    } else {
      console.error('No ID parameter found in URL');
    }
  };

  useEffect(() => {
    if (id) {
      apiOrder.getOrderById(id).then((res: any) => {
        if (res.status) {
          setPaymentData(res.data);
          const total = res.data.orderItems?.reduce(function (
            sum: number,
            obj: any,
          ) {
            return sum + obj.item.totalPrice;
          }, 0);
          setTotalPrice(total);
        }
      });
    }
  }, []);

  return (
    <PaymentStyle>
      {success ? (
        <div className="success-container">
          <div className="success-wrapper px-5">
            <Image
              priority
              className="flare absolute"
              src="/images/flare.svg"
              alt=""
              width={400}
              height={200}
            />
            <div className="bg-[#EBFEEB] m-4 p-4 rounded-full relative">
              <Image
                priority
                className=" w-[40px] h-[40px]"
                src="/icons/check.svg"
                alt=""
                width={40}
                height={40}
              />
            </div>
            <span className="subtitle">การแจ้งชำระเงิน สำเร็จ!</span>
            <span className="content">
              เราได้รับการแจ้งชำระเงินของคุณแล้ว ขณะนี้กำลัง <br />{' '}
              ตรวจสอบการชำระเงิน
            </span>
            <span className="content">
              คุณสามารถเช็คสถานะของรายการสั่งซื้อของคุณได้ที่ <br />
              “ตรวจสอบการชำระเงิน”
            </span>
            <Button
              className="button w-100"
              onClick={() => {
                setSuccess(false);
                router.push('/my-orders?status=3');
              }}
            >
              ตรวจสอบการชำระเงิน
            </Button>
          </div>
        </div>
      ) : (
        paymentData && (
          <div>
            <div className="header-container">
              <span className="subheader">ชำระเงิน</span>
              <div className={'btn-close'}>
                <Image
                  priority
                  className="cursor-pointer"
                  onClick={() => router.back()}
                  src="/icons/close.png"
                  alt=""
                  width={14}
                  height={14}
                />
              </div>
            </div>
            <div className="order-info-wrapper">
              <span className="subheader-bold">{paymentData.orderNumber}</span>
              <span className="caption mt-2 mb-10">
                {`วันที่สั่งซื้อ: ${dayjs(paymentData.createdDate).format('D MMM YYYY, H:mm')}`}
              </span>
              <div className="order-price-wrapper">
                <div className="flex flex-col text-left">
                  <span className="content">ราคาสินค้าทั้งหมด</span>
                  <span className="content my-6">ภาษีมูลค่าเพิ่ม 7%</span>
                  <span className="content">ค่าจัดส่ง</span>
                </div>
                <div className="flex flex-col text-right">
                  <span className="content-bold">
                    {numberWithCommas(totalPrice, 2)}
                  </span>
                  <span className="content-bold my-6">
                    {numberWithCommas(paymentData.vat, 2)}
                  </span>
                  <span className="content-bold">
                    {numberWithCommas(paymentData.shippingCost, 2)}
                  </span>
                </div>
              </div>
              <div className="total-price-wrapper">
                <span className="content-bold">ยอดชำระเงินทั้งหมด</span>
                <span className="subtitle">
                  {numberWithCommas(
                    totalPrice + paymentData.vat + paymentData.shippingCost,
                  )}
                </span>
              </div>
              <div className="payment-wrapper ">
                <span className="subtitle-bold">ช่องทางการชำระเงิน</span>
                <div className="flex flex-row my-10">
                  <Button
                    className={`button-payment ${selectedPayment === 1 ? 'selected' : ''} mr-2`}
                    onClick={() => setSelectedPayment(1)}
                  >
                    Prompt Pay
                  </Button>
                  <Button
                    className={`button-payment ${selectedPayment === 2 ? 'selected' : ''} ml-2`}
                    onClick={() => setSelectedPayment(2)}
                  >
                    โอนบัญชีธนาคาร
                  </Button>
                </div>
                <div>
                  {selectedPayment === 1 ? (
                    <div className="promptpay-wrapper">
                      <div className="promptpay-head-wrapper">
                        <Image
                          priority
                          className="mr-6 cursor-pointer"
                          src="/icons/promptpay.png"
                          alt=""
                          width={78}
                          height={24}
                        />
                      </div>
                      <div className="promptpay-content-wrapper">
                        {/* <a href="/icons/promptpay.png" download="promptpay.png"> */}
                        <Image
                          priority
                          src="/images/promptpay.png"
                          alt=""
                          width={200}
                          height={200}
                        />
                        <div className="promptpay-text-wrapper">
                          <span className="subtitle-bold">
                            สแกนเพื่อโอนเงินเข้าบัญชี
                          </span>
                          <span className="caption">
                            รับเงินได้จากทุกธนาคาร
                          </span>
                        </div>
                        <Button
                          className="bg-[#F5F5F5] p-2 flex justify-center rounded-full hover:bg-[#F5F5F5] absolute top-6 right-6"
                          onClick={() =>
                            handleDownload('/images/promptpay.png')
                          }
                        >
                          <Image
                            priority
                            src="/icons/download.svg"
                            alt=""
                            width={24}
                            height={24}
                          />
                        </Button>
                      </div>
                      <div className="promptpay-footer-wrapper">
                        <div className="h-fit">
                          <span className="content">ชื่อ</span>
                          <span className="content-bold ml-2">
                            บริษัท ลูก้าบล็อก จำกัด
                          </span>
                        </div>
                        <div className={'box-row'}>
                          <div className="box-acc-number h-fit border-l-[1px] border-[#DBE2E5] pl-2 ml-2">
                            <span className="content">เลขบัญชี</span>
                            <span className="content-bold ml-2">
                              088-1-88988-1
                            </span>
                          </div>

                          <Button
                            className="rounded-full ml-4"
                            onClick={() =>
                              handleCopyToClipboard('088-1-88988-1')
                            }
                          >
                            คัดลอก
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bank-wrapper">
                      <div>
                        <Image
                          className="image-wrappers"
                          src="/images/kbank-logo.png"
                          alt={''}
                          width={100}
                          height={100}
                        />
                      </div>
                      <div className="bank-content-wrapper ml-4">
                        <span className="subtitle-bold">ธนาคารกสิกรไทย</span>
                        <div className="flex flex-row justify-center items-center">
                          <span className="subtitle">เลขที่บัญชี:</span>
                          <span className="subtitle-bold ml-2">
                            265-4-156358
                          </span>
                          <Button
                            className="rounded-full bg-[#E5E5FC] text-secondary-main font-bold ml-4 text-[12px] h-fit hover:bg-[#E5E5FC]"
                            onClick={() =>
                              handleCopyToClipboard('265-4-156358')
                            }
                          >
                            คัดลอก
                          </Button>
                        </div>
                        <div>
                          <span className="subtitle">ชื่อบัญชี: </span>
                          <span className="subtitle-bold ml-2">
                            บริษัท ลูก้าบล็อก จำกัด
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="formdata-wrapper">
                <Formik
                  initialValues={{
                    amount: null,
                    date: new Date(),
                    description: '',
                    image: '',
                    paymentType: '',
                    bankName: '',
                    bankAccountName: '',
                    bankAccountNumber: '',
                  }}
                  validationSchema={paymentSchema}
                  onSubmit={(data: any) => {
                    handleSubmitPayment(data);
                  }}
                >
                  {(formik) => (
                    <Form className="form-user-custom-container">
                      <div className="upload-wrapper">
                        <div className="flex flex-col">
                          <div className="subtitle-bold">แจ้งชำระเงิน</div>
                          <Field name="image">
                            {({ field }: any) => (
                              <div className="upload-content">
                                <Input
                                  name={field.name}
                                  className="hidden"
                                  ref={fileInputRef}
                                  onChange={(values: any) =>
                                    handleFileChange(values, formik)
                                  }
                                  type={'file'}
                                />
                                {selectedImage ? (
                                  <div className="w-full h-full max-h-[600px] relative">
                                    <Image
                                      className="image-wrapper w-full h-full object-contain"
                                      src={selectedImage}
                                      alt="Selected"
                                      width={100}
                                      height={100}
                                      unoptimized
                                    />
                                  </div>
                                ) : (
                                  <div className="upload-placeholder">
                                    JPG หรือ PNG ขนาดไม่เกิน 800K
                                  </div>
                                )}
                                <Button
                                  type={'button'}
                                  className="upload-button"
                                  onClick={handleFileUpload}
                                >
                                  <Image
                                    priority
                                    src="/icons/add_photo_alternate.svg"
                                    alt=""
                                    width={24}
                                    height={24}
                                  />
                                  อัปโหลดหลักฐานการโอนเงิน
                                  <div />
                                </Button>
                              </div>
                            )}
                          </Field>
                          <ErrorMessage name="image">
                            {(msg) => <div className="text-red-500">{msg}</div>}
                          </ErrorMessage>
                        </div>
                      </div>
                      {/* <h3 className="text-head px-6 pt-6">ผู้รับ</h3> */}
                      <div className="flex-row text-start">
                        <label className="text-bold text-m font-bold">
                          จำนวนเงินที่โอน
                        </label>
                        <Field name="amount">
                          {({ field }: any) => (
                            <div>
                              <div className="flex items-center mt-[14px] border-[1px] border-neutral-200 pr-2 rounded-[6px]">
                                <Input
                                  disabled={loading}
                                  name={field.name}
                                  type={'text'}
                                  className="focus-visible:ring-0 focus-visible:ring-offset-0 outline-0 pl-2 border-0"
                                  placeholder={'0.00'}
                                  onChange={(e: any) => {
                                    let { value } = e.target;
                                    // Remove any non-numeric characters except '.'
                                    value = value.replace(/[^0-9.]/g, '');
                                    // Split the value on the decimal point
                                    const parts = value.split('.');
                                    // If there's more than one decimal point, keep only the first part and the first two decimal places
                                    if (parts.length > 1) {
                                      value = `${parts[0]}.${parts[1].slice(0, 2)}`;
                                    }
                                    // Replace leading zeros with empty string unless it's a zero before a decimal point
                                    if (
                                      value.startsWith('0') &&
                                      !value.startsWith('0.')
                                    ) {
                                      value = value.replace(/^0+/, '');
                                    }

                                    formik.setFieldValue(field.name, value);
                                  }}
                                  onBlur={() => {
                                    if (formik.values.amount === 0) {
                                      formik.setFieldValue(field.name, '');
                                    }
                                  }}
                                  value={field.value}
                                />
                                <span className={''}>บาท</span>
                              </div>
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                      <div className="flex flex-col text-start my-6">
                        <label className="text-bold text-m font-bold mb-4">
                          วันที่ และ เวลา
                        </label>
                        <Field name="date">
                          {({ field }: any) => (
                            <div>
                              {/* eslint-disable-next-line react/jsx-no-undef */}
                              <DateTimePicker
                                className="w-full"
                                onChange={(data: any) => {
                                  formik.setFieldValue(field.name, data);
                                }}
                                calendarIcon={<CustomCalendarIcon />}
                                format={'y-MM-dd h:mm a'}
                                value={field.value}
                                clearIcon={false}
                                disableClock={true}
                              />
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                      <div className="flex-row text-start">
                        <label className="text-bold text-m font-bold">
                          หมายเหตุ
                        </label>
                        <Field name="description">
                          {({ field }: any) => (
                            <div>
                              <Textarea
                                name={field.name}
                                value={field.value}
                                placeholder="ระบุหมายเหตุ"
                                className="mt-[14px] resize-none border-1-[#EEEEEE] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                                onChange={field.onChange}
                              />
                              <ErrorMessage name={field.name}>
                                {(msg) => (
                                  <div className="text-red-500">{msg}</div>
                                )}
                              </ErrorMessage>
                            </div>
                          )}
                        </Field>
                      </div>
                      <div className="flex py-6">
                        <Button
                          disabled={loading}
                          type="submit"
                          className="confirm-button"
                        >
                          ยืนยันชำระเงิน
                        </Button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        )
      )}
    </PaymentStyle>
  );
};

export default PaymentPage;
