import React, { useEffect, useRef, useState } from 'react';
import CouponStyle from '@/styles/CouponStyle';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Grid from '@mui/material/Grid2';
import Box from '@mui/material/Box';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import Image from 'next/image';
import apiDiscount from '@/services/discount';
import CouponCard from '@/components/customUI/CouponCard/CouponCard';
import Footer from '@/components/customUI/Footer/Footer';
import LoadingButton from '@mui/lab/LoadingButton';
import { discountAll, discountCategory } from '@/utils/discount';
import { isEmpty, isUndefined } from 'lodash';

const Index = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [valueTab, setValueTab] = useState<any>(0);
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [valueSeeMore, setValueSeeMore] = useState<boolean>(false);
  const [dataDiscount, setDataDiscount] = useState<any>([]);
  const [dataMyDiscount, setDataMyDiscount] = useState<any>({});

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValueSeeMore(false);
    setValueTab(newValue);
    if (ref.current) {
      ref.current.lastElementChild?.scrollIntoView({ behavior: 'smooth' });
    }
  };
  const getDiscountPage = async () => {
    setLoading(true);
    const res = await apiDiscount.getDiscountPage({
      page: 0,
      size: 100,
      ascending: true,
    });
    if (res.status) {
      setDataDiscount(res.data);
      const allData = discountAll(res.data?.content);
      setData(allData);
    }
    setLoading(false);
  };

  const getUserDiscountList = async () => {
    const res = await apiDiscount.getUserDiscountList({
      page: 0,
      size: 100,
      ascending: true,
    });
    if (res.status) {
      setDataMyDiscount(res.data);
    }
  };
  const myDiscount = dataMyDiscount?.content?.map((item: any) => {
    return item.discount.id;
  });
  useEffect(() => {
    getUserDiscountList();
  }, []);

  useEffect(() => {
    getDiscountPage();
  }, []);
  const discountCategoryList = discountCategory(dataDiscount?.content);
  const allData = discountAll(dataDiscount?.content);
  useEffect(() => {
    if (valueTab !== 0) {
      setData(discountCategoryList?.[valueTab]);
    } else {
      setData(allData);
    }
  }, [valueTab]);
  return (
    <CouponStyle>
      <div className="container mx-auto">
        <div className="zone-coupon-page">
          <div className={'box-banner-coupon'}>
            <Image
              priority
              src={`/images/banner-coupon.svg`}
              alt={'image banner'}
              width={500}
              height={24}
            />
          </div>
          <div className="box-detail-coupon">
            <h2>คูปองส่วนลดสินค้าและบริการ</h2>
            <p>
              ลดแรงแซงทุกดีลกับคูปองส่วนลดสุดคุ้ม ที่ให้คุณสั่งซื้อ
              ได้มากขึ้นแต่จ่ายน้อยลงในราคาสบายกระเป๋า
            </p>
          </div>
          <div className="tab-menu">
            <Tabs value={valueTab} onChange={handleChange} centered>
              <Tab
                icon={<div className={'amount'}>{allData?.length}</div>}
                iconPosition="end"
                label="ทั้งหมด"
              />
              {!isUndefined(discountCategoryList) &&
                Object.keys(discountCategoryList).map(
                  (item: any, index: number) => {
                    const data = discountCategoryList[item];
                    return (
                      <Tab
                        value={item}
                        icon={<div className={'amount'}>{data.length}</div>}
                        iconPosition="end"
                        label={item}
                        key={index}
                      />
                    );
                  },
                )}
            </Tabs>
          </div>
          <Box sx={{ flexGrow: 1 }} ref={ref}>
            <Grid container spacing={2} className={'container-banner-coupon'}>
              {!isEmpty(data) ? (
                data?.map((item: any, index: number) => {
                  const collected = myDiscount?.includes(item.id);
                  if (item.isFull) {
                    return null;
                  }
                  if (!valueSeeMore) {
                    if (index > 11) {
                      return null;
                    }
                  }
                  return (
                    <Grid
                      size={{ md: 4, sm: 6, xs: 12 }}
                      className={'child-card-coupon'}
                      key={index}
                    >
                      <CouponCard
                        discountId={item.id}
                        name={item.title}
                        description={item.description}
                        remainingRights={item.maxUsage}
                        discountCategory={item.discountCategory}
                        isShowModal={true}
                        discountCode={item.discountCode}
                        startDate={item.startDate}
                        endDate={item.endDate}
                        isCollected={collected}
                        fetchDataDiscount={async () => {
                          setValueSeeMore(false);
                          await getDiscountPage();
                          await getUserDiscountList();
                        }}
                      />
                    </Grid>
                  );
                })
              ) : (
                <div className={'not-found-coupon'}>
                  <p>ไม่พบคูปอง</p>
                </div>
              )}
            </Grid>
          </Box>
          {allData?.length >= 12 && (
            <div className={'box-more'}>
              <LoadingButton
                id={'seeMore'}
                variant={'text'}
                endIcon={
                  valueSeeMore ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />
                }
                onClick={() => setValueSeeMore(!valueSeeMore)}
                loading={loading}
              >
                {valueSeeMore ? 'ดูน้อยลง' : 'ดูเพิ่มเติม'}
              </LoadingButton>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </CouponStyle>
  );
};

export default Index;
