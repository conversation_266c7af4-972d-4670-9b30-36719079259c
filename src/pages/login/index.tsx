import React, { Fragment, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { getCookie, setCookie } from 'cookies-next';
import { useAppDispatch } from '@/store/index';
import { useRouter } from 'next/router';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as yup from 'yup';
import apiUser from '@/services/user';
import { getUserProfile } from '@/store/reducers/backup/userSlice';
import { getAuthUrl } from '@/utils/lineAuth';
import styled from 'styled-components';
import { toast } from 'sonner';
import Image from 'next/image';
import { ReloadIcon } from '@radix-ui/react-icons';
import { isEmpty, isUndefined } from 'lodash';

const LoginStyle = styled.div`
  display: flex;
  flex-direction: column;
  text-align: center;
  position: relative;
  width: 500px;
  height: fit-content;
  padding-top: 80px;
  @media only screen and (max-width: 430px) {
    padding: 0 16px;
  }
  .line {
    position: relative;
    height: 52px;
    padding: 10px 8px;
    border-radius: 8px;
    color: white;
  }
`;

const LoginPage = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const loginSchema = yup.object().shape({
    email: yup
      .string()
      .email('กรุณากรอกอีเมลให้ถูกต้อง')
      .required('กรุณากรอกอีเมล'),
    password: yup.string().required('กรุณากรอกรหัสผ่าน'),
  });

  useEffect(() => {
    if (getCookie('remember')) {
      setRememberMe(true);
    }
  }, []);

  const handleLogin = async (values: any) => {
    const payload = { username: values.email, password: values.password };
    setLoading(true);
    const res = await apiUser.login(payload);
    if (rememberMe) {
      setCookie('remember', values.email);
    }

    if (res && !res.isError) {
      setCookie('access_token', res.data);
      await dispatch(getUserProfile());
      await redirectToCustomize();
    } else {
      toast.error(res.error);
    }

    setLoading(false);
  };

  const redirectToCustomize = async () => {
    const token = getCookie('customize');
    if (!isUndefined(token) && !isEmpty(token)) {
      return router.push(`/customize?slug=${token}`);
    }
    return router.push('/welcome');
  };

  return (
    <LoginStyle>
      <div className="flex flex-col justify-center items-center mt-[60px] w-full mb-[72px]">
        <Image
          priority
          className="m-auto mb-4"
          src="/icons/digibox-icon-gray.png"
          alt=""
          width={56}
          height={64}
        />
        <Image
          priority
          className="h-[24px] w-[150px] mb-4"
          src="/icons/DIGIBOXS.png"
          alt=""
          height={24}
          width={150}
        />
        <label className="text-white text-m">เข้าสู่ระบบ</label>
      </div>
      <Link
        href={getAuthUrl('line')}
        className="line bg-[#06c755] hover:!bg-[#07d55b] content-center"
      >
        <Image
          priority
          className="absolute top-[20%] left-[2%]"
          src="/icons/LINE_logo%201.png"
          alt=""
          height={32}
          width={32}
        />
        <span className="font-bold text-lg text-white">Sign in with LINE</span>
      </Link>
      <div className="flex w-full items-center my-8">
        <label className="font-normal text-m text-white w-full">หรือ</label>
      </div>
      <Formik
        initialValues={{
          email: getCookie('remember') ? getCookie('remember') : '',
          password: '',
        }}
        validationSchema={loginSchema}
        onSubmit={(values: any) => handleLogin(values)}
      >
        <Form>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">อีเมล</label>
            <Field name="email">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    name={field.name}
                    type={'email'}
                    className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุอีเมล'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name="email">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">รหัสผ่าน</label>
            <Field name="password">
              {({ field }: any) => (
                <div className="relative">
                  <Input
                    disabled={loading}
                    name={field.name}
                    className="text-white bg-[#42424233] mt-3.5 border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุรหัสผ่าน'}
                    type={showPassword ? 'texts' : 'password'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  {!showPassword ? (
                    <Image
                      priority
                      className={`absolute top-[4px] right-[4px] cursor-pointer`}
                      src="/icons/hide-password.svg"
                      alt=""
                      height={32}
                      width={32}
                      onClick={() => setShowPassword(true)}
                    />
                  ) : (
                    <Image
                      priority
                      className={`absolute top-[4px] right-[4px] cursor-pointer`}
                      src="/icons/show-password.svg"
                      alt=""
                      height={32}
                      width={32}
                      onClick={() => setShowPassword(false)}
                    />
                  )}

                  <ErrorMessage name="password">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex justify-between mb-8">
            <div className="flex items-center">
              <Checkbox
                onCheckedChange={(checked: any) => {
                  setRememberMe(checked);
                }}
                checked={rememberMe}
                className="border-[#685B7E] w-6 h-6 mr-3.5 rounded data-[state=checked]:bg-secondary-main"
              />
              <label
                className="text-m text-white"
                style={{ lineHeight: 'normal' }}
              >
                ให้ฉันอยู่ในระบบต่อไป
              </label>
            </div>
            <div className="flex">
              <Link
                href="/forgot-password"
                className="text-m cursor-pointer text-white"
              >
                ลืมรหัสผ่าน?
              </Link>
            </div>
          </div>
          <div className="relative">
            <Button
              disabled={loading}
              type="submit"
              className="font-bold text-[20px] w-full h-[50px] mb-[120px] bg-white text-black hover:bg-white hover:text-secondary-main"
            >
              {loading ? (
                <Fragment>
                  <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                  กำลังดำเนินการ
                </Fragment>
              ) : (
                'เข้าสู่ระบบ'
              )}
            </Button>
            <div className="flex justify-center absolute m-auto left-0 right-0 top-[70%]">
              <label className="text-sm text-white mr-2">
                ยังไม่มีบัญชีกับเรา?
              </label>
              <Link
                className="text-sm font-bold text-white cursor-pointer"
                href="/register"
              >
                สร้างบัญชี
              </Link>
            </div>
          </div>
        </Form>
      </Formik>
    </LoginStyle>
  );
};

export default LoginPage;
