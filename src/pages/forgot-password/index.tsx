import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as yup from 'yup';
import styled from 'styled-components';
import Image from 'next/image';
import apiUser from '@/services/user';
import { toast } from 'sonner';
import { ReloadIcon } from '@radix-ui/react-icons';

const ForgotPasswordStyle = styled.div`
  display: flex;
  flex-direction: column;
  text-align: center;
  position: relative;
  width: 500px;
  height: 100vh;
  padding-top: 80px;
  .bottom-section {
    display: block;
    height: fit-content;
    align-self: center;
    position: absolute;
    bottom: 80px;
  }
`;

const LoginPage = () => {
  const [loading, setLoading] = useState(false);
  const loginSchema = yup.object().shape({
    email: yup
      .string()
      .email('กรุณากรอกอีเมลให้ถูกต้อง')
      .required('กรุณากรอกอีเมล'),
  });

  const handleForgotPassword = async (values: any) => {
    setLoading(true);

    const res = await apiUser.sendEmailForgotPassword(values);
    if (res && !res.isError) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }

    setLoading(false);
  };

  return (
    <ForgotPasswordStyle>
      <div className="flex flex-col justify-center items-center mt-[60px] w-full mb-[72px]">
        <Image
          priority
          className="m-auto mb-4"
          src="/icons/digibox-icon-gray.png"
          alt=""
          width={56}
          height={64}
        />
        <Image
          priority
          className="h-[24px] w-[150px] mb-4"
          src="/icons/DIGIBOXS.png"
          alt=""
          height={24}
          width={150}
        />
        <label className="text-white text-m">ลืมรหัสผ่าน</label>
      </div>
      <Formik
        initialValues={{
          email: '',
        }}
        validationSchema={loginSchema}
        onSubmit={(values: any) => handleForgotPassword(values)}
      >
        <Form>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">อีเมล</label>
            <Field name="email">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    name={field.name}
                    type={'email'}
                    className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุอีเมล'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name="email">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="relative">
            <Button
              disabled={loading}
              type="submit"
              className="font-bold text-[20px] w-full h-[50px] mb-[120px] bg-white text-black hover:bg-white hover:text-secondary-main"
            >
              {loading ? (
                <>
                  <ReloadIcon className="h-2 w-2 animate-spin" />
                  กำลังดำเนินการ
                </>
              ) : (
                'รีเซ็ตรหัสผ่าน'
              )}
            </Button>
          </div>
        </Form>
      </Formik>
      <div className="bottom-section">
        <label className="text-sm text-white mr-2">มีบัญชีผู้ใช้งานแล้ว</label>
        <Link
          className="text-sm font-bold text-white cursor-pointer"
          href="/login"
        >
          เข้าสู่ระบบ
        </Link>
      </div>
    </ForgotPasswordStyle>
  );
};

export default LoginPage;
