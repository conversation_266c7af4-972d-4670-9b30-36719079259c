import React from 'react';
import { GetServerSideProps } from 'next';
import { isUndefined } from 'lodash';
import { setCookie } from 'cookies-next';

const ResetPasswordPagePage = () => {
  return <>reset-password</>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const { token } = query;
  if (!isUndefined(token)) {
    try {
      setCookie('access_token', token, { req, res, maxAge: 60 * 60 * 24 });
      return {
        redirect: {
          destination: '/new-password',
          permanent: false,
        },
      };
    } catch (error) {
      console.error(error);
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false,
    },
  };
};

export default ResetPasswordPagePage;
