import React, { Fragment, useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import { Button } from '@/components/ui/button';

import { toast } from 'sonner';
import { useRouter } from 'next/router';
import { useAppDispatch } from '@/store/index';
import { cartSelector, getCart } from '@/store/reducers/backup/cartSlice';
import apiCart from '@/services/cart';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CartItemType } from '@/types/cart';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { useSelector } from 'react-redux';
import CardCartCustom from '@/components/customUI/CardCartCustom';
import { Skeleton } from '@/components/ui/skeleton';

const CartStyle = styled.div`
  width: 100%;
  padding-top: 80px;
  height: max-content;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
    position: relative;
  }
  .head-cart {
    display: flex;
    padding: 24px;
    @media only screen and (max-width: 430px) {
      padding: 24px 0;
    }
    .subtitle {
      font-size: 20px;
      font-weight: 700;
    }
  }
  .card-container {
    width: 70%;
    margin: 54px auto 0;
    @media only screen and (max-width: 430px) {
      width: 100%;
      margin-top: 0;
      padding: 0 5%;
      height: calc(100vh - 280px);
      overflow-y: scroll;
    }
    .card-box {
      display: flex;
      margin: 24px 0;
      position: relative;
      flex-direction: column;
      border-bottom: 0;
      @media only screen and (max-width: 430px) {
        margin-top: 0;
      }
      .header-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #dbe2e5;
        .action-wrapper {
          display: flex;
          .delete {
            margin-left: 8px;
            cursor: pointer;
            width: 24px;
            height: 24px;
          }
          .edit {
            width: 24px;
            height: 24px;
            cursor: pointer;
          }
        }
        .text-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
        }
      }
      .content-wrapper {
        display: flex;
        border-bottom: 1px solid #dbe2e5;
        padding: 16px;
        .content-bold {
          font-size: 14px;
          font-weight: 700;
        }
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .text-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding-left: 8px;
          @media only screen and (max-width: 430px) {
            padding-left: 8px;
          }
          .subtitle {
            font-size: 14px;
            font-weight: 400;
          }
        }
        .price-container {
          .price {
            font-size: 14px;
            font-weight: 700;
          }
          .unit {
            margin-left: 8px;

            font-size: 14px;
            font-weight: 700;
          }
        }
      }
      .footer-wrapper {
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .accordion {
          border-bottom: 1px solid #dbe2e5;
          border-radius: 8px;
        }
        .show-more-container {
          .show-more-wrapper {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #dbe2e5;
            padding: 16px;
            &:last-child {
              border-bottom: 0;
            }
            .special-wrapper {
              display: flex;
              flex-direction: column;
              text-align: right;
              .special-text {
                font-size: 14px;
                font-weight: 700;
              }
            }
            .head-text {
              font-size: 14px;
              font-weight: 400;
            }
            .content-text {
              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
    }
  }
  .result-container {
    background-color: #212121f2;
    display: flex;
    justify-content: space-between;
    width: 70%;
    margin: 24px auto;
    padding: 24px;
    border-radius: 16px;
    @media only screen and (max-width: 430px) {
      background-color: #212121;
      width: 100%;
      flex-direction: column;
      position: fixed;
      bottom: 0;
      margin: 0;
      height: 144px;
      border-radius: 0;
    }
    .result-text-wrapper {
      @media only screen and (max-width: 430px) {
        margin-bottom: 16px;
      }
      .result-wrapper {
        span {
          color: white;
          font-size: 20px;
          font-weight: 700;
        }
        .unit {
          margin-left: 8px;
        }
      }
      .content {
        font-size: 14px;
        font-weight: 400;
        color: white;
      }
    }
    .confirm-button {
      width: fit-content;
      height: 48px;
      font-size: 20px;
      font-weight: 700;
      @media only screen and (max-width: 430px) {
        width: 100%;
      }
    }
  }
  @media (max-width: 991px) {
    .result-container,
    .card-container {
      width: 90%;
      margin: auto;
      .text-wrapper {
        width: unset !important;
      }
    }
    .card-container {
      padding: unset !important;
    }
  }
  @media (max-width: 768px) {
    .card-container {
      padding-bottom: 8rem !important;
    }
    .result-container {
      position: fixed;
      bottom: 0;
      border-radius: 0;
      width: 100%;
      .result-text-wrapper {
        .content {
          font-size: 12px !important;
        }
      }
    }
  }
  @media (max-width: 425px) {
    .card-container {
      padding-top: 2rem !important;
      padding-bottom: 3rem !important;
    }
    .card-container {
      .content-wrapper {
        padding: 10px !important;
      }
    }
  }
  @media (min-width: 376px) {
    .card-container {
      .content-wrapper {
        .text-container {
          .text-wrapper {
            .subtitle {
              br {
                display: none;
              }
            }
          }
        }
      }
    }
  }
`;

const CartPage = () => {
  const dispatch = useAppDispatch();
  const [result, setResult] = useState(0);
  const router = useRouter();
  const { cart, pending } = useSelector(cartSelector);
  const [isActiveDeleteModal, setIsActiveDeleteModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<CartItemType | any>(null);

  const calPrice = () => {
    const result = cart.reduce(function (sum: number, obj: CartItemType) {
      return sum + obj.totalPrice;
    }, 0);
    setResult(result);
  };

  const handleDeleteCartItem = async (id: number) => {
    const res = await apiCart.deleteCart(id);
    if (res.status) {
      toast.success(res.message);
      setIsActiveDeleteModal(false);
      dispatch(getCart());
    } else {
      toast.error(res.error);
    }
  };

  useEffect(() => {
    dispatch(getCart());
  }, []);

  useEffect(() => {
    if (!isEmpty(cart) && !isUndefined(cart)) {
      calPrice();
    }
  }, [cart]);

  return (
    <CartStyle>
      <div className="head-cart">
        <Image
          priority
          className="ml-4 flex mr-2"
          src="/icons/shopping_cart-bk.png"
          alt=""
          width={24}
          height={24}
        />
        <span className="subtitle">รถเข็นของคุณ</span>
      </div>
      {!isUndefined(selectedItem) && !isNull(selectedItem) && (
        <Dialog
          open={isActiveDeleteModal}
          onOpenChange={(value: any) => setIsActiveDeleteModal(value)}
        >
          <DialogContent className="modal-cancel-order sm:max-w-[425px]">
            <DialogHeader className="border-b-[1px] flex">
              <DialogTitle className="text-center p-4">
                ยืนยันลบรายการโมเดล
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
              <div className="bg-[#FFECEC] rounded-full w-fit p-[30px]">
                <Image
                  className="rounded-xl"
                  priority
                  src="/icons/delete.svg"
                  alt=""
                  width={40}
                  height={40}
                />
              </div>
              <div className="text-wrapper mt-[32px] flex flex-col">
                <span className="subtitle-bold"></span>
                <span className="content">คุณยืนยันลบรายการโมเดล</span>
                <span className="content-bold">
                  {selectedItem.model.name}
                  <span className="content">ใช่หรือไม่</span>
                </span>
              </div>
            </div>
            <DialogFooter className="flex flex-row p-6">
              <DialogClose asChild>
                <Button
                  type={'button'}
                  className="w-1/2 bg-white text-black border-[1px] border-black me-2 hover:bg-white hover:text-black"
                  onClick={() => setIsActiveDeleteModal(false)}
                >
                  ไม่, ยังไม่ใช่ตอนนี้
                </Button>
              </DialogClose>
              <Button
                type={'submit'}
                onClick={() =>
                  handleDeleteCartItem(selectedItem && selectedItem.id)
                }
                className="w-1/2 ml-2"
              >
                ยืนยันลบ
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      {pending ? (
        <div className="card-container w-full h-[200px] animate-pulse">
          <Skeleton className="w-full h-full" />
          <Skeleton className="mt-6 w-full h-[80px]" />
        </div>
      ) : (
        <Fragment>
          <div className="card-container">
            <CardCartCustom
              cartData={cart}
              setIsActiveDeleteModal={setIsActiveDeleteModal}
              setSelectedItem={setSelectedItem}
            />
          </div>
          {!isEmpty(cart) && !isUndefined(cart) && (
            <div className="result-container">
              <div className="result-text-wrapper">
                <div className="result-wrapper">
                  <span className="subtitle">{numberWithCommas(result)}</span>
                  <span className="unit">บาท</span>
                </div>
                <span className="content">
                  (ยังไม่รวมค่าจัดส่ง และภาษีมูลค่าเพิ่ม 7% )
                </span>
              </div>
              <Button
                disabled={isEmpty(cart)}
                className="confirm-button bg-primary-main hover:bg-primary-light"
                onClick={() => router.push('/orders')}
              >
                ยืนยันการสั่งซื้อ
              </Button>
            </div>
          )}
        </Fragment>
      )}
    </CartStyle>
  );
};

export default CartPage;
