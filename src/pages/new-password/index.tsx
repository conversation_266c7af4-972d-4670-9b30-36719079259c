import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { deleteCookie, getCookie } from 'cookies-next';
import { useRouter } from 'next/router';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as yup from 'yup';
import apiUser from '@/services/user';
import styled from 'styled-components';
import { toast } from 'sonner';
import Image from 'next/image';

const NewPasswordStyle = styled.div`
  display: flex;
  flex-direction: column;
  text-align: center;
  position: relative;
  width: 500px;
  height: fit-content;
  padding-top: 80px;
  .bottom-section {
    display: block;
    height: fit-content;
    align-self: center;
    position: absolute;
    bottom: 80px;
  }
`;

const ResetPasswordForm = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const loginSchema = yup.object().shape({
    password: yup.string().required('กรุณากรอกรหัสผ่าน'),
    rePassword: yup
      .string()
      .required('กรุณากรอกรหัสผ่าน')
      .oneOf([yup.ref('password')], 'รหัสผ่านไม่ตรงกัน'),
  });
  const token = getCookie('access_token');

  const handleResetPassword = async (values: any) => {
    setLoading(true);
    const payload = { token: token, newPassword: values.password };
    const res = await apiUser.resetPassword(payload);
    if (res && !res.isError) {
      toast.success(res.message);
      setTimeout(() => {
        deleteCookie('access_token');
        router.push(`/login`);
      }, 2000);
    } else {
      toast.error(res.error);
    }
    setLoading(false);
  };

  return (
    <NewPasswordStyle>
      <div className="flex flex-col justify-center items-center mt-[60px] w-full mb-[72px]">
        <Image
          priority
          className="m-auto mb-4"
          src="/icons/digibox-icon-gray.png"
          alt=""
          width={56}
          height={64}
        />
        <Image
          priority
          className="h-[24px] w-[150px] mb-4"
          src="/icons/DIGIBOXS.png"
          alt=""
          height={24}
          width={150}
        />
        <label className="text-white text-m">เปลี่ยนรหัสบัญชีใหม่</label>
      </div>
      <Formik
        initialValues={{
          password: '',
          rePassword: '',
        }}
        validationSchema={loginSchema}
        onSubmit={(values: any) => handleResetPassword(values)}
      >
        <Form>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">รหัสใหม่</label>
            <Field name="password">
              {({ field }: any) => (
                <div>
                  <Input
                    name={field.name}
                    type={'password'}
                    className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุรหัสใหม่'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name="password">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">
              ยืนยันรหัสใหม่
            </label>
            <Field name="rePassword">
              {({ field }: any) => (
                <div>
                  <Input
                    name={field.name}
                    type={'password'}
                    className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุยืนยันรหัสใหม่'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name="rePassword">
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="relative">
            <Button
              disabled={loading}
              type="submit"
              className="font-bold text-[20px] w-full h-[50px] mb-[120px] bg-white text-black hover:bg-white hover:text-secondary-main"
            >
              บันทึก
            </Button>
          </div>
        </Form>
      </Formik>
      <div className="bottom-section">
        <label className="text-sm text-white mr-2">มีบัญชีผู้ใช้งานแล้ว</label>
        <Link
          className="text-sm font-bold text-white cursor-pointer"
          href="/login"
        >
          เข้าสู่ระบบ
        </Link>
      </div>
    </NewPasswordStyle>
  );
};

export default ResetPasswordForm;
