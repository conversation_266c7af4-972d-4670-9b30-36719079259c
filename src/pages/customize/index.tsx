import React, { Fragment } from 'react';

import CustomizeForm from '@/components/newCustomUI/customize/CustomizeForm';

const CustomizePage = () => {
  // const { productLoading } = useSelector(productSelector);

  return (
    <Fragment>
      {/* {!productLoading && ( */}
      {/*  <CustomizeComponent */}
      {/*    submit={submit} */}
      {/*    editMode={mode} */}
      {/*    editData={null} */}
      {/*    custom={custom} */}
      {/*  /> */}
      {/* )} */}
      <CustomizeForm />
    </Fragment>
  );
};

export default CustomizePage;
