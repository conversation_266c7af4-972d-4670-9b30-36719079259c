import React, { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import apiCart from '@/services/cart';
import { toast } from 'sonner';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { useAppDispatch } from '@/store/index';
import { getProduct } from '@/store/reducers/backup/productSlice';
import { GetCartType } from '@/types/cart';
import CustomizeComponent from '@/components/customUI/custom/CustomizeComponent';

const customizeEditPage = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [cartItem, setCartItem] = useState<GetCartType>();
  const { id } = router.query;

  const submit = async (values: any) => {
    const payload = {
      modelId: values.modelId,
      width: values.width,
      length: values.length,
      height: values.height,
      amount: values.amount,
      unitPrice: values.amountPrice,
      materialId: values.materialId,
      printingId: values.printing,
      printingPrice: values.printingPrice,
      coatingId: values.coatingId,
      coatingPrice: values.coatingPrice,
      productDemoId: values.productDemo,
      productDemoPrice: values.productDemoPrice,
      isArtwork: values.isArtwork === 2,
      artworkUrl: values.artworkUrl,
      artworkPrice: values.artworkPrice,
      description: values.description,
      specialTechnic: !isEmpty(values.specialTechnic)
        ? values.specialTechnic.map((data: any) => {
            return {
              id: data.id,
              width: data.width,
              height: data.height,
              price: data.price,
            };
          })
        : [],
    };
    if (id) {
      const res = await apiCart.editCart(Number(id), payload);
      if (!res.isError) {
        toast.success(res.message);
        setTimeout(() => {
          return router.push(`/cart`);
        }, 500);
      } else {
        toast.error(res.error);
      }
    }
  };

  const getCartById = async (id: number) => {
    const res = await apiCart.getCartItem(id);
    if (!res.isError) {
      await dispatch(getProduct());
      return setCartItem(res.data);
    }
  };

  useEffect(() => {
    if (!isUndefined(id) && !isNull(id)) {
      getCartById(Number(id));
    }
  }, [id]);

  return (
    <Fragment>
      {!isUndefined(cartItem) && !isNull(cartItem) && (
        <CustomizeComponent
          submit={submit}
          editMode={'edit'}
          editData={cartItem}
        />
      )}
    </Fragment>
  );
};

export default customizeEditPage;
