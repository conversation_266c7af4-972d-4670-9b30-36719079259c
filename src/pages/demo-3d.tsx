import React from 'react';
import styled from 'styled-components';
import IframePreview from '@/components/iframe-preview/IframePreview';

export default function Demo3dPage() {
  return (
    <div>
      <Container>
        <IframePreview
          modelId={100010}
          modelType={'3d'}
          width={50}
          height={100}
          length={20}
          material={{ inside: 'white', outside: 'kraft' }}
          onInfo={(info: any) => {
            const { widthOfArea, heightOfArea } = info.data;
            console.log(widthOfArea, heightOfArea);
          }}
        />
      </Container>
    </div>
  );
}

const Container = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  @media only screen and (max-width: 430px) {
    width: 100%;
    display: none;
  }
  .name-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 0;
    z-index: 2;
    .product {
      font-size: 18px;
      font-weight: 700;
    }
    .model {
      font-size: 12px;
      font-weight: 500;
    }
  }
  .display-mode {
    position: absolute;
    top: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 2;
  }
`;
