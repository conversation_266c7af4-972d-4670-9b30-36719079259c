import type { AppProps, AppContext, AppInitialProps } from 'next/app';
import App from 'next/app';
import '@/styles/global.css';
import { NextPage } from 'next';
import React, { ReactElement, ReactNode } from 'react';
import Head from 'next/head';

import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

import getConfig from 'next/config';
import Router from 'next/router';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import dayjs from 'dayjs';

import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import localFont from 'next/font/local';
import { createGlobalStyle } from 'styled-components';
import { Provider } from 'react-redux';
import { wrapper } from '@/store/index';
import MainLayout from '@/layouts/MainLayout';
import { Toaster } from '@/components/ui/sonner';

dayjs.extend(buddhistEra);
dayjs.extend(utc);
dayjs.extend(timezone);

const { publicRuntimeConfig } = getConfig();

export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

export const LineSeedSand = localFont({
  variable: '--line-seed-sans-font',
  src: [
    {
      path: '../../public/fonts/lineSeedTH/LINESeedSansTH_W_Th.woff2',
      weight: '100',
      style: 'normal',
    },
    {
      path: '../../public/fonts/lineSeedTH/LINESeedSansTH_W_Rg.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/lineSeedTH/LINESeedSansTH_W_Bd.woff2',
      weight: '700',
      style: 'normal',
    },
  ],
});

const GlobalStyle = createGlobalStyle`*{
    font-family: ${LineSeedSand.style.fontFamily} !important;
}`;
Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', () => NProgress.done());
Router.events.on('routeChangeError', () => NProgress.done());

function MyApp({ Component, pageProps, ...rest }: AppPropsWithLayout) {
  // const getLayout = Component.getLayout ?? ((page) => page);
  const { store } = wrapper.useWrappedStore(rest);

  return (
    <div className="h-full">
      <GlobalStyle />
      <Provider store={store}>
        <Head>
          <title>{publicRuntimeConfig.APP_NAME}</title>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
          />
        </Head>
        <div className="h-full">
          <Toaster richColors />
          <MainLayout>{<Component {...pageProps} />}</MainLayout>
        </div>
      </Provider>
    </div>
  );
}

MyApp.getInitialProps = async (
  context: AppContext,
): Promise<AppInitialProps> => {
  const ctx = await App.getInitialProps(context);
  return { ...ctx };
};

export default MyApp;
