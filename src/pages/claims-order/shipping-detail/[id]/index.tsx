import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import apiClaimsOrder from '@/services/claimsOrder';
import ClaimsOrderCardShippingDetail from '@/components/customUI/claimsOrder/ClaimsOrderCardShippingDetail';

const ShippingDetailStyled = styled.div`
  padding-top: 80px;
  width: 100%;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .header-container {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    padding: 24px;
    .subheader {
      font-size: 20px;
      font-weight: 700;
      color: #212121;
    }
  }
  .content-container {
    .head-content-wrapper {
      width: 66.66%;
      margin: 48px auto;
    }
  }
`;

const ShippingDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  // const [orderDetail, setOrderDetail] = useState<OrderType>();
  const [shippingDetail, setShippingDetail] = useState<any>({});

  const fetch = async (id: string | string[]) => {
    const resShipping = await apiClaimsOrder.getClaimsOrderShipping(id);

    if (resShipping.status) {
      setShippingDetail(resShipping.data);
    }
  };

  useEffect(() => {
    if (id) {
      fetch(id);
    }
  }, []);

  return (
    shippingDetail && (
      <ShippingDetailStyled>
        <div className="header-container">
          <Image
            priority
            className="w-[40px] h-[40px] cursor-pointer"
            src="/icons/back-icon.svg"
            onClick={() => router.push(`/claims-order`)}
            alt=""
            width={40}
            height={40}
          />
          <span className="subheader">
            {shippingDetail.shippingDetail?.shipping === 1
              ? 'การรับสินค้าด้วยตัวเอง'
              : 'การจัดส่งสินค้า'}
          </span>
          <div></div>
        </div>
        <div className="content-container">
          <ClaimsOrderCardShippingDetail shippingData={shippingDetail} />
        </div>
      </ShippingDetailStyled>
    )
  );
};

export default ShippingDetailPage;
