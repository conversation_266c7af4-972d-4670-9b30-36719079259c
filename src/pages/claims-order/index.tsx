import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import ClaimsOrderModal from '@/components/customUI/claimsOrder/ClaimsOrderModal';
import apiClaimsOrder from '@/services/claimsOrder';
import CardClaimsCustom from '@/components/customUI/claimsOrder/CardClaimsCustom';
import { toast } from 'sonner';
import PaginationCustom from '@/components/customUI/PaginationCustom';
import ClaimsOrderAcceptModal from '@/components/customUI/claimsOrder/ClaimsOrderAcceptModal';
import { isEmpty } from 'lodash';
import { OrderClaimTypeState } from '@/store/type/orderClaim';

const ClaimsOrderStyle = styled.div`
  width: 100%;
  padding-top: 80px;
  height: max-content;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .head-cart {
    display: flex;
    padding: 24px;
    border-bottom: 1px solid #dbe2e5;
    justify-content: center;
    align-items: center;
    .subtitle {
      margin-left: 16px;
      font-size: 20px;
      font-weight: 700;
    }
  }
  .card-container {
    width: 70%;
    margin: 32px auto 0;
    @media (max-width: 430px) {
      width: 95%;
      background-color: #fff;
      margin: auto;
    }
    .text-head-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      @media only screen and (max-width: 430px) {
        padding: 16px 0;
        background-color: white;
      }
      .subtitle-bold {
        font-size: 20px;
        font-weight: 700;
      }
      .add-button {
        font-size: 14px;
        font-weight: 400;
      }
    }
    .card-box {
      display: flex;
      margin: 24px 0;
      position: relative;
      flex-direction: column;
      @media only screen and (max-width: 430px) {
        margin: 8px 0;
      }
      .header-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #dbe2e5;
        .action-wrapper {
          display: flex;
          .delete {
            margin-left: 8px;
            cursor: pointer;
            width: 24px;
            height: 24px;
          }
          .edit {
            width: 24px;
            height: 24px;
            cursor: pointer;
          }
        }
        .text-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
        }
      }
      .content-wrapper {
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid #dbe2e5;
        margin: 0 16px;
        padding: 16px 0;
        .content-box {
          display: flex;
          margin: 0 16px;
          padding: 16px 0;
          .text-container {
            display: flex;
            flex-direction: row;
            margin-left: 24px;
            justify-content: space-between;
            width: 100%;
            @media only screen and (max-width: 430px) {
              margin-left: 16px;
              width: calc(100% - 80px);
            }
            .text-wrapper {
              display: flex;
              flex-direction: column;
              @media only screen and (max-width: 430px) {
                //flex-direction: row;
              }
              //flex: 1;
              .subtitle {
                font-size: 14px;
                font-weight: 400;
                @media only screen and (max-width: 430px) {
                  font-size: 10px;
                }
              }
              .subtitle-bold {
                font-size: 20px;
                font-weight: 700;
                display: flex;
                align-items: center;
                @media only screen and (max-width: 430px) {
                  font-size: 16px;
                }
              }
              .content {
                font-size: 14px;
                font-weight: 400;
              }
              .content-bold {
                font-size: 14px;
                font-weight: 700;
              }
              .claims-status {
                margin-left: 8px;
                border-radius: 99px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 700;
                color: #f09700;
                background-color: #fff8c0;
              }
              .time {
                color: #bdbdbd;
                font-size: 14px;
                font-weight: 400;
                flex: 1;
              }
            }
            .price-container {
              .price {
                font-size: 14px;
                font-weight: 700;
              }
              .unit {
                margin-left: 8px;

                font-size: 14px;
                font-weight: 700;
              }
            }
          }
        }

        .time-container {
          .time {
            color: #bdbdbd;
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
      .media-wrapper {
        padding: 16px;
        border-bottom: 1px solid #dbe2e5;
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .content-bold {
          font-size: 14px;
          font-weight: 700;
        }
        .media-list-wrapper {
          //display: flex;
          //flex-direction: row;
          //@media only screen and (max-width: 430px) {
          //  grid-template-columns: repeat(4, minmax(0, 1fr));
          //}
          .media-box {
            cursor: pointer;
            width: 80px;
            height: 80px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            border: 1px solid #dbe2e5;
            margin: 8px 8px;
            @media only screen and (max-width: 430px) {
              //width: 100%;
              //height: 100%;
            }
            &:first-child {
              margin-left: 0;
              @media only screen and (max-width: 430px) {
                margin-left: 8px;
              }
            }
            &:last-child {
              margin-right: 0;
              @media only screen and (max-width: 430px) {
                margin-right: 8px;
              }
            }
          }
        }
      }
      .footer-wrapper {
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .show-more-container {
          .show-more-wrapper {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #dbe2e5;
            padding: 16px;
            @media only screen and (max-width: 430px) {
              flex-direction: column;
            }
            &:last-child {
              border-bottom: 0;
            }
            .special-wrapper {
              display: flex;
              flex-direction: column;
              text-align: right;
              @media only screen and (max-width: 430px) {
                text-align: left;
              }
              .special-text {
                font-size: 14px;
                font-weight: 700;
              }
            }
            .head-text {
              font-size: 14px;
              font-weight: 400;
            }
            .content-text {
              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
    }
  }
  .result-container {
    background-color: #212121f2;
    display: flex;
    justify-content: space-between;
    width: 70%;
    margin: 24px auto;
    padding: 24px;
    border-radius: 16px;
    .result-text-wrapper {
      .result-wrapper {
        span {
          color: white;
          font-size: 20px;
          font-weight: 700;
        }
        .unit {
          margin-left: 8px;
        }
      }
      .content {
        font-size: 14px;
        font-weight: 400;
        color: white;
      }
    }
    .confirm-button {
      width: fit-content;
      height: 48px;
      font-size: 20px;
      font-weight: 700;
    }
  }
`;

const ClaimsOrderPage = () => {
  const [openAddClaims, setOpenAddClaims] = useState(false);
  const [claimsData, setClaimsData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [size] = useState(5);
  const [ascending] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [claimsList, setClaimsList] = useState<OrderClaimTypeState>();
  const [loading, setLoading] = useState(false);
  const [fetchData, setFetchData] = useState(false);
  const [isActiveAcceptModal, setIsActiveAcceptModal] = useState(false);
  const [dataItem, setDataItem] = useState<any>('');

  const fetch = async () => {
    const responseClaimsData = await apiClaimsOrder.getClaimsOrder();
    if (responseClaimsData.status) {
      setClaimsData(responseClaimsData.data);
    }

    const responseClaimsList = await apiClaimsOrder.getClaimsOrderByCustomer(
      currentPage,
      size,
      ascending,
    );
    if (responseClaimsList.status) {
      setClaimsList(responseClaimsList.data.content);
      setTotalPages(responseClaimsList.data.totalElements);
    }
  };

  useEffect(() => {
    fetch();
  }, [currentPage, fetchData]);

  const handleSubmitClaimsOrder = async (values: any) => {
    setLoading(true);

    const payload = {
      orderId: parseInt(values.orderNumber),
      orderItemId: parseInt(values.orderItemId),
      amount: parseInt(values.amount),
      files: values.files,
      description: values.description,
    };
    const res = await apiClaimsOrder.createClaimsOrder(payload);
    if (res.status) {
      toast.success(res.message);
      fetch();
      setOpenAddClaims(false);
      setLoading(false);
    } else {
      setLoading(false);
      toast.error(res.err);
    }
  };

  return (
    <ClaimsOrderStyle>
      <div className="head-cart">
        <span className="subtitle">แจ้งเคลมสินค้า</span>
      </div>
      <div className="card-container">
        <div className="text-head-wrapper !mb-2">
          <span className="subtitle-bold">รายการแจ้งเคลม</span>
          <Button
            className="add-button"
            disabled={isEmpty(claimsData)}
            onClick={() => setOpenAddClaims(true)}
          >
            <Image
              priority
              src="/icons/add-white.svg"
              alt=""
              width={16}
              height={16}
            />
            แจ้งเคลมสินค้า
          </Button>
        </div>
        {isEmpty(claimsData) && (
          <div className="text-head-wrapper text-sm !justify-end text-red-500 ">
            ยังไม่มีรายการสั่งซื้อที่สามารถเคลมได้
          </div>
        )}
        {claimsData && (
          <ClaimsOrderModal
            handleSubmitAddress={async (values: any) => {
              await handleSubmitClaimsOrder(values);
            }}
            open={openAddClaims}
            handleClose={setOpenAddClaims}
            loading={loading}
            data={claimsData}
          />
        )}
        <CardClaimsCustom
          dataClaimsDetail={claimsList}
          setDataItem={setDataItem}
          setIsActiveAcceptModal={setIsActiveAcceptModal}
        />
        {!isEmpty(claimsList) && (
          <PaginationCustom
            totalPosts={totalPages}
            postsPerPage={size}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        )}
        <ClaimsOrderAcceptModal
          open={isActiveAcceptModal}
          handleClose={setIsActiveAcceptModal}
          data={dataItem}
          setFetch={setFetchData}
        />
      </div>
    </ClaimsOrderStyle>
  );
};

export default ClaimsOrderPage;
