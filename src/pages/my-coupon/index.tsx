import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
// import CouponStyle from '@/components/customUI/CouponCard/CouponStyle';
import CouponStyle from '@/styles/CouponStyle';
import Grid from '@mui/material/Grid2';
import Button from '@mui/material/Button';
import HistoryIcon from '@mui/icons-material/History';
import Footer from '@/components/customUI/Footer/Footer';
import { useRouter } from 'next/router';
import apiDiscount from '@/services/discount';
import LoadingButton from '@mui/lab/LoadingButton';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { isEmpty, isUndefined } from 'lodash';
import CouponCard from '@/components/customUI/CouponCard/CouponCard';
import { discountAll, discountCategory } from '@/utils/discount';

const MyCouponStyle = styled.div`
  min-height: 100vh;
  background: #fff;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  header {
    margin-top: 4.8rem;
    margin-bottom: 3rem;
    border-bottom: 1px solid #dbe2e5;
    padding: 1.5rem;
    h2 {
      text-align: center;
      font-size: 20px;
      font-weight: 700;
    }
    .box-mock {
      display: none;
    }
    .btn-history {
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      color: #212121;
      &.mobile {
        display: none !important;
      }
      .text {
        margin-left: 0.2rem;
      }
    }
  }
  .container {
    max-width: 1300px !important;
    .child-card-coupon {
      margin: 0 !important;

      .not-found {
        padding: 15rem 0;
        width: 100%;
        text-align: center;
        color: #9e9e9e;
      }
    }
    .tab-menu {
      margin: 3rem 0 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn-history {
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        color: #212121;
        .text {
          margin-left: 0.2rem;
        }
      }
      .Mui-selected {
        .amount {
          background: #b9e901;
          font-weight: 700;
        }
      }
      .amount {
        color: #212121;
        background: #fff;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 12px;
      }
      .MuiTabs-flexContainer {
        width: fit-content;
        background: #f5f5f5;
        border-radius: 40px;
        padding: 0.3rem;
        gap: 20px;
        .MuiButtonBase-root {
          min-height: unset;
          border-radius: 43px !important;
          padding: 5px;
          &.Mui-selected {
            background: #212121;
            color: #fff;
          }
        }
      }
      .MuiTabs-indicator {
        display: none !important;
      }
    }
  }
  .zone-card-coupon {
    padding-bottom: 3rem;
  }
  .box-more {
    text-align: center;
    margin-bottom: 1rem;
  }
  @media (max-width: 991px) {
    header {
      margin-top: 3.4rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding: 1rem;
      .box-mock {
        width: 35px;
        height: 35px;
        display: unset !important;
      }
      .btn-history {
        width: 35px;
        height: 35px;
        max-width: unset !important;
        min-width: unset !important;
        padding: 3px !important;
        align-items: center !important;
        &.mobile {
          display: flex !important;
        }
      }
    }
    .btn-history {
      display: none;
    }
  }
  @media (max-width: 768px) {
    .container {
      padding: 0 1rem;
    }
    .tab-menu {
      display: unset !important;
      .MuiTabs-flexContainer {
        width: 100% !important;
        justify-content: center;
        flex-wrap: wrap;
      }
    }
    .child-card-coupon {
      padding-top: 1rem !important;
    }
  }
  @media (max-width: 576px) {
    .tab-menu {
      .MuiTabs-flexContainer {
        border-radius: 20px !important;
      }
    }
    .zone-card-coupon {
      .not-found {
        padding: 10rem 0 !important;
      }
    }
  }
  @media (max-width: 425px) {
    .zone-card-coupon {
      padding-bottom: 7rem;
    }
  }
  @media (max-width: 375px) {
    .zone-card-coupon {
      .not-found {
        padding: 6rem 0 !important;
      }
    }
  }
`;
const Index = () => {
  const router = useRouter();
  const [valueSeeMore, setValueSeeMore] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [valueTab, setValueTab] = useState<any>(0);
  const [dataMyDiscount, setDataMyDiscount] = useState<any>({});
  const [data, setData] = useState<any>([]);

  const handleChange = (event: React.SyntheticEvent, newValue: any) => {
    setValueSeeMore(false);
    setValueTab(newValue);
  };
  const getUserDiscountList = async () => {
    setLoading(true);
    const res = await apiDiscount.getUserDiscountList({
      page: 0,
      size: 100,
      ascending: true,
    });
    if (res.status) {
      const allData = discountAll(res.data.content);
      setDataMyDiscount(res.data);
      setData(allData);
    }
    setLoading(false);
  };
  useEffect(() => {
    getUserDiscountList();
  }, []);
  const myCategoryDiscount = discountCategory(dataMyDiscount?.content);
  const allData = discountAll(dataMyDiscount?.content);
  useEffect(() => {
    if (valueTab !== 0) {
      setData(myCategoryDiscount?.[valueTab]);
    } else {
      setData(allData);
    }
  }, [valueTab]);
  return (
    <MyCouponStyle>
      <div>
        <header>
          <div className={'box-mock'}></div>
          <h2>คูปองของฉัน</h2>
          <Button
            variant="text"
            className={'btn-history mobile'}
            onClick={() => router.push('/my-coupon/history')}
          >
            <HistoryIcon />
          </Button>
        </header>
        <div className={'container'}>
          <div className={'tab-menu'}>
            <Tabs value={valueTab} onChange={handleChange}>
              <Tab
                value={0}
                icon={<div className={'amount'}>{allData?.length}</div>}
                iconPosition="end"
                label="ทั้งหมด"
              />
              {!isUndefined(myCategoryDiscount) &&
                Object.keys(myCategoryDiscount).map(
                  (item: any, index: number) => {
                    const data = myCategoryDiscount[item];
                    return (
                      <Tab
                        value={item}
                        icon={<div className={'amount'}>{data.length}</div>}
                        iconPosition="end"
                        label={item}
                        key={index}
                      />
                    );
                  },
                )}
            </Tabs>
            <Button
              variant="text"
              className={'btn-history'}
              onClick={() => router.push('/my-coupon/history')}
            >
              <HistoryIcon />
              <span className={'text'}>ประวัติ</span>
            </Button>
          </div>
          <div className={'zone-card-coupon'}>
            <CouponStyle>
              <div className={'child-card-coupon inModal'}>
                <Grid container spacing={2}>
                  {!isEmpty(data) &&
                    data.map((item: any, index: number) => {
                      if (!valueSeeMore) {
                        if (index > 11) {
                          return null;
                        }
                      }
                      return (
                        <Grid size={{ md: 4, sm: 6, xs: 12 }} key={index}>
                          <CouponCard
                            discountId={item.discount.id}
                            name={item.discount.title}
                            discountCategory={item.discount.discountCategory}
                            description={item.discount.description}
                            remainingRights={item.discount.maxUsage}
                            isShowModal={true}
                            isCollected={true}
                            discountCode={item.discount.discountCode}
                            startDate={item.discount.startDate}
                            endDate={item.discount.endDate}
                          />
                        </Grid>
                      );
                    })}
                </Grid>
              </div>
            </CouponStyle>
          </div>
          {allData?.length >= 12 && (
            <div className={'box-more'}>
              <LoadingButton
                variant={'text'}
                endIcon={
                  valueSeeMore ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />
                }
                onClick={() => {
                  setValueSeeMore(!valueSeeMore);
                }}
                loading={loading}
              >
                {valueSeeMore ? 'ดูน้อยลง' : 'ดูเพิ่มเติม'}
              </LoadingButton>
            </div>
          )}
        </div>
      </div>

      <Footer />
    </MyCouponStyle>
  );
};

export default Index;
