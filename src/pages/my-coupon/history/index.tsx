import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import CouponCard from '@/components/customUI/CouponCard/CouponCard';
import CouponStyle from '@/components/customUI/CouponCard/CouponStyle';
import Grid from '@mui/material/Grid2';
import Button from '@mui/material/Button';
import Footer from '@/components/customUI/Footer/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useRouter } from 'next/router';
import apiDiscount from '@/services/discount';
import dayjs from 'dayjs';
import { isActiveDiscount } from '@/utils/discount';

const MyCouponStyle = styled.div`
  min-height: 100vh;
  background: #fff;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4.8rem;
    margin-bottom: 3rem;
    border-bottom: 1px solid #dbe2e5;
    padding: 1.5rem;
    .box-mock {
      width: 40px;
      height: 40px;
    }
    .btn-go-back {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f5f5f5;
      color: #212121;
      min-width: unset !important;
    }
    h2 {
      text-align: center;
      font-size: 20px;
      font-weight: 700;
    }

    .btn-history {
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      color: #212121;
      &.mobile {
        display: none !important;
      }
      .text {
        margin-left: 0.2rem;
      }
    }
  }
  .container {
    max-width: 1300px !important;
    .child-card-coupon {
      margin: 0 !important;
      filter: grayscale(1);

      .not-found {
        padding: 8rem 0;
        width: 100%;
        text-align: center;
        color: #9e9e9e;
      }
    }
    .tab-menu {
      margin: 3rem 0 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn-history {
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        color: #212121;
        .text {
          margin-left: 0.2rem;
        }
      }
      .Mui-selected {
        .amount {
          background: #b9e901;
          font-weight: 700;
        }
      }
      .amount {
        color: #212121;
        background: #fff;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 12px;
      }
      .MuiTabs-flexContainer {
        width: fit-content;
        background: #f5f5f5;
        border-radius: 40px;
        padding: 0.3rem;
        gap: 20px;
        .MuiButtonBase-root {
          min-height: unset;
          border-radius: 43px !important;
          padding: 10px;
          &.Mui-selected {
            background: #212121;
            color: #fff;
          }
        }
      }
      .MuiTabs-indicator {
        display: none !important;
      }
    }
  }
  .zone-card-coupon {
    padding-bottom: 5rem;
  }
  @media (max-width: 991px) {
    header {
      margin-top: 3.4rem;
    }
  }
  @media (max-width: 576px) {
    .container {
      padding: 0 1rem;
    }
    .tab-menu {
      display: unset !important;
      .MuiTabs-flexContainer {
        width: fit-content !important;
        margin: auto;
        justify-content: center;
        flex-wrap: wrap;
      }
    }
    .child-card-coupon {
      padding-top: 1rem !important;
    }
    .btn-history {
      display: none;
    }
  }
  @media (max-width: 425px) {
    .zone-card-coupon {
      padding-bottom: 7rem;
    }
  }
`;
const Index = () => {
  const router = useRouter();
  const [dataMyDiscount, setDataMyDiscount] = useState<any>([]);
  const [data, setData] = useState<any>([]);
  const [value, setValue] = React.useState('used');

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
    const expiredData = dataMyDiscount?.filter((item: any) => {
      return dayjs().unix() > dayjs(item.discount.endDate).unix();
    });
    const activeData = isActiveDiscount(dataMyDiscount);
    switch (newValue) {
      case 'expire':
        setData(expiredData);
        return;
      default:
        setData(activeData);
    }
  };
  const getUserDiscountList = async () => {
    const res = await apiDiscount.getUserDiscountList({
      page: 0,
      size: 100,
      ascending: true,
    });
    if (res.status) {
      setDataMyDiscount(res.data.content);
      const activeData = isActiveDiscount(res.data.content);
      setData(activeData);
    }
  };
  useEffect(() => {
    getUserDiscountList();
  }, []);
  return (
    <MyCouponStyle>
      <div>
        <header>
          <Button
            variant="text"
            className={'btn-go-back'}
            onClick={() => router.push('/my-coupon')}
          >
            <ArrowBackIcon />
          </Button>
          <h2>ประวัติการใช้งานคูปอง</h2>
          <div className="box-mock"></div>
        </header>
        <div className={'container'}>
          <div className={'tab-menu'}>
            <Tabs value={value} onChange={handleChange}>
              <Tab value="used" iconPosition="end" label="ใช้แล้ว" />
              <Tab value="expire" iconPosition="end" label="หมดอายุ" />
            </Tabs>
          </div>
          <div className={'zone-card-coupon'}>
            <CouponStyle>
              <div className={'child-card-coupon inModal'}>
                <Grid container spacing={2}>
                  {dataMyDiscount?.length > 0 ? (
                    data?.map((item: any, index: number) => (
                      <Grid size={{ md: 4, sm: 6, xs: 12 }} key={index}>
                        <CouponCard
                          discountId={item.discount.id}
                          name={item.discount.title}
                          description={item.discount.description}
                          remainingRights={item.discount.maxUsage}
                          discountCategory={item.discount.discountCategory}
                          isShowModal={true}
                          isHide={true}
                        />
                      </Grid>
                    ))
                  ) : (
                    <div className={'not-found'}>
                      <p>ไม่พบคูปอง</p>
                    </div>
                  )}
                </Grid>
              </div>
            </CouponStyle>
          </div>
        </div>
      </div>
      <Footer />
    </MyCouponStyle>
  );
};

export default Index;
