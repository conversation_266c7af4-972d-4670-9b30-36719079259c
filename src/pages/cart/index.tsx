import React, { useEffect } from 'react';
import ShoppingCartOutlinedIcon from '@mui/icons-material/ShoppingCartOutlined';
import styled from 'styled-components';
import CartCheckoutForm from '@/components/newCustomUI/cart/CartCheckoutForm';
import {
  dataAddCartSelector,
  fetchGetMyCart,
} from '@/store/reducers/cartSlice';
import { useAppDispatch } from '@/store/index';
import { useSelector } from 'react-redux';
import { isEmpty } from 'lodash';
import CartSelectForm from '@/components/newCustomUI/cart/CartSelectForm';
import EmptyDataMessage from '@/components/common/EmptyDataMessage';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { ICartValue, IMyCartItem } from '@/store/type/cart';

const CartPageStyle = styled.div`
  padding-top: 7rem;
  header {
    > div {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 40px;
      font-weight: bold;
      svg {
        font-size: 40px;
      }
    }
  }
  .content {
    padding-top: 2rem;
    display: grid;
    grid-template-columns: 900px 1fr;
    gap: 1.5rem;
    .cart-content {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
  }
`;
const validationSchema = yup.object({
  cartValue: yup
    .array()
    .min(1, 'กรุณาเลือกสินค้าอย่างน้อย 1 รายการ')
    .required('กรุณาเลือกสินค้า'),
});
const CartPage = () => {
  const dispatch = useAppDispatch();
  const { cart } = useSelector(dataAddCartSelector);
  const formik = useFormik<ICartValue>({
    initialValues: {
      cartValue: cart,
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      console.log(values);
    },
  });
  const calculateTotalPrice = (total: IMyCartItem[]) => {
    if (isEmpty(total)) return 0;
    return total.reduce((total: number, item: any) => {
      return total + item.totalPrice;
    }, 0);
  };
  const totalCartPrice = calculateTotalPrice(formik.values.cartValue);
  useEffect(() => {
    dispatch(fetchGetMyCart());
  }, []);
  return (
    <CartPageStyle>
      <div className={'container min-w-[1400px] w-full mx-auto'}>
        <header>
          <div>
            <ShoppingCartOutlinedIcon />
            <h3>รถเข็น</h3>
            {!isEmpty(cart) && <span>: {cart.length}</span>}
          </div>
        </header>
        <form onSubmit={formik.handleSubmit}>
          <div className={'content'}>
            <div className={'cart-content'}>
              {!isEmpty(cart) ? (
                cart.map((item: any, index: number) => (
                  <CartSelectForm
                    key={index}
                    dataModel={item}
                    formik={formik}
                  />
                ))
              ) : (
                <EmptyDataMessage
                  message={'ไม่พบสินค้าในรถเข็น'}
                  height={'165px'}
                />
              )}
            </div>
            <CartCheckoutForm
              totalCartPrice={totalCartPrice}
              cartValue={formik.values.cartValue}
            />
          </div>
        </form>
      </div>
    </CartPageStyle>
  );
};

export default CartPage;
