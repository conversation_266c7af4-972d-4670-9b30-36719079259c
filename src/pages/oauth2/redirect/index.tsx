import React from 'react';
import { setCookie } from 'cookies-next';
import { GetServerSideProps } from 'next';

const RedirectPage = () => {
  return (
    <div className="mt-100px h-screen flex justify-center items-center">
      <h1 className="text-white text-4xl">Redirect...</h1>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const { token } = query;
  if (token) {
    try {
      setCookie('access_token', token, { req, res, maxAge: 60 * 60 * 24 });
    } catch (error) {
      console.error(error);
    }
    return {
      redirect: {
        destination: '/welcome',
        permanent: false,
      },
    };
  }
  return {
    redirect: {
      destination: '/login',
      permanent: false,
    },
  };
};

export default RedirectPage;
