import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as yup from 'yup';
import { getAuthUrl } from '@/utils/lineAuth';
import styled from 'styled-components';
import Image from 'next/image';
import { toast } from 'sonner';
import apiUser from '@/services/user';
import { ReloadIcon } from '@radix-ui/react-icons';

const RegisterStyle = styled.div`
  display: flex;
  flex-direction: column;
  text-align: center;
  position: relative;
  width: 500px;
  padding-top: 80px;
  @media (max-width: 768px) {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .line {
    position: relative;
    height: 52px;
    padding: 10px 8px;
    border-radius: 8px;
    color: white;
  }
  .box-form-register {
    @media (max-width: 576px) {
      flex-direction: column !important;
      gap: 25px;
      .box-field {
        width: 100% !important;
        margin-right: 0 !important;
        margin-left: 0 !important;
      }
    }
  }
`;

const RegisterPage = () => {
  const [acceptPolicy, setAcceptPolicy] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const registerSchema = yup.object().shape({
    firstname: yup.string().required('กรุณากรอก ชื่อจริง'),
    lastname: yup.string().required('กรุณากรอก นามสกุล'),
    email: yup
      .string()
      .email('กรุณากรอกอีเมลให้ถูกต้อง')
      .required('กรุณากรอกอีเมล'),
    password: yup.string().required('กรุณากรอกรหัสผ่าน'),
  });

  const handleRegister = async (values: any) => {
    setLoading(true);

    const res = await apiUser.register(values);
    if (res && !res.isError) {
      setLoading(false);
      toast.success(res.message);
      setTimeout(() => {
        router.push(`/verify-email?email=${values.email}`);
      }, 2000);
    } else {
      setLoading(false);
      toast.error(res.error);
    }
  };

  return (
    <RegisterStyle>
      <div className="flex flex-col justify-center items-center mt-[60px] w-full mb-[72px]">
        <Image
          priority
          className="m-auto mb-4"
          src="/icons/digibox-icon-gray.png"
          alt=""
          width={56}
          height={64}
        />
        <Image
          priority
          className="h-[24px] w-[150px] mb-4"
          src="/icons/DIGIBOXS.png"
          alt=""
          height={24}
          width={150}
        />
        <label className="text-white text-m">สร้างบัญชี</label>
      </div>
      <Link
        href={getAuthUrl('line')}
        className="line bg-[#06c755] hover:!bg-[#07d55b] content-center"
      >
        <Image
          priority
          className="absolute top-[20%] left-[2%]"
          src="/icons/LINE_logo%201.png"
          alt=""
          height={32}
          width={32}
        />
        <span className="font-bold text-lg text-white">Sign in with LINE</span>
        <div></div>
      </Link>
      <div className="flex w-full items-center my-8">
        <label className="font-normal text-m text-white w-full">หรือ</label>
      </div>
      <Formik
        initialValues={{
          firstname: '',
          lastname: '',
          email: '',
          password: '',
        }}
        validationSchema={registerSchema}
        onSubmit={(values: any) => handleRegister(values)}
      >
        <Form>
          <div className="flex flex-row text-start mb-8 box-form-register">
            <div className="mr-2 w-1/2 box-field">
              <label className="text-bold text-m text-white">ชื่อ</label>
              <Field name="firstname">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุชื่อจริง'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
            <div className="ml-2 w-1/2 box-field">
              <label className="text-bold text-m text-white">นามสกุล</label>
              <Field name="lastname">
                {({ field }: any) => (
                  <div>
                    <Input
                      disabled={loading}
                      name={field.name}
                      className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                      placeholder={'ระบุนามสกุล'}
                      onChange={field.onChange}
                      value={field.value}
                    />
                    <ErrorMessage name={field.name}>
                      {(msg) => <div className="text-red-500">{msg}</div>}
                    </ErrorMessage>
                  </div>
                )}
              </Field>
            </div>
          </div>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">อีเมล</label>
            <Field name="email">
              {({ field }: any) => (
                <div>
                  <Input
                    disabled={loading}
                    name={field.name}
                    type={'email'}
                    className="text-white bg-[#42424233] mt-[14px] border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุอีเมล'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  <ErrorMessage name={field.name}>
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex-row text-start mb-8">
            <label className="text-bold text-m text-white">รหัสผ่าน</label>
            <Field name="password">
              {({ field }: any) => (
                <div className="relative">
                  <Input
                    disabled={loading}
                    name={field.name}
                    className="text-white bg-[#42424233] mt-3.5 border-[#685B7E] border-[1px] focus-visible:ring-0 focus-visible:ring-offset-0 outline-0"
                    placeholder={'ระบุรหัสผ่าน'}
                    type={showPassword ? 'texts' : 'password'}
                    onChange={field.onChange}
                    value={field.value}
                  />
                  {!showPassword ? (
                    <Image
                      priority
                      className={`absolute top-[4px] right-[4px] cursor-pointer`}
                      src="/icons/hide-password.svg"
                      alt=""
                      height={32}
                      width={32}
                      onClick={() => setShowPassword(true)}
                    />
                  ) : (
                    <Image
                      priority
                      className={`absolute top-[4px] right-[4px] cursor-pointer`}
                      src="/icons/show-password.svg"
                      alt=""
                      height={32}
                      width={32}
                      onClick={() => setShowPassword(false)}
                    />
                  )}
                  <ErrorMessage name={field.name}>
                    {(msg) => <div className="text-red-500">{msg}</div>}
                  </ErrorMessage>
                </div>
              )}
            </Field>
          </div>
          <div className="flex justify-between mb-8">
            <div className="flex items-center">
              <Checkbox
                disabled={loading}
                onCheckedChange={(value: any) => {
                  setAcceptPolicy(value);
                }}
                className="border-[#685B7E] w-6 h-6 mr-3.5 rounded data-[state=checked]:bg-secondary-main"
              />
              <div className="w-2/3 text-left">
                <label
                  className="text-m text-white"
                  style={{ lineHeight: 'normal' }}
                >
                  การสร้างบัญชีหมายความว่าคุณยอมรับ
                </label>
                <label
                  className="ml-2 text-m text-white underline cursor-pointer"
                  onClick={() => router.push('/policy')}
                >
                  เงื่อนไขการใช้บริการ และ นโยบายความเป็นส่วนตัว
                </label>
              </div>
            </div>
          </div>
          <div className="relative">
            <Button
              type="submit"
              disabled={!acceptPolicy}
              className="font-bold text-[20px] w-full h-[50px] mb-[120px] bg-white text-black hover:bg-white hover:text-secondary-main"
            >
              {loading ? (
                <>
                  <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                  กำลังดำเนินการ
                </>
              ) : (
                'สร้างบัญชี'
              )}
            </Button>
            <div className="flex justify-center absolute m-auto left-0 right-0 top-[70%]">
              <label className="text-sm text-white mr-2">
                มีบัญชีผู้ใช้งานแล้ว?
              </label>
              <Link
                className="text-sm font-bold text-white cursor-pointer"
                href="/login"
              >
                เข้าสู่ระบบ
              </Link>
            </div>
          </div>
        </Form>
      </Formik>
    </RegisterStyle>
  );
};

export default RegisterPage;
