import React, { Fragment, useEffect, useState } from 'react';

import apiUser from '@/services/user';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/index';
import {
  getUserProfile,
  userSelector,
} from '@/store/reducers/backup/userSlice';
import { couponSelector, setCoupon } from '@/store/reducers/backup/couponSlice';
import { useRouter } from 'next/router';
import {
  getCartOrders,
  orderSelector,
  setShippingCode,
} from '@/store/reducers/backup/orderSlice';
import { Button } from '@/components/ui/button';
import TaxCardCustom from '@/components/customUI/TaxCardCustom';
import UserCardCustom from '@/components/customUI/UserCardCustom';
import { numberWithCommas } from '@/utils/numberFormat';

import Link from 'next/link';
import Image from 'next/image';
import { OrdersHeaderStyle, OrdersStyle } from '@/styles/orders.styled';
import CardOrdersCustom from '@/components/customUI/CardOrdersCustom';
import { AddressType } from '@/types/address';
import apiOrder from '@/services/order';
import { OrderItemType } from '@/store/type/order';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { useSelector } from 'react-redux';
import ModalCouponList from '@/components/customUI/modal/ModalCouponList';
import { calculatePercentage } from '@/utils/calculator';
import { dataResult, priceDiscountVat } from '@/utils/discount';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';

const OrdersPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isActiveModal, setIsActiveModal] = useState(false);
  const [isActiveAdd, setIsActiveAdd] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [dataAddress, setDataAddress] = useState<AddressType[]>([]);
  const [dataTax, setDataTax] = useState<any[]>([]);
  const [nextStep, setNextStep] = useState(1);
  const dispatch = useAppDispatch();
  const [editModeTax, setEditModeTax] = useState(false);
  const [editModeUser, setEditModeUser] = useState(false);
  const { user } = useSelector(userSelector);
  const couponSelectors = useSelector(couponSelector);
  const [isActiveModalUser, setIsActiveModalUser] = useState(false);
  const [isActiveModalTax, setIsActiveModalTax] = useState(false);
  const [selectedTaxAction, setSelectedTaxAction] = useState<any>({
    id: 0,
    name: '',
    phoneNumber: '',
    address: '',
    zipCode: '',
    province: '',
    district: '',
    subDistrict: '',
    isTax: false,
    taxPayerType: 0,
    taxId: '',
    email: '',
  });
  const [validateAddress, setValidateAddress] = useState(true);
  const [orderId, setOrderId] = useState();
  const { orders } = useSelector(orderSelector);
  const [selectedItem, setSelectedItem] = useState(1);
  const handleSubmitAddress = async (id: number | undefined, values: any) => {
    if (editMode && !isUndefined(id)) {
      setLoading(true);
      const payload = {
        name: `${values.firstname} ${values.lastname}`,
        phoneNumber: values.phoneNumber,
        email: values.email,
        address: values.address,
        zipCode: values.zipCode,
        province: values.province,
        district: values.district,
        subDistrict: values.subDistrict,
        isTax: false,
        taxPayerType: 0,
        taxId: null,
      };
      setLoading(true);
      const resEdit = await apiUser.editAddress(id, payload);
      if (resEdit && !resEdit.isError) {
        toast.success(resEdit.message);
        fetchData();
        setEditMode(false);
        setIsActiveAdd(false);
      } else {
        toast.error(resEdit.error);
      }
      setLoading(false);
    } else {
      setLoading(true);
      const payload = {
        name: `${values.firstname} ${values.lastname}`,
        phoneNumber: values.phoneNumber,
        email: values.email,
        address: values.address,
        zipCode: values.zipCode,
        province: values.province,
        district: values.district,
        subDistrict: values.subDistrict,
        isTax: values.isTax,
        taxPayerType: 0,
        taxId: '',
      };
      setLoading(true);
      const resCreate = await apiUser.createAddress(payload);
      if (resCreate && !resCreate.isError) {
        toast.success(resCreate.message);
        fetchData();
        setOrderId(resCreate.data);
        setIsActiveAdd(false);
      } else {
        toast.error(resCreate.error);
      }
      setLoading(false);
    }
  };

  const handleSelectAddress = (address: AddressType) => {
    const mapData = {
      ...orders,
      items: orders.items.map((orderData: OrderItemType) => {
        if (orderData.itemId === selectedItem) {
          return {
            ...orderData,
            name: address.name,
            recipientName: address.name,
            phoneNumber: address.phoneNumber,
            address: address.address,
            email: address.email,
            district: address.district,
            subDistrict: address.subDistrict,
            province: address.province,
            zipCode: address.zipCode,
            status: 0,
          };
        }
        return {
          ...orderData,
        };
      }),
    };
    dispatch(setShippingCode(mapData));
  };

  const fetchData = async () => {
    try {
      const resAddress = await apiUser.getAddress(false);
      if (resAddress.status) {
        setDataAddress(resAddress.data);
      }
      const resAddressTax = await apiUser.getAddress(true);
      if (resAddressTax.status) {
        setDataTax(resAddressTax.data);
      }
    } catch (error) {
      console.error('error', error);
    }
  };

  useEffect(() => {
    dispatch(getCartOrders());
    fetchData();
  }, []);

  useEffect(() => {
    if (!isEmpty(orders) && !isUndefined(orders)) {
      checkAddressAndShipping(orders.items);
    }
  }, [orders]);

  const handleSubmitUser = async (values: any) => {
    const formData = new FormData();
    formData.append('updateUserRequest', JSON.stringify(values));
    const res = await apiUser.updateProfile(formData);
    if (res && !res.isError) {
      toast.success(res.message);
      dispatch(getUserProfile());
      setLoading(false);
      setIsActiveModalUser(false);
    } else {
      setLoading(false);
      toast.error(res.error);
    }
  };

  const handleSubmitTax = async (values: any) => {
    let payload = {};
    if (parseInt(values.taxPayerType) === 1) {
      payload = {
        ...values,
        name: `${values.firstname} ${values.lastname}`,
        taxPayerType: parseInt(values.taxPayerType),
      };
    } else {
      payload = {
        ...values,
        name: values.taxPayerName,
        taxPayerType: parseInt(values.taxPayerType),
      };
    }
    if (editModeTax) {
      setLoading(true);
      const resEdit = await apiUser.editAddress(selectedTaxAction.id, payload);
      if (resEdit.status) {
        toast.success(resEdit.message);
        fetchData();
        setIsActiveModalTax(false);
      } else {
        toast.error(resEdit.error);
      }
      setLoading(false);
    } else {
      setLoading(true);
      const resCreate = await apiUser.createAddress(payload);
      if (resCreate && !resCreate.isError) {
        toast.success(resCreate.message);
        fetchData();
        setSelectedTaxAction(payload);
        setIsActiveModalTax(false);
      } else {
        toast.error(resCreate.error);
      }
      setLoading(false);
    }
  };
  const sumVat = Number(priceDiscountVat(orders.totalPrice, couponSelectors));
  const sumPrice = dataResult(
    couponSelectors,
    orders.totalPrice,
    orders.shippingCost,
    sumVat,
  );

  const submitOrder = async () => {
    setLoading(true);
    const discountList = couponSelectors.map((item: any) => {
      return item.id;
    });

    const payload = {
      items: orders.items.map((newArr: any) => {
        return {
          itemId: newArr.itemId,
          totalPrice: newArr.totalPrice,
          shipping: newArr.shipping,
          shippingCost: newArr.shippingCost,
          recipientName: newArr.name,
          email: newArr.email,
          phoneNumber: newArr.phoneNumber,
          address: newArr.address,
          district: newArr.district,
          subDistrict: newArr.subDistrict,
          province: newArr.province,
          zipCode: newArr.zipCode,
        };
      }),
      isTax: selectedTaxAction.isTax,
      taxPayerType: selectedTaxAction.taxPayerType,
      taxId: selectedTaxAction.taxId,
      taxPayerName: selectedTaxAction.name,
      phoneNumber: selectedTaxAction.phoneNumber,
      address: selectedTaxAction.address,
      district: selectedTaxAction.district,
      subDistrict: selectedTaxAction.subDistrict,
      province: selectedTaxAction.province,
      zipCode: selectedTaxAction.zipCode,
      email: selectedTaxAction.email,
      discounts: discountList,
    };
    const responseCreateOrder = await apiOrder.createOrder(payload);
    if (responseCreateOrder.status) {
      dispatch(setCoupon({}));
      setLoading(false);
      setOrderId(responseCreateOrder.data);
      setNextStep(3);
    } else {
      toast.error(responseCreateOrder.error);
    }
  };

  const handleSelectShipping = (itemId: number, shippingId: number) => {
    const mapData = {
      ...orders,
      items: orders.items.map((orderData: OrderItemType) => {
        if (orderData.itemId === itemId) {
          return {
            ...orderData,
            shipping: shippingId,
            shippingCost: shippingId === 1 ? 0 : 500,
            totalPrice: orderData.totalPrice,
            recipientName: null,
            phoneNumber: null,
            address: null,
            district: null,
            subDistrict: null,
            province: null,
            zipCode: null,
            status: 0,
          };
        }
        return {
          ...orderData,
        };
      }),
    };
    const sumShippingCost = mapData.items.reduce((sum: number, obj: any) => {
      return sum + obj.shippingCost;
    }, 0);
    dispatch(setShippingCode({ ...mapData, shippingCost: sumShippingCost }));
  };

  function checkAddressAndShipping(items: OrderItemType[]) {
    for (const item of items) {
      if (item.shipping === 2 && isNull(item.address)) {
        toast.error('กรุณาเลือกที่อยู่');
        setValidateAddress(false);
        return;
      }
      if (item.shipping !== 1 && item.shipping !== 2) {
        setValidateAddress(false);
        return;
      }
      setValidateAddress(true);
    }
  }
  const [open, setOpen] = React.useState(false);
  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => setOpen(false);
  const removeCoupon = (id: number) => {
    const dataRemoveCoupon = couponSelectors.filter(
      (item: any) => item.id !== id,
    );
    dispatch(setCoupon(dataRemoveCoupon));
  };
  return (
    <OrdersStyle>
      {nextStep === 3 ? (
        <div className="success-container">
          <div className="success-wrapper">
            <Image
              priority
              className="flare absolute"
              src="/images/flare.svg"
              alt=""
              width={400}
              height={200}
            />
            <div className="bg-[#EBFEEB] m-4 p-4 rounded-full">
              <Image
                priority
                className=" w-[40px] h-[40px]"
                src="/icons/check.svg"
                alt=""
                width={40}
                height={40}
              />
            </div>
            <span className="subtitle">รายการสั่งซื้อ สำเร็จ!</span>
            <span className="subheader">{orderId}</span>
            <span className="content">
              เราได้รับการสั่งซื้อของคุณแล้ว
              เราจะส่งข้อมูลใบเสนอราคาไปทางอีเมลของคุณ
            </span>
            <span className="content">
              คุณสามารถตรวจสอบสถนาะของคุณได้ที่ “รอเสนอราคา”
            </span>
            <Button
              className="button"
              onClick={() => router.push('/my-orders')}
            >
              รายการรอเสนอราคา
            </Button>
          </div>
        </div>
      ) : (
        <OrdersHeaderStyle>
          {nextStep === 1 ? (
            <Link className="subtitle" href={'/src/pages/_backup/cart'}>
              <Image
                priority
                className="w-[40px] h-[40px] cursor-pointer"
                src="/icons/back-icon.svg"
                alt=""
                width={80}
                height={80}
              />
            </Link>
          ) : (
            <Image
              priority
              onClick={() => setNextStep(1)}
              className="w-[40px] h-[40px] cursor-pointer"
              src="/icons/back-icon.svg"
              alt=""
              width={80}
              height={80}
            />
          )}
          <span className="subtitle">
            {nextStep === 1 ? 'รายการสั่งซื้อ' : 'ข้อมูลผู้สั่งซื้อ'}
          </span>
          <span className="subtitle">{nextStep}/2</span>
        </OrdersHeaderStyle>
      )}
      {nextStep === 1 && (
        <div className="card-container">
          {!isEmpty(orders) && !isUndefined(orders) && (
            <Fragment>
              {orders.items.map((data: OrderItemType, index: number) => (
                <CardOrdersCustom
                  editMode={editMode}
                  validateAddress={validateAddress}
                  data={data}
                  dataAddress={dataAddress}
                  loading={loading}
                  key={index}
                  selectedItem={selectedItem}
                  setSelectedItem={setSelectedItem}
                  handleSelectShipping={(id: number, shippingId: number) =>
                    handleSelectShipping(id, shippingId)
                  }
                  handleSelectAddress={(address: AddressType) =>
                    handleSelectAddress(address)
                  }
                  handleSubmitAddress={(id: number | undefined, address: any) =>
                    handleSubmitAddress(id, address)
                  }
                  isActiveModal={isActiveModal}
                  setIsActiveModal={setIsActiveModal}
                  isActiveAdd={isActiveAdd}
                  setIsActiveAdd={setIsActiveAdd}
                  setEditMode={setEditMode}
                />
              ))}
              <div className="coupon-container">
                <header>
                  <div className="title">
                    <Image
                      src={'/images/confirmation_number.svg'}
                      alt="icon"
                      width={'25'}
                      height={'25'}
                    />
                    <b>คูปองส่วนลด</b>
                  </div>
                  <div className="see-coupon">
                    <Button onClick={handleOpen}>ดูคูปองที่ใช้ได้</Button>
                  </div>
                </header>
                {!isEmpty(couponSelectors) &&
                  couponSelectors.map((item: any, index: number) => {
                    const { totalPrice } = orders;
                    const { percentage, maxDiscount, id, title } = item;
                    const priceDiscount =
                      calculatePercentage(totalPrice, percentage) > maxDiscount
                        ? maxDiscount
                        : calculatePercentage(totalPrice, percentage);
                    return (
                      <div className="detail-grid" key={index}>
                        <div className="detail-price">
                          <div className={'detail'}>
                            <span>{title}</span>
                          </div>
                          <div className={'price'}>
                            {' '}
                            <span>- ฿{numberWithCommas(priceDiscount, 2)}</span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          style={{ color: '#d32f2f' }}
                          onClick={() => removeCoupon(id)}
                        >
                          <DeleteForeverIcon />
                        </Button>
                      </div>
                    );
                  })}
                <ModalCouponList open={open} handleClose={handleClose} />
              </div>
              <div className="result-container">
                <div>
                  <b>สรุปคำสั่งซื้อ</b>
                </div>
                <div className="result-text-wrapper">
                  <div className="w-1/2 flex flex-col">
                    <span className="content-1">ราคาสินค้าทั้งหมด</span>
                    <span className="content-1">ภาษีมูลค่าเพิ่ม 7%</span>
                    <span className="content-1">ค่าจัดส่ง</span>
                    {!isEmpty(couponSelectors) &&
                      couponSelectors.map((item: any, index: number) => {
                        return (
                          <span className="content-1" key={index}>
                            {item.discountCategory.name}
                          </span>
                        );
                      })}
                  </div>
                  <div className="w-1/2 flex flex-col text-right">
                    <span className="content-2">
                      {numberWithCommas(orders.totalPrice)}
                    </span>
                    <span className="content-2">
                      {numberWithCommas(sumVat)}
                    </span>
                    <span className="content-2">
                      {numberWithCommas(orders.shippingCost)}
                    </span>
                    {!isEmpty(couponSelectors) &&
                      couponSelectors.map((item: any, index: number) => {
                        const { totalPrice } = orders;
                        const { percentage, maxDiscount } = item;
                        const priceDiscount =
                          calculatePercentage(totalPrice, percentage) >
                          maxDiscount
                            ? maxDiscount
                            : calculatePercentage(totalPrice, percentage);
                        return (
                          <span
                            style={{ color: '#E5200C' }}
                            className="content-2"
                            key={index}
                          >
                            - ฿{numberWithCommas(priceDiscount, 2)}
                          </span>
                        );
                      })}
                  </div>
                </div>
              </div>
              <div className="total-container">
                <span className="subtitle">ยอดชำระเงินทั้งหมด</span>
                <span className="subheader">
                  {numberWithCommas(sumPrice?.sumTotalPrice)}
                </span>
              </div>
              <Button
                disabled={!validateAddress}
                className="confirm-button bg-primary-main hover:bg-primary-light"
                onClick={() => {
                  if (validateAddress) {
                    setNextStep(2);
                  }
                }}
              >
                ดำเนินการต่อไป
              </Button>
            </Fragment>
          )}
        </div>
      )}
      {nextStep === 2 && (
        <div className="card-container">
          <UserCardCustom
            submit={handleSubmitUser}
            loading={loading}
            setEditMode={setEditModeUser}
            editMode={editModeUser}
            editData={user}
            isActive={isActiveModalUser}
            setIsActive={setIsActiveModalUser}
          />
          <TaxCardCustom
            submit={handleSubmitTax}
            loading={loading}
            setEditMode={setEditModeTax}
            editMode={editModeTax}
            editData={dataTax}
            isActive={isActiveModalTax}
            setIsActive={setIsActiveModalTax}
            setSelectedAction={setSelectedTaxAction}
          />
          <Button
            disabled={
              isEmpty(dataTax) ||
              isNull(user.phoneNumber) ||
              isUndefined(user.phoneNumber)
            }
            className="confirm-button bg-primary-main hover:bg-primary-light mt-6"
            onClick={submitOrder}
          >
            ยืนยันการสั่งซื้อ
          </Button>
        </div>
      )}
    </OrdersStyle>
  );
};

export default OrdersPage;
