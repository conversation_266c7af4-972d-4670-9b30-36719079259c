import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiOrder from '@/services/order';
import CardMyOrderCustom from '@/components/customUI/CardMyOrderCustom';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { isEmpty, isUndefined } from 'lodash';

const MyOrdersStyled = styled.div`
  height: fit-content;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .header-wrapper {
    display: flex;
    justify-content: center;
    padding: 24px;
    .subtitle {
      font-size: 20px;
      font-weight: 700;
    }
  }
  .menu-wrapper {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #dbe2e5;
    border-top: 1px solid #dbe2e5;
    padding: 16px;
    @media only screen and (max-width: 430px) {
      overflow-y: scroll;
      justify-content: unset;
    }
    @media only screen and (max-width: 830px) {
      overflow-y: scroll;
      justify-content: unset;
    }
    .menu-item {
      cursor: pointer;
      border: 1px solid #dbe2e5;
      display: flex;
      padding: 4px 8px;
      margin-right: 24px;
      @media only screen and (max-width: 430px) {
        width: fit-content;
        flex-direction: row;
        align-items: center;
        height: 40px;
      }
      @media only screen and (max-width: 830px) {
        width: fit-content;
        flex-direction: row;
        align-items: center;
        height: 40px;
      }
      &:last-child {
        margin-right: 0;
      }
      .text-menu {
        color: #757575;
        @media only screen and (max-width: 430px) {
          height: 32px;
          width: max-content;
          align-content: center;
        }
        @media only screen and (max-width: 830px) {
          height: 32px;
          width: max-content;
          align-content: center;
        }
      }
      .count {
        margin-left: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px;
        width: 20px;
        height: 20px;
        border-radius: 100%;
        background-color: #757575;
        color: white;
        line-height: 1;
      }
    }
    .selected {
      border: 1px solid #ff4f00;
      background-color: #ff4f00;
      .text-menu {
        color: white;
      }
      .count {
        background-color: white;
        color: #ff4f00;
      }
    }
  }
  .my-orders-wrapper {
    margin: 30px auto;
    width: 66.66%;
    @media only screen and (max-width: 430px) {
      margin: 20px 0;
      width: 100%;
    }
  }
`;

const MyOrdersPage = () => {
  const [listMenu, setListMenu] = useState<any>([]);
  const [selectedMenu, setSelectedMenu] = useState('waitingQuotation');
  const searchParams = useSearchParams();

  const fetchData = async () => {
    const resAllOrders = await apiOrder.getAllOrders();
    if (resAllOrders.status) {
      setListMenu(resAllOrders.data);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const mapStatus = (key: string) => {
    switch (key) {
      case '1':
        return 'waitingQuotation';
      case '2':
        return 'waitingPayment';
      case '3':
        return 'verifyPayments';
      case '4':
        return 'production';
      case '5':
        return 'successful';
      case '6':
        return 'canceled';
      default:
        return ''; // Return -1 for keys that don't match any case
    }
  };

  const mapKey = (key: string) => {
    switch (key) {
      case 'waitingQuotation':
        return 'รอเสนอราคา';
      case 'waitingPayment':
        return 'รอชำระเงิน ';
      case 'verifyPayments':
        return 'ตรวจสอบการชำระเงิน';
      case 'production':
        return 'ดำเนินการผลิต';
      case 'successful':
        return 'สำเร็จ';
      case 'canceled':
        return 'ยกเลิก';
      default:
        return -1; // Return -1 for keys that don't match any case
    }
  };

  useEffect(() => {
    if (searchParams.get('status')) {
      const status = searchParams.get('status');
      setSelectedMenu(mapStatus(status || '1'));
    }
  }, []);

  return (
    <MyOrdersStyled className="pt-[80px] w-full">
      <div className="header-wrapper">
        <span className="subtitle">รายการสั่งซื้อ</span>
      </div>
      <div className="menu-wrapper p-6">
        {Object.entries(listMenu).map(([key], index) => (
          <div
            key={index}
            className={`${key === selectedMenu ? 'selected' : ''} p-2 menu-item rounded-full`}
            onClick={() => setSelectedMenu(key)}
          >
            <span className="text-menu">{mapKey(key)}</span>
            <span className="count">{listMenu[key].count}</span>
          </div>
        ))}
      </div>
      <div className="my-orders-wrapper">
        {!isUndefined(listMenu[selectedMenu]) &&
        !isEmpty(listMenu[selectedMenu].orders) ? (
          listMenu[selectedMenu].orders.map((data: any, index: number) => (
            <CardMyOrderCustom
              key={index}
              data={data}
              selectKey={selectedMenu}
              fetchData={fetchData}
            />
          ))
        ) : (
          <div className="bg-[#F5F5F5] h-[400px] rounded-xl flex flex-col items-center justify-center">
            <Image
              priority
              className="mb-2"
              src="/icons/edit_document.svg"
              alt=""
              width={40}
              height={40}
            />
            <span className="text-[#BDBDBD] text-[14px]">
              ไม่มีรายการสั่งซื้อ
            </span>
          </div>
        )}
      </div>
    </MyOrdersStyled>
  );
};

export default MyOrdersPage;
