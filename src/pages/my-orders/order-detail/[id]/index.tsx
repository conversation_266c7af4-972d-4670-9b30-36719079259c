import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { useRouter } from 'next/router';
import apiOrder from '@/services/order';
import OrderDetailCustom from '@/components/customUI/OrderDetailCustom';
import AcceptProductModal from '@/components/customUI/modal/AcceptProductModal';

const OrderDetailStyled = styled.div`
  padding-top: 80px;
  width: 100%;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .header-container {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    padding: 24px;

    .subheader {
      font-size: 20px;
      font-weight: 700;
      color: #212121;
    }
    .zone-empty {
      width: 40px;
      height: 40px;
    }
  }
  .content-container {
    .head-content-wrapper {
      width: 66.66%;
      margin: 48px auto;
      @media only screen and (max-width: 430px) {
        width: 90%;
        margin: 24px auto;
      }
    }
    .card-container {
      @media only screen and (max-width: 430px) {
        width: 90%;
      }
      .content-box {
        @media only screen and (max-width: 430px) {
          margin-top: 0;
        }
      }
    }
  }
  @media (max-width: 425px) {
    .header-container {
      padding: 20px 10px !important;
      height: 70px;
      img {
        width: 35px;
      }
    }
  }
`;

const OrderDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [orderDetail, setOrderDetail] = useState<any>();
  const [fetch, setFetch] = useState(false);
  const [isActiveAcceptModal, setIsActiveAcceptModal] = useState(false);
  const [dataItem, setDataItem] = useState<any>('');

  useEffect(() => {
    if (id) {
      apiOrder.getOrderById(id).then((res: any) => {
        if (res.status) {
          setOrderDetail(res.data);
          setFetch(false);
        }
      });
    }
  }, [fetch]);

  return (
    orderDetail && (
      <OrderDetailStyled>
        <div className="header-container">
          <Image
            priority
            className="w-[40px] h-[40px] cursor-pointer"
            src="/icons/back-icon.svg"
            onClick={() =>
              router.push(`/my-orders?status=${orderDetail?.status}`)
            }
            alt=""
            width={40}
            height={40}
          />
          <span className="subheader">{orderDetail.orderNumber}</span>
          <div className={'zone-empty'}></div>
        </div>
        <div className="content-container">
          <OrderDetailCustom
            data={orderDetail}
            setIsActiveAcceptModal={setIsActiveAcceptModal}
            setDataItem={setDataItem}
          />
          <AcceptProductModal
            open={isActiveAcceptModal}
            handleClose={setIsActiveAcceptModal}
            data={dataItem}
            setFetch={setFetch}
          />
        </div>
      </OrderDetailStyled>
    )
  );
};

export default OrderDetailPage;
