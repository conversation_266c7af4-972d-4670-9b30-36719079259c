import React, { useEffect, useState } from 'react';

import apiUser from '@/services/user';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/index';
import {
  getUserProfile,
  userSelector,
} from '@/store/reducers/backup/userSlice';
import { useRouter } from 'next/router';
import { Button } from '@/components/ui/button';
import TaxCardCustom from '@/components/customUI/TaxCardCustom';
import UserCardCustom from '@/components/customUI/UserCardCustom';
import { numberWithCommas } from '@/utils/numberFormat';
import Image from 'next/image';
import { OrdersHeaderStyle, OrdersStyle } from '@/styles/orders.styled';
import { AddressType } from '@/types/address';
import { useSearchParams } from 'next/navigation';
import apiRevision from '@/services/revision';
import CardRevisionOrdersCustom from '@/components/customUI/CardRevisionOrdersCustom';
import { useSelector } from 'react-redux';
import {
  getReQuotation,
  reQuotationSelector,
  setReQuotationShipping,
} from '@/store/reducers/backup/reQuotationSlice';
import { isEmpty, isNull } from 'lodash';
import { ReQuotationItemType } from '@/store/type/reQuotation';

const RevisionOrderPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isActiveModal, setIsActiveModal] = useState(false);
  const [isActiveAdd, setIsActiveAdd] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [dataAddress, setDataAddress] = useState<AddressType[]>([]);
  const [dataTax, setDataTax] = useState<any[]>([]);
  const [nextStep, setNextStep] = useState(1);
  const dispatch = useAppDispatch();
  const [editModeTax, setEditModeTax] = useState(false);
  const [editModeUser, setEditModeUser] = useState(false);
  const { id } = router.query;
  const searchParams = useSearchParams();
  const revisionId = searchParams.get('revisionId');
  const { user } = useSelector(userSelector);
  const [isActiveModalUser, setIsActiveModalUser] = useState(false);
  const [isActiveModalTax, setIsActiveModalTax] = useState(false);
  const [selectedTaxAction, setSelectedTaxAction] = useState({
    id: 0,
    name: null,
    phoneNumber: null,
    address: null,
    zipCode: null,
    province: null,
    district: null,
    subDistrict: null,
    isTax: false,
    taxPayerType: 0,
    taxId: null,
    email: null,
  });
  const [validateAddress, setValidateAddress] = useState(true);

  const [orderId, setOrderId] = useState();
  const { reQuotations } = useSelector(reQuotationSelector);

  const [selectedItem, setSelectedItem] = useState(1);

  const handleSubmitAddress = async (id: number, values: any) => {
    if (editMode && id) {
      setLoading(true);
      const payload = {
        name: `${values.firstname} ${values.lastname}`,
        phoneNumber: values.phoneNumber,
        email: values.email,
        address: values.address,
        zipCode: values.zipCode,
        province: values.province,
        district: values.district,
        subDistrict: values.subDistrict,
        isTax: false,
        taxPayerType: 0,
        taxId: '',
      };
      setLoading(true);
      const res = await apiUser.editAddress(id, payload);
      if (res && !res.isError) {
        toast.success(res.message);
        fetchData();
        setEditMode(false);
        setIsActiveAdd(false);
      } else {
        toast.error(res.error);
      }
      setLoading(false);
    } else {
      setLoading(true);
      const payload = {
        name: `${values.firstname} ${values.lastname}`,
        phoneNumber: values.phoneNumber,
        email: values.email,
        address: values.address,
        zipCode: values.zipCode,
        province: values.province,
        district: values.district,
        subDistrict: values.subDistrict,
        isTax: values.isTax,
        taxPayerType: 0,
        taxId: '',
      };
      setLoading(true);
      const res = await apiUser.createAddress(payload);
      if (res && !res.isError) {
        toast.success(res.message);
        fetchData();
        setOrderId(res.data);
        setIsActiveAdd(false);
      } else {
        toast.error(res.error);
      }
      setLoading(false);
    }
  };

  const handleSelectAddress = (address: AddressType) => {
    const mapData = {
      ...reQuotations,
      items: reQuotations.items.map((orderData: ReQuotationItemType) => {
        if (orderData.itemId === selectedItem) {
          return {
            ...orderData,
            name: address.name,
            recipientName: orderData.recipientName,
            phoneNumber: address.phoneNumber,
            address: address.address,
            district: address.district,
            subDistrict: address.subDistrict,
            province: address.province,
            zipCode: address.zipCode,
            status: 0,
          };
        }
        return {
          ...orderData,
        };
      }),
    };
    dispatch(setReQuotationShipping(mapData));
  };

  const fetchData = async () => {
    try {
      const resAddress = await apiUser.getAddress(false);
      if (resAddress.status) {
        setDataAddress(resAddress.data);
      }
      const resAddressTax = await apiUser.getAddress(true);
      if (resAddressTax.status) {
        setDataTax(resAddressTax.data);
      }
    } catch (error) {
      console.error('error', error);
    }
  };

  useEffect(() => {
    if (!reQuotations.step) {
      if (!isNull(revisionId)) {
        dispatch(getReQuotation(revisionId));
      }
    }
    fetchData();
  }, []);

  const handleSubmitUser = async (values: any) => {
    const formData = new FormData();
    formData.append('updateUserRequest', JSON.stringify(values));
    const res = await apiUser.updateProfile(formData);
    if (res && !res.isError) {
      toast.success(res.message);
      dispatch(getUserProfile());
      setLoading(false);
      setIsActiveModalUser(false);
    } else {
      setLoading(false);
      toast.error(res.error);
    }
  };

  const handleSubmitTax = async (values: any) => {
    let payload = {};
    if (parseInt(values.taxPayerType) === 1) {
      payload = {
        ...values,
        name: `${values.firstname} ${values.lastname}`,
        taxPayerType: parseInt(values.taxPayerType),
      };
    } else {
      payload = {
        ...values,
        name: values.taxPayerName,
        taxPayerType: parseInt(values.taxPayerType),
      };
    }
    if (editModeTax) {
      setLoading(true);
      const res = await apiUser.editAddress(selectedTaxAction.id, payload);
      if (res && !res.isError) {
        toast.success(res.message);
        fetchData();
        setIsActiveModalTax(false);
      } else {
        toast.error(res.error);
      }
      setLoading(false);
    } else {
      setLoading(true);
      const resEdit = await apiUser.createAddress(payload);
      if (resEdit && !resEdit.isError) {
        toast.success(resEdit.message);
        fetchData();
        setIsActiveModalTax(false);
      } else {
        toast.error(resEdit.error);
      }
      setLoading(false);
    }
  };

  const submitOrder = async () => {
    const payload = {
      revisionItems: reQuotations.items.map((newArr: ReQuotationItemType) => {
        return {
          revisionItemId: newArr.itemId,
          totalPrice: newArr.totalPrice,
          shipping: newArr.shipping,
          shippingCost: newArr.shippingCost,
          recipientName: newArr.recipientName,
          phoneNumber: newArr.phoneNumber,
          address: newArr.address,
          district: newArr.district,
          subDistrict: newArr.subDistrict,
          province: newArr.province,
          zipCode: newArr.zipCode,
        };
      }),
      isTax: selectedTaxAction.isTax,
      taxPayerType: selectedTaxAction.taxPayerType,
      taxId: selectedTaxAction.taxId,
      taxPayerName: selectedTaxAction.name,
      phoneNumber: selectedTaxAction.phoneNumber,
      address: selectedTaxAction.address,
      district: selectedTaxAction.district,
      subDistrict: selectedTaxAction.subDistrict,
      province: selectedTaxAction.province,
      zipCode: selectedTaxAction.zipCode,
      email: selectedTaxAction.email,
    };
    if (typeof id === 'string') {
      const response = await apiRevision.confirmRevision(id, payload);
      if (response.status) {
        setOrderId(response.data);
        setNextStep(3);
      }
    }
  };

  const handleSelectShipping = (itemId: number, shippingId: number) => {
    const mapData = {
      ...reQuotations,
      items: reQuotations.items.map((orderData: ReQuotationItemType) => {
        if (orderData.itemId === itemId) {
          return {
            ...orderData,
            shipping: shippingId,
            shippingCost: shippingId === 1 ? 0 : 500,
            totalPrice: orderData.totalPrice,
            recipientName: null,
            phoneNumber: null,
            address: null,
            district: null,
            subDistrict: null,
            province: null,
            zipCode: null,
            status: 0,
          };
        }
        return {
          ...orderData,
        };
      }),
    };
    const sumShippingCost = mapData.items.reduce((sum: number, obj: any) => {
      return sum + obj.shippingCost;
    }, 0);
    dispatch(
      setReQuotationShipping({ ...mapData, shippingCost: sumShippingCost }),
    );
  };

  function checkAddressAndShipping(items: ReQuotationItemType[]) {
    for (const item of items) {
      if (item.shipping === 2 && isNull(item.address)) {
        toast.error('กรุณาเลือกที่อยู่');
        setValidateAddress(false);
        return;
      }
      if (item.shipping !== 1 && item.shipping !== 2) {
        setValidateAddress(false);
        return;
      }
      setValidateAddress(true);
    }
  }

  useEffect(() => {
    if (!isEmpty(reQuotations.items)) {
      checkAddressAndShipping(reQuotations.items);
    }
  }, [reQuotations]);

  return (
    <OrdersStyle>
      {nextStep === 3 ? (
        <div className="success-container">
          <div className="success-wrapper">
            <Image
              priority
              className="flare absolute"
              src="/images/flare.svg"
              alt=""
              width={400}
              height={200}
            />
            <div className="bg-[#EBFEEB] m-4 p-4 rounded-full">
              <Image
                priority
                className=" w-[40px] h-[40px]"
                src="/icons/check.svg"
                alt=""
                width={40}
                height={40}
              />
            </div>
            <span className="subtitle">รายการสั่งซื้อ สำเร็จ!</span>
            <span className="subheader">{orderId}</span>
            <div>
              <span className="content">
                เราได้รับการสั่งซื้อของคุณแล้ว
                เราจะส่งข้อมูลใบเสนอราคาไปทางอีเมลของคุณ
              </span>
            </div>
            <span className="content">
              คุณสามารถตรวจสอบสถนาะของคุณได้ที่ “รอเสนอราคา”
            </span>
            <Button className="button" onClick={() => router.push('/')}>
              รายการรอเสนอราคา
            </Button>
          </div>
        </div>
      ) : (
        <OrdersHeaderStyle>
          {nextStep === 1 ? (
            <Image
              priority
              className="w-[40px] h-[40px] cursor-pointer"
              src="/icons/back-icon.svg"
              alt=""
              width={80}
              height={80}
              onClick={() => router.back()}
            />
          ) : (
            <Image
              priority
              onClick={() => setNextStep(1)}
              className="w-[40px] h-[40px] cursor-pointer"
              src="/icons/back-icon.svg"
              alt=""
              width={80}
              height={80}
            />
          )}
          <span className="subtitle">
            {nextStep === 1 ? 'รายการสั่งซื้อ' : 'ข้อมูลผู้สั่งซื้อ'}
          </span>
          <span className="subtitle">{nextStep}/2</span>
        </OrdersHeaderStyle>
      )}
      {nextStep === 1 && (
        <div className="card-container">
          {!isEmpty(reQuotations.items) &&
            reQuotations.items.map((data: any, index: number) => (
              <CardRevisionOrdersCustom
                editMode={editMode}
                validateAddress={validateAddress}
                data={data}
                dataAddress={dataAddress}
                loading={loading}
                key={index}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                handleSelectShipping={(id: number, shippingId: number) =>
                  handleSelectShipping(id, shippingId)
                }
                handleSelectAddress={(address: any) =>
                  handleSelectAddress(address)
                }
                handleSubmitAddress={(id: number, address: any) =>
                  handleSubmitAddress(id, address)
                }
                isActiveModal={isActiveModal}
                setIsActiveModal={setIsActiveModal}
                isActiveAdd={isActiveAdd}
                setIsActiveAdd={setIsActiveAdd}
                setEditMode={setEditMode}
              />
            ))}
          <div className="result-container">
            <div className="result-text-wrapper">
              <div className="w-1/2 flex flex-col">
                <span className="content-1">ราคาสินค้าทั้งหมด</span>
                <span className="content-1">ภาษีมูลค่าเพิ่ม 7%</span>
                <span className="content-1">ค่าจัดส่ง</span>
              </div>
              <div className="w-1/2 flex flex-col text-right">
                <span className="content-2">
                  {numberWithCommas(reQuotations.totalPrice)}
                </span>
                <span className="content-2">
                  {numberWithCommas(reQuotations.vat)}
                </span>
                <span className="content-2">
                  {numberWithCommas(reQuotations.shippingCost)}
                </span>
              </div>
            </div>
          </div>
          <div className="total-container">
            <span className="subtitle">ยอดชำระเงินทั้งหมด</span>
            <span className="subheader">
              {numberWithCommas(
                reQuotations.totalPrice +
                  reQuotations.vat +
                  reQuotations.shippingCost,
              )}
            </span>
          </div>
          <Button
            disabled={!validateAddress}
            className="confirm-button bg-primary-main hover:bg-primary-light"
            onClick={() => {
              if (validateAddress) {
                setNextStep(2);
              }
            }}
          >
            ดำเนินการต่อไป
          </Button>
        </div>
      )}
      {nextStep === 2 && (
        <div className="card-container">
          <UserCardCustom
            submit={handleSubmitUser}
            loading={loading}
            setEditMode={setEditModeUser}
            editMode={editModeUser}
            editData={user}
            isActive={isActiveModalUser}
            setIsActive={setIsActiveModalUser}
          />
          <TaxCardCustom
            submit={handleSubmitTax}
            loading={loading}
            setEditMode={setEditModeTax}
            editMode={editModeTax}
            editData={dataTax}
            isActive={isActiveModalTax}
            setIsActive={setIsActiveModalTax}
            setSelectedAction={setSelectedTaxAction}
          />
          <Button
            disabled={!(dataTax.length > 0) || !(user.phoneNumber.length > 0)}
            className="confirm-button bg-primary-main hover:bg-primary-light mt-6"
            onClick={submitOrder}
          >
            ยืนยันการสั่งซื้อ
          </Button>
        </div>
      )}
    </OrdersStyle>
  );
};

export default RevisionOrderPage;
