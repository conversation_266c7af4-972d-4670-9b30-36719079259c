import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useSearchParams } from 'next/navigation';
import apiRevision from '@/services/revision';
import styled from 'styled-components';
import { isEmpty, isUndefined } from 'lodash';
import CustomizeForm from '@/components/customUI/custom/CustomizeForm';
import { getProduct } from '@/store/reducers/backup/productSlice';
import { useAppDispatch } from '@/store/index';
import { toast } from 'sonner';

const CustomizeStyle = styled.div`
  display: flex;
  width: 100%;
  margin-top: 80px;
  padding: 60px 40px 0;
  @media only screen and (max-width: 430px) {
    flex-direction: column;
    padding: 30px 10px 0;
  }
  @media only screen and (max-width: 820px) {
    //padding: 16px 10px 0;
    margin-top: 56px;
  }
  .model-container {
    display: flex;
    width: 60%;
    margin-bottom: 40px;
    @media only screen and (max-width: 430px) {
      width: 100%;
      display: none;
    }
  }
  .customize-container {
    display: flex;
    width: 40%;
    margin-bottom: 40px;
    @media only screen and (max-width: 430px) {
      width: 100%;
    }
  }
`;

const customizeEditPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const revisionId = searchParams.get('revisionId');
  const { id } = router.query;
  const [reQuotationData, setReQuotationData] = useState(null);

  const submit = async (values: any) => {
    const payload = {
      modelId: values.modelId,
      width: values.width,
      length: values.length,
      height: values.height,
      amount: values.amount,
      unitPrice: values.amountPrice,
      materialId: values.materialId,
      printingId: values.printing,
      printingPrice: values.printingPrice,
      coatingId: values.coatingId,
      coatingPrice: values.coatingPrice,
      productDemoId: values.productDemo,
      productDemoPrice: values.productDemoPrice,
      isArtwork: values.isArtwork === 2,
      artworkUrl: values.artworkUrl,
      artworkPrice: values.artworkPrice,
      description: values.description,
      specialTechnic: !isEmpty(values.specialTechnic)
        ? values.specialTechnic.map((data: any) => {
            return {
              id: data.id,
              width: data.width,
              height: data.height,
              price: data.price,
            };
          })
        : [],
    };

    const res = await apiRevision.editRevision(id, payload);
    if (res && !res.isError) {
      toast.success(res.message);
      router.back();
    } else {
      toast.error(res.message);
    }
  };

  const getItemById = async (itemId: number) => {
    const res = await apiRevision.getRevisionItemById(itemId);
    if (!res.isError) {
      await dispatch(getProduct());
      return setReQuotationData(res.data);
    }
  };

  useEffect(() => {
    if (!isUndefined(revisionId) && !isUndefined(id)) {
      getItemById(Number(id));
    }
  }, [revisionId]);

  return (
    <CustomizeStyle>
      <div className="model-container  ">
        <h1>Model</h1>
      </div>
      <div className="customize-container">
        <div className="flex flex-col w-full">
          {!isUndefined(reQuotationData) && (
            <CustomizeForm
              editMode={'re-quotation'}
              editData={reQuotationData}
              submit={submit}
            />
          )}
        </div>
      </div>
    </CustomizeStyle>
  );
};

export default customizeEditPage;
