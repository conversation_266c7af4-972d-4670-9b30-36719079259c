import React, { Fragment, useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/numberFormat';
import { Button } from '@/components/ui/button';

import { toast } from 'sonner';
import { useRouter } from 'next/router';
import { useAppDispatch } from '@/store/index';
import { getCart } from '@/store/reducers/backup/cartSlice';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  getReQuotation,
  reQuotationSelector,
} from '@/store/reducers/backup/reQuotationSlice';

import CardReQuotationCustom from '@/components/customUI/reQuotation/CardReQuotationCustom';
import { useSearchParams } from 'next/navigation';
import apiRevision from '@/services/revision';
import { useSelector } from 'react-redux';
import { isEmpty, isNull, isUndefined } from 'lodash';
import { ReQuotationItemType } from '@/store/type/reQuotation';

const CartStyle = styled.div`
  width: 100%;
  padding-top: 80px;
  height: max-content;
  @media only screen and (max-width: 430px) {
    padding-top: 56px;
  }
  .head-cart {
    display: flex;
    padding: 24px;
    border-bottom: 1px solid #dbe2e5;
    justify-content: center;
    align-items: center;
    @media only screen and (max-width: 430px) {
      padding: 24px 0;
      border: 0;
    }
    .subtitle {
      margin-left: 16px;
      font-size: 20px;
      font-weight: 700;
    }
  }
  .card-container {
    width: 70%;
    margin: auto;
    margin-top: 54px;
    @media only screen and (max-width: 430px) {
      width: 100%;
      margin-top: 0;
      padding: 0 5%;
      height: calc(100vh - 280px);
      overflow-y: scroll;
    }
    .card-box {
      display: flex;
      margin: 24px 0;
      position: relative;
      flex-direction: column;
      @media only screen and (max-width: 430px) {
        margin-top: 0;
      }
      .header-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #dbe2e5;
        .action-wrapper {
          display: flex;
          .delete {
            margin-left: 8px;
            cursor: pointer;
            width: 24px;
            height: 24px;
          }
          .edit {
            width: 24px;
            height: 24px;
            cursor: pointer;
          }
        }
        .text-wrapper {
          display: flex;
          flex-direction: column;
          .subtitle {
            font-size: 20px;
            font-weight: 700;
          }
        }
      }
      .content-wrapper {
        display: flex;
        border-bottom: 1px solid #dbe2e5;
        padding: 16px;
        .text-container {
          display: flex;
          flex-direction: column;
          margin-left: 24px;
          justify-content: space-between;
          .text-wrapper {
            display: flex;
            flex-direction: column;
            .subtitle {
              font-size: 14px;
              font-weight: 400;
            }
          }
          .price-container {
            .price {
              font-size: 14px;
              font-weight: 700;
            }
            .unit {
              margin-left: 8px;

              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
      .footer-wrapper {
        .content {
          font-size: 14px;
          font-weight: 400;
        }
        .show-more-container {
          .show-more-wrapper {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #dbe2e5;
            padding: 16px;
            .special-wrapper {
              display: flex;
              flex-direction: column;
              text-align: right;
              .special-text {
                font-size: 14px;
                font-weight: 700;
              }
            }
            .head-text {
              font-size: 14px;
              font-weight: 400;
            }
            .content-text {
              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
    }
  }
  .result-container {
    background-color: #212121f2;
    display: flex;
    justify-content: space-between;
    width: 70%;
    margin: 24px auto;
    padding: 24px;
    border-radius: 16px;
    @media only screen and (max-width: 430px) {
      background-color: #212121;
      width: 100%;
      flex-direction: column;
      position: fixed;
      bottom: 0;
      margin: 0;
      height: 144px;
      border-radius: 0;
    }
    .result-text-wrapper {
      @media only screen and (max-width: 430px) {
        margin-bottom: 16px;
      }
      .result-wrapper {
        span {
          color: white;
          font-size: 20px;
          font-weight: 700;
        }
        .unit {
          margin-left: 8px;
        }
      }
      .content {
        font-size: 14px;
        font-weight: 400;
        color: white;
      }
    }
    .confirm-button {
      width: fit-content;
      height: 48px;
      font-size: 20px;
      font-weight: 700;
      @media only screen and (max-width: 430px) {
        width: 100%;
      }
    }
  }
`;

const ReQuotationPage = () => {
  const [result, setResult] = useState(0);
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const { reQuotations } = useSelector(reQuotationSelector);
  const [isActiveDeleteModal, setIsActiveDeleteModal] = useState(false);
  const id = searchParams.get('revisionId');
  const orderNumber = searchParams.get('orderNumber');
  const [selectedItem, setSelectedItem] = useState<ReQuotationItemType>();

  const calPrice = () => {
    if (!isEmpty(reQuotations.items)) {
      const result = reQuotations.items.reduce(function (
        sum: number,
        obj: any,
      ) {
        return sum + obj.totalPrice;
      }, 0);
      setResult(result);
    }
  };

  const handleDeleteCartItem = async (id: number) => {
    const resDelete = await apiRevision.deleteRevision(id);
    if (resDelete.status) {
      toast.success(resDelete.message);
      setIsActiveDeleteModal(false);
      dispatch(getCart());
    } else {
      toast.error(resDelete.error);
    }
  };

  const handleCancelRevision = async (id: string) => {
    const resDelete = await apiRevision.cancelRevision(id);
    if (resDelete.status) {
      toast.success(resDelete.message);
      setIsActiveDeleteModal(false);
      dispatch(getCart());
    } else {
      toast.error(resDelete.error);
    }
  };

  useEffect(() => {
    if (!reQuotations.step) {
      if (!isNull(id)) {
        dispatch(getReQuotation(id));
      }
    }
  }, []);

  useEffect(() => {
    calPrice();
  }, [reQuotations]);

  return (
    <CartStyle>
      <div className="head-cart">
        <span className="subtitle">เสนอราคาอีกครั้ง</span>
      </div>
      {!isNull(selectedItem) && !isUndefined(selectedItem) && (
        <Dialog
          open={isActiveDeleteModal}
          onOpenChange={(value: any) => setIsActiveDeleteModal(value)}
        >
          <DialogContent className="modal-cancel-order max-w-[600px]">
            <DialogHeader className="border-b-[1px] flex">
              <DialogTitle className="text-center p-4">
                ยืนยันลบรายการโมเดล
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col justify-center items-center text-center p-6 pb-0">
              <div className="bg-[#FFECEC] rounded-full w-fit p-[30px]">
                <Image
                  className="rounded-xl"
                  priority
                  src="/icons/delete.svg"
                  alt=""
                  width={40}
                  height={40}
                />
              </div>
              <div className="text-wrapper mt-[32px] flex flex-col">
                {!isEmpty(reQuotations.items) ? (
                  <Fragment>
                    <span className="subtitle-bold"></span>
                    <span className="content">คุณยืนยันลบรายการโมเดล</span>
                    <span className="content-bold">
                      {selectedItem.model.name}
                      <span className="content"> ใช่หรือไม่</span>
                    </span>
                  </Fragment>
                ) : (
                  <Fragment>
                    <span className="subtitle-bold">
                      รายการโมเดลที่คุณเลือกเป็นรายการโมเดลสุดท้ายในรายการสั่งซื้อ
                    </span>
                    <span className="content">
                      หากคุณต้องการลบ จะเป็นการยกเลิกรายการสั่งซื้อนี้
                    </span>
                    <span className="content">
                      คุณยืนยันลบรายการสั่งซื้อสินค้าหมายเลข
                      <span className="content-bold">
                        {' '}
                        {orderNumber}{' '}
                        <span className="content">ใช่หรือไม่</span>
                      </span>
                    </span>
                  </Fragment>
                )}
              </div>
            </div>
            <DialogFooter className="flex flex-row p-6">
              <DialogClose asChild>
                <Button
                  type={'button'}
                  className="w-1/2 bg-white text-black border-[1px] border-black me-2 hover:bg-white hover:text-black"
                  onClick={() => setIsActiveDeleteModal(false)}
                >
                  ไม่, ยังไม่ใช่ตอนนี้
                </Button>
              </DialogClose>
              <Button
                type={'submit'}
                onClick={() =>
                  !isEmpty(reQuotations.items)
                    ? handleDeleteCartItem(selectedItem.id)
                    : !isNull(id) && handleCancelRevision(id)
                }
                className="w-1/2 ml-2"
              >
                ยืนยันลบ
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <div className="card-container">
        <CardReQuotationCustom
          cartData={reQuotations.items}
          setIsActiveDeleteModal={setIsActiveDeleteModal}
          setSelectedItem={setSelectedItem}
        />
      </div>
      <div className="result-container">
        <div className="result-text-wrapper">
          <div className="result-wrapper">
            <span className="subtitle">{numberWithCommas(result)}</span>
            <span className="unit">บาท</span>
          </div>
          <span className="content">
            (ยังไม่รวมค่าจัดส่ง และภาษีมูลค่าเพิ่ม 7% )
          </span>
        </div>
        <Button
          disabled={isEmpty(reQuotations.items)}
          className="confirm-button bg-primary-main hover:bg-primary-light"
          onClick={() => router.push(`/my-orders/re-quotation/order/${id}`)}
        >
          ยืนยันการแก้ไขคำสั่งซื้อ
        </Button>
      </div>
    </CartStyle>
  );
};

export default ReQuotationPage;
