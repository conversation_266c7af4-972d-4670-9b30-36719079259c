import React from 'react';
import styled from 'styled-components';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/router';

const WelComePageStyled = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;

    .text-group {
      z-index: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .top-section-content {
        display: flex;
        padding: 8px 16px 8px 8px;
        justify-content: center;
        align-items: center;
        gap: 16px;
        border-radius: 24px;
        border: 1px solid #685b7e;
        background: rgba(66, 66, 66, 0.2);
        backdrop-filter: blur(calc(20px / 2));
        margin-bottom: 32px;

        .first-content {
          display: flex;
          height: 32px;
          padding: 8px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 28px;
          background: #ff4f00;
          color: #fff;
          text-align: center;
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
        }

        .last-content {
          color: #fff;
          text-align: center;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .text-welcome-element {
        display: flex;
        flex-direction: column;
        gap: 48px;
        margin-bottom: 56px;

        h1 {
          color: #fff;
          text-align: center;
          font-size: 120px;
          font-style: normal;
          font-weight: 700;
          line-height: 100px;
        }

        h6 {
          color: #fff;
          text-align: center;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .button-element {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-bottom: 24px;

        button {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 8px;
          height: 48px;
          max-width: 264px;
          width: 100%;
          padding: 8px 24px;
          align-self: stretch;
          border-radius: 8px;
          background: #ff4f00;
        }
      }
      .text-bottom {
        color: #fff;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-decoration-line: underline;

        &:hover {
          cursor: pointer;
        }
      }

      @media screen and (max-width: 830px) {
        .top-section-content {
          .first-content {
            font-size: 14px;
            height: 24px;
            padding: 8px 16px;
          }

          .last-content {
            font-size: 14px;
          }
        }

        .text-welcome-element {
          h1 {
            font-size: 98px;
            line-height: 80px;
          }

          h6 {
            font-size: 16px;
          }
        }

        .button-element {
          button {
            max-width: 200px;
          }
        }
      }
      @media screen and (max-width: 768px) {
        .top-section-content {
          .first-content {
            font-size: 13px;
            height: 24px;
            padding: 8px 16px;
          }

          .last-content {
            font-size: 13px;
          }
        }

        .text-welcome-element {
          h1 {
            font-size: 86px;
            line-height: 80px;
          }

          h6 {
            padding: 0 150px;
            font-size: 14px;
          }
        }

        .button-element {
          button {
            max-width: 200px;
          }
        }
      }
      @media screen and (max-width: 650px) {
        .top-section-content {
          .first-content {
            font-size: 12px;
            height: 24px;
            padding: 8px 16px;
          }

          .last-content {
            font-size: 12px;
          }
        }

        .text-welcome-element {
          h1 {
            font-size: 78px;
            line-height: 80px;
          }

          h6 {
            padding: 0 130px;
            font-size: 12px;
          }
        }

        .button-element {
          button {
            font-size: 14px;
            padding: 16px;
            height: 40px;
            width: 200px;
          }
        }
        .text-bottom {
          font-size: 12px;
        }
      }
      @media screen and (max-width: 530px) {
        .top-section-content {
          .first-content {
            font-size: 12px;
            height: 20px;
            padding: 8px 16px;
          }

          .last-content {
            font-size: 12px;
          }
        }

        .text-welcome-element {
          h1 {
            font-size: 68px;
            line-height: 60px;
          }

          h6 {
            padding: 0 100px;
            font-size: 12px;
          }
        }

        .button-element {
          button {
            font-size: 14px;
            padding: 16px;
            height: 40px;
            width: 200px;
          }
        }
        .text-bottom {
          font-size: 12px;
        }
      }
      @media screen and (max-width: 436px) {
        .top-section-content {
          .first-content {
            font-size: 9px;
            height: 16px;
            padding: 8px 8px;
          }

          .last-content {
            font-size: 9px;
          }
        }

        .text-welcome-element {
          h1 {
            font-size: 56px;
            line-height: 40px;
          }

          h6 {
            padding: 0 80px;
            font-size: 11px;
          }
        }

        .button-element {
          button {
            font-size: 14px;
            padding: 16px;
            height: 40px;
            width: 200px;
          }
        }
        .text-bottom {
          font-size: 11px;
        }
      }
    }
  }
`;

const WelcomePage = () => {
  const { push } = useRouter();
  return (
    <WelComePageStyled>
      <div className="header-section">
        <div className="text-group">
          <div className="top-section-content">
            <p className="first-content">👋🏻 Hello!</p>
            <p className="last-content">Ready to join with us ?</p>
          </div>
          <div className="text-welcome-element">
            <h1>Welcome to</h1>
            <h1>Digiboxs</h1>
            <h6>
              เรามีบรรจุภัณฑ์พร้อมผลิตมากกว่า 100+{' '}
              รายการเพื่อรองรับผลิตภัณฑ์ของคุณ
            </h6>
          </div>
          <div className="button-element">
            <Button onClick={() => push('/customize')}>สั่งผลิต</Button>
          </div>
          <p className="text-bottom" onClick={() => push('/')}>
            ไว้ภายหลัง
          </p>
        </div>
      </div>
    </WelComePageStyled>
  );
};

export default WelcomePage;
