import React from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import Image from 'next/image';

const ContactStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 140px;
  @media only screen and (max-width: 430px) {
    padding-top: 80px;
  }
  span {
    color: white;
  }
  .contact-text-footer {
    @media only screen and (max-width: 430px) {
      display: block;
      width: 60%;
      word-break: break-word;
      padding-bottom: 24px;
    }
  }
`;

const ContactPage = () => {
  return (
    <ContactStyle>
      <div className="flex flex-col items-center">
        <span className="text-xl !text-primary-main ">ติดต่อสอบถาม</span>
        <span className="text-[36px] tablet:text-[120px] my-6 h-[80px] font-bold leading-[80px]">
          CONTACT
        </span>
        <span className="text-sm">คุณสามารถติดต่อเราที่</span>
      </div>

      <div className="mt-[64px] w-[90%] tablet:w-[680px] rounded-[24px] bg-[#********] border-[1px] border-[#685B7E] p-[40px] flex flex-col items-center">
        <span>Add Line</span>
        <span>(Official Account)</span>
        <Image
          priority
          className="m-[10px] mt-8"
          src="/images/qr-line.png"
          alt=""
          width={180}
          height={180}
        />
        <div className="flex mt-8">
          <Image
            priority
            className="bg-gray-800 p-2 rounded-full cursor-pointer"
            src="/icons/share.png"
            alt=""
            width={40}
            height={40}
          />
          <Image
            priority
            className="bg-gray-800 p-2 rounded-full mx-3.5 cursor-pointer"
            src="/icons/download.png"
            alt=""
            width={40}
            height={40}
          />
          <Image
            priority
            className="bg-gray-800 p-2 rounded-full cursor-pointer"
            src="/icons/link.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
      </div>
      <div className="flex flex-col justify-center items-center my-[48px]">
        <span className="text-xl font-bold">Luca block Co., Ltd.</span>
        <span className="text-sm font-thin">
          6 ซอยบางแค 12 บางแค บางแค กรุงเทพมหานคร 10160, ประเทศไทย
        </span>
      </div>
      <div
        className="mb-[32px] h-[64px] w-full flex flex-col tablet:flex-row justify-center items-center text-center
       text-white font-thin border-gray-700 border-y-[1px]"
      >
        <span className="text-sm">Call Center +66 657127411</span>
        <span className="hidden tablet:block mx-8">•</span>
        <span className="text-m">Email <EMAIL></span>
      </div>
      <div className="flex justify-center mb-8">
        <div className="w-10 h-10 ">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full cursor-pointer"
            src="/icons/facebook-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 mx-[48px]">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full cursor-pointer"
            src="/icons/youtube-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 mr-[40px]">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full cursor-pointer"
            src="/icons/line-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
        <div className="w-10 h-10 ">
          <Image
            priority
            className="bg-primary-main p-2 rounded-full cursor-pointer"
            src="/icons/tiktok-white.png"
            alt=""
            width={40}
            height={40}
          />
        </div>
      </div>
      <div className="contact-text-footer flex justify-center w-1/2 tablet:w-full">
        <label className="text-xs text-[#685B7E]">
          © 2024 © 2023 Digiboxs. All rights reserved.
        </label>
        <label className="text-xs text-[#685B7E] mx-2 hidden tablet:block">
          •
        </label>
        <label className="text-xs text-[#685B7E]">Cookie</label>
        <label className="text-xs text-[#685B7E] mx-2 hidden tablet:block">
          •
        </label>
        <Link className="text-xs text-[#685B7E]" href={'/policy'}>
          Privacy Policy
        </Link>
      </div>
    </ContactStyle>
  );
};

export default ContactPage;
