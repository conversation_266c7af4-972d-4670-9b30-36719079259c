import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import FormAccountCustom from '@/components/customUI/account/FormAccountCustom';
import FormPasswordCustom from '@/components/customUI/account/FormPasswordCustom';
import styled from 'styled-components';
import apiUser from '@/services/user';
import { useRouter } from 'next/router';
import { toast } from 'sonner';
import { deleteCookie } from 'cookies-next';
import { useAppDispatch } from '@/store/index';
import { getUserProfile, resetUser } from '@/store/reducers/backup/userSlice';

const EditAccountPageStyle = styled.div`
  margin-top: 64px;
  width: 100%;
  display: flex;
  justify-content: center;
  @media only screen and (max-width: 430px) {
  }
  .tab-wrapper {
    width: 33.33%;
    @media only screen and (max-width: 430px) {
      width: 90%;
    }
  }
`;

const EditAccountPage = () => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const handleSubmitAccount = async (values: any) => {
    const formData = new FormData();
    const payload = {
      firstname: values.firstname,
      lastname: values.lastname,
      phoneNumber: values.phoneNumber,
      email: values.email,
    };
    formData.append('updateUserRequest', JSON.stringify(payload));
    formData.append('file', values.image);
    const res = await apiUser.updateProfile(formData);
    if (res && !res.isError) {
      toast.success(res.message);
      dispatch(getUserProfile());
      setLoading(false);
      router.push('/my-account');
    } else {
      setLoading(false);
      toast.error(res.error);
    }
  };

  const handleSubmitPassword = async (values: any) => {
    setLoading(true);
    const payload = {
      oldPassword: values.oldPassword,
      newPassword: values.password,
    };
    const res = await apiUser.updatePassword(payload);
    if (res.status) {
      setLoading(false);
      deleteCookie('access_token');
      toast.success(res.message);
      dispatch(resetUser());
      setTimeout(() => {
        router.push('/login');
      }, 1000);
    } else {
      setLoading(false);
      toast.error(res.error);
    }
  };

  return (
    <EditAccountPageStyle className="">
      <Tabs defaultValue="account" className="tab-wrapper">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="account">แก้ไขโปรโฟล์</TabsTrigger>
          <TabsTrigger value="password">เปลี่ยนรหัสผ่าน</TabsTrigger>
        </TabsList>
        <TabsContent value="account">
          <div className="w-full h-full">
            <FormAccountCustom submit={handleSubmitAccount} loading={loading} />
          </div>
        </TabsContent>
        <TabsContent value="password">
          <div>
            <FormPasswordCustom
              submit={handleSubmitPassword}
              loading={loading}
            />
          </div>
        </TabsContent>
      </Tabs>
    </EditAccountPageStyle>
  );
};

export default EditAccountPage;
