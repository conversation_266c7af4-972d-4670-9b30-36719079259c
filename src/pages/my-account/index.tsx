import React, { Fragment, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useAppDispatch } from '@/store/index';
import {
  getUserProfile,
  userSelector,
} from '@/store/reducers/backup/userSlice';
import CardAddressAccountCustom from '@/components/customUI/account/CardAddressAccountCustom';
import { useRouter } from 'next/router';
import { Card } from '@/components/ui/card';
import FormModal from '@/components/customUI/account/FormModal';
import apiUser from '@/services/user';
import { toast } from 'sonner';
import { useSelector } from 'react-redux';
import { isEmpty } from 'lodash';
import { UserShippingAddressType, UserTaxAddressType } from '@/store/type/user';

const MyAccountPageStyle = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 80px;
  width: 100%;
  @media only screen and (max-width: 430px) {
    margin-top: 56px;
  }
  .edit-container {
    width: 33.33%;
    margin-top: 32px;
    @media only screen and (max-width: 430px) {
      width: 90%;
    }
    .text-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .subtitle {
        font-size: 20px;
        font-weight: 700;
      }
      .caption {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
      }
    }
    .address-empty {
      //margin: 40px auto;
      background-color: #f5f5f5;
      border: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px 0;
    }
    .edit-account-button {
      width: 100%;
      background-color: white;
      color: black;
      border: 1px solid #dbe2e5;
      &:hover {
        background-color: white;
      }
    }
  }
`;

const MyAccountPage = () => {
  const { user } = useSelector(userSelector);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [isActiveForm, setIsActiveForm] = useState(false);

  const handleSubmitAddress = async (isTax: boolean, values: any) => {
    const payload = isTax
      ? {
          name:
            values.taxPayerType === '1'
              ? `${values.firstname} ${values.lastname}`
              : values.taxPayerName,
          phoneNumber: values.phoneNumber,
          address: values.address,
          zipCode: values.zipCode,
          province: values.province,
          district: values.district,
          subDistrict: values.subDistrict,
          isTax: true,
          taxPayerType: parseInt(values.taxPayerType),
          taxId: values.taxId,
          email: values.email,
        }
      : {
          name: `${values.firstname} ${values.lastname}`,
          phoneNumber: values.phoneNumber,
          address: values.address,
          zipCode: values.zipCode,
          province: values.province,
          district: values.district,
          subDistrict: values.subDistrict,
          isTax: false,
          taxPayerType: 0,
          taxId: null,
          email: values.email,
        };
    const res = await apiUser.createAddress(payload);
    if (res && !res.isError) {
      setIsActiveForm(false);
      dispatch(getUserProfile());
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  useEffect(() => {
    dispatch(getUserProfile());
  }, []);

  return (
    <MyAccountPageStyle>
      <div className="edit-container">
        <div className="w-full flex mb-[32px]">
          {user.imageUrl ? (
            <div
              className={`w-[80px] h-[80px] rounded-full content-center mr-4`}
            >
              <Image
                className="w-full h-full object-cover rounded-full shadow-md"
                src={user.imageUrl}
                alt="Selected"
                width={80}
                height={80}
              />
            </div>
          ) : (
            <Image
              className="mr-4"
              priority
              src="/images/default-profile.svg"
              alt=""
              width={80}
              height={80}
            />
          )}
          <div className="text-wrapper">
            <span className="subtitle">{user.name}</span>
            <span className="caption">
              <Image
                className="mr-2"
                priority
                src="/icons/mail.svg"
                alt=""
                width={16}
                height={16}
              />
              {user.email}
            </span>
            <span className="caption">
              <Image
                className="mr-2"
                priority
                src="/icons/call.svg"
                alt=""
                width={16}
                height={16}
              />
              {user.phoneNumber || '-'}
            </span>
          </div>
        </div>
        <div className="w-full">
          <Button
            className="edit-account-button"
            onClick={() => router.push('/my-account/edit-account')}
          >
            แก้ไขโปรไฟล์
          </Button>
        </div>
        <Tabs defaultValue="address" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="address">ข้อมูลการจัดส่ง</TabsTrigger>
            <TabsTrigger value="tax">ข้อมูลใบกำกับภาษี</TabsTrigger>
          </TabsList>
          <TabsContent value="address" className="mt-6">
            <div className="address-wrapper w-full h-full">
              {!isEmpty(user.shippingAddress) && (
                <Fragment>
                  <Button
                    className="w-full mb-6"
                    onClick={() => {
                      setIsActiveForm(true);
                    }}
                  >
                    <Image
                      priority
                      className=""
                      src="/icons/add-white.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    เพิ่มที่อยู่จัดส่ง
                  </Button>
                  <FormModal
                    handleClose={setIsActiveForm}
                    open={isActiveForm}
                    handleSubmitAddress={(value: any) =>
                      handleSubmitAddress(false, value)
                    }
                    editMode={false}
                    loading={false}
                  />
                </Fragment>
              )}
              {!isEmpty(user.shippingAddress) ? (
                user.shippingAddress.map(
                  (address: UserShippingAddressType, index: number) => (
                    <CardAddressAccountCustom key={index} data={address} />
                  ),
                )
              ) : (
                <Card className="address-empty">
                  <Image
                    priority
                    className="mb-2"
                    src="/icons/local_shipping-gray.svg"
                    alt=""
                    width={40}
                    height={40}
                  />
                  <div className="text-[#BDBDBD] text-sm">
                    คุณยังไม่มีข้อมูลผู้สั่งซื้อเพิ่มข้อมูลการจัดส่ง
                  </div>
                  <Button
                    className="w-fit mt-[32px]"
                    onClick={() => {
                      setIsActiveForm(true);
                    }}
                  >
                    <Image
                      priority
                      src="/icons/add-white.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    เพิ่มที่อยู่จัดส่ง
                  </Button>
                  <FormModal
                    handleClose={setIsActiveForm}
                    open={isActiveForm}
                    handleSubmitAddress={(value: any) =>
                      handleSubmitAddress(false, value)
                    }
                    editMode={false}
                    loading={false}
                  />
                </Card>
              )}
            </div>
          </TabsContent>
          <TabsContent value="tax" className="mt-6">
            <div className="tax-wrapper w-full h-full">
              {!isEmpty(user.taxAddress) && (
                <Fragment>
                  <Button
                    className="w-full mb-6"
                    onClick={() => setIsActiveForm(true)}
                  >
                    <Image
                      priority
                      className=""
                      src="/icons/add-white.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    เพิ่มที่อยู่ใบกำกับภาษี
                  </Button>
                  <FormModal
                    handleClose={setIsActiveForm}
                    open={isActiveForm}
                    handleSubmitAddress={(value: any) =>
                      handleSubmitAddress(true, value)
                    }
                    isTax={true}
                    editMode={false}
                    loading={false}
                  />
                </Fragment>
              )}
              {!isEmpty(user.taxAddress) ? (
                user.taxAddress.map(
                  (tax: UserTaxAddressType, index: number) => (
                    <CardAddressAccountCustom key={index} data={tax} />
                  ),
                )
              ) : (
                <Card className="address-empty">
                  <Image
                    priority
                    className="mb-2"
                    src="/icons/contract-gray.svg"
                    alt=""
                    width={40}
                    height={40}
                  />
                  <div className="text-[#BDBDBD] text-sm">
                    คุณยังไม่มีข้อมูลสำหรับขอใบกำกับภาษี
                  </div>
                  <Button
                    className="w-fit mt-[32px]"
                    onClick={() => {
                      setIsActiveForm(true);
                    }}
                  >
                    <Image
                      priority
                      className=""
                      src="/icons/add-white.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                    เพิ่มที่อยู่ใบกำกับภาษี
                  </Button>
                  <FormModal
                    handleClose={setIsActiveForm}
                    open={isActiveForm}
                    handleSubmitAddress={(value: any) =>
                      handleSubmitAddress(true, value)
                    }
                    isTax={true}
                    editMode={false}
                    loading={false}
                  />
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MyAccountPageStyle>
  );
};

export default MyAccountPage;
