import React, { Fragment, useEffect } from 'react';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { getCookie } from 'cookies-next';
import HeaderAccount from '@/components/HeaderAccount';
import Navbar from '@/components/common/Navbar';
import Footer from '@/components/common/Footer';

const ContentFullHStyle = styled.main`
  display: flex;
  justify-content: center;
  height: auto;
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  @media only screen and (max-width: 430px) {
    height: auto !important;
    padding-top: 0;
  }
`;

const ContentStyle = styled.main`
  display: flex;
  justify-content: center;
  height: 100%;
  //padding: 0 40px;
  background-size: cover;
  @media only screen and (max-width: 430px) {
    height: auto !important;
    //padding: 0 16px 0;
  }
`;

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const fullSizeAllowList = [
    '/',
    '/login',
    '/welcome',
    '/contact',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/new-password',
    '/verify-email',
    '/oauth2/redirect',
    '/cart',
    '/orders',
    '/claims-order',
    '/claims-order/shipping-detail/[id]',
    '/payment/[id]',
    '/my-orders',
    '/my-orders/order-detail/[id]',
    '/my-orders/order-detail/shipping-detail/[id]',
    '/my-account',
    '/my-account/edit-account',
    '/my-orders/re-quotation',
    '/my-orders/re-quotation/order/[id]',
    '/my-coupon',
    '/my-coupon/history',
    '/coupon',
  ];
  const footerAllowList = ['/', '/cart', '/policy'];
  const authAllowList = [
    '/',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/new-password',
    '/verify-email',
    '/oauth2/redirect',
  ];

  // const [showCookiePopup, setShowCookiePopup] = useState(false);

  useEffect(() => {
    checkPolicyCookie();
  }, []); // Only runs on mount

  const checkPolicyCookie = () => {
    const digiboxsPolicyCookie = getCookie('cookies-accept');
    if (!digiboxsPolicyCookie) {
      // setShowCookiePopup(true);
    }
  };

  // const allowPolicy = () => {
  //   setCookie('cookies-accept', true);
  //   setShowCookiePopup(false);
  // };
  //
  // const handleClose = () => {
  //   setShowCookiePopup(false);
  // };

  return (
    <Fragment>
      {router.pathname === '/my-account/edit-account' ? (
        <HeaderAccount />
      ) : (
        // <Header />
        <Navbar />
      )}
      {fullSizeAllowList.includes(router.pathname) ? (
        <ContentFullHStyle
          className={`
          ${authAllowList.includes(router.pathname) ? 'bg-bg h-auto' : 'h-full pt-0'} 
          ${router.pathname === '/' ? 'h-max' : ''} 
           ${router.pathname === '/welcome' ? 'bg-bg-welcome bg-contain bg-no-repeat bg-center w-full h-full flex flex-col justify-center' : ''}
          ${router.pathname === '/contact' ? 'h-full bg-bg-contact' : ''}
          `}
        >
          {children}
        </ContentFullHStyle>
      ) : (
        <ContentStyle
          className={`bg-background-paper ${footerAllowList.includes(router.pathname) ? '' : '!h-auto'}`}
        >
          {children}
        </ContentStyle>
      )}
      {footerAllowList.includes(router.pathname) && <Footer />}
      {/* {showCookiePopup && ( */}
      {/*  <CookiesCard handleClose={handleClose} handleCookies={allowPolicy} /> */}
      {/* )} */}
    </Fragment>
  );
};

export default MainLayout;
