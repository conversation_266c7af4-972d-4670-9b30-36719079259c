import { NextRequest, NextResponse } from 'next/server';

export function middleware(req: NextRequest) {
  const cookieString = req.headers.get('cookie');
  // const { pathname } = req.nextUrl;

  if (cookieString && cookieString?.search('access_token') >= 0) {
    const response = NextResponse.next();
    response.headers.set('Cache-Control', 'no-cache');
    return response;
  }

  // if (pathname === '/customize') {
  //   const params = req.nextUrl.searchParams.get('slug');
  //   if (params !== undefined && params !== null) {
  //     const response = NextResponse.redirect(new URL('/login', req.url));
  //     response.headers.set('Cache-Control', 'no-cache');
  //     response.cookies.set('customize', params);
  //     return response;
  //   }
  // }
  //
  // const response = NextResponse.redirect(new URL('/login', req.url));
  // response.headers.set('Cache-Control', 'no-cache');
  // return response;
}

export const config = {
  matcher: [
    '/',
    '/((?!api|_next/static|_next|background|images|icons|login|register|verify-email|forgot-password|reset-password|redirect|policy|contact|favicon.ico|oauth2).*)',
  ],
};
