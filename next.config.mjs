import { withNextVideo } from 'next-video/process';

/** @type {import('next').NextConfig} */
const nextConfig = {
  async redirects() {
    return [
      {
        source: '/',
        destination: process.env.NEXT_PUBLIC_LINK_URL,
        permanent: false,
        basePath: false,
      },
    ];
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    return config;
  },
  skipTrailingSlashRedirect: true,
  reactStrictMode: false,
  publicRuntimeConfig: {
    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    API_ENDPOINT: process.env.NEXT_PUBLIC_API_ENDPOINT,
    OAUTH_ENDPOINT: process.env.NEXT_PUBLIC_OAUTH_ENDPOINT,
    OAUTH_REDIRECT_URL: process.env.NEXT_PUBLIC_OAUTH_REDIRECT_URL,
    LINK_REDIRECT_URL: process.env.NEXT_PUBLIC_LINK_URL,
  },
  serverRuntimeConfig: {
    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    API_ENDPOINT: process.env.NEXT_PUBLIC_API_ENDPOINT,
    OAUTH_ENDPOINT: process.env.NEXT_PUBLIC_OAUTH_ENDPOINT,
    OAUTH_REDIRECT_URL: process.env.NEXT_PUBLIC_OAUTH_REDIRECT_URL,
    LINK_REDIRECT_URL: process.env.NEXT_PUBLIC_LINK_URL,
  },
  images: {
    domains: ['cdn.digiboxs.com', 'profile.line-scdn.net'],
  },
  compiler: {
    styledComponents: {
      ssr: true,
      displayName: true,
    },
  },
};

export default withNextVideo(nextConfig);
